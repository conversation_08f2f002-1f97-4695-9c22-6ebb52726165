const fs = require('fs');

// Simplified function to determine if text is user-facing
function isUserFacingText(text) {
  // Must be at least 3 characters
  if (text.length < 3) return false
  
  // Must not be too long
  if (text.length > 200) return false
  
  // Must contain letters
  if (!/[a-zA-Z]/.test(text)) return false
  
  // Filter out obvious technical content
  const technicalTerms = [
    'className', 'src=', 'alt=', 'href=', 'id=', 'data-', 'process.env', 'NEXT_PUBLIC',
    'useState', 'useEffect', 'onClick', 'onChange', 'onSubmit', 'target=', 'rel=', 'type=',
    'value=', 'defaultValue=', 'placeholder=', 'aria-', 'role=', 'tabIndex=', 'disabled=',
    'required=', 'readOnly=', 'autoFocus=', 'autoComplete=', 'maxLength=', 'minLength=',
    'pattern=', 'title=', 'name=', 'form=', 'list=', 'dir=', 'lang=', 'spellCheck=',
    'contentEditable=', 'suppressContentEditableWarning=', 'suppressHydrationWarning=',
    'itemScope=', 'itemType=', 'itemProp=', 'itemRef=', 'itemID=', 'dangerouslySetInnerHTML=',
    '__html', '__dangerouslySetInnerHTML', '__self', '__source', '__proto__',
    'constructor', 'prototype', 'toString', 'valueOf', 'hasOwnProperty', 'isPrototypeOf',
    'propertyIsEnumerable', 'toLocaleString', 'toLocaleDateString', 'toLocaleTimeString',
    'toLocaleLowerCase', 'toLocaleUpperCase', 'toFixed', 'toExponential', 'toPrecision',
    'charAt', 'charCodeAt', 'indexOf', 'lastIndexOf', 'localeCompare', 'match', 'replace',
    'search', 'slice', 'split', 'substring', 'substr', 'toLowerCase', 'toUpperCase',
    'trim', 'trimLeft', 'trimRight', 'concat', 'join', 'pop', 'push', 'reverse', 'shift',
    'unshift', 'splice', 'sort', 'filter', 'map', 'reduce', 'reduceRight', 'every', 'some',
    'forEach', 'find', 'findIndex', 'includes', 'keys', 'values', 'entries', 'fromEntries',
    'assign', 'create', 'defineProperty', 'defineProperties', 'getOwnPropertyDescriptor',
    'getOwnPropertyNames', 'getOwnPropertySymbols', 'getPrototypeOf', 'isExtensible',
    'isFrozen', 'isSealed', 'preventExtensions', 'seal', 'freeze', 'setPrototypeOf',
    'delete', 'instanceof', 'typeof', 'void', 'new', 'this', 'super', 'extends',
    'implements', 'interface', 'type', 'enum', 'const', 'let', 'var', 'function', 'class',
    'return', 'break', 'continue', 'throw', 'catch', 'finally', 'else',
    'switch', 'case', 'default', 'while', 'do', 'of', 'with', 'debugger', 'export',
    'import', 'from', 'as', 'default', 'namespace', 'module', 'require', 'module.exports',
    'exports.', '__dirname', '__filename', 'process', 'global', 'window', 'document',
    'console', 'setTimeout', 'setInterval', 'clearTimeout', 'clearInterval',
    'requestAnimationFrame', 'cancelAnimationFrame', 'Promise', 'resolve', 'reject',
    'then', 'catch', 'finally', 'async', 'await', 'yield', 'generator', 'Symbol', 'Map',
    'Set', 'WeakMap', 'WeakSet', 'Proxy', 'Reflect', 'Intl', 'JSON', 'Math', 'Date',
    'RegExp', 'Error', 'EvalError', 'RangeError', 'ReferenceError', 'SyntaxError',
    'TypeError', 'URIError', 'Array', 'Object', 'String', 'Number', 'Boolean', 'Function',
    'undefined', 'null', 'NaN', 'Infinity', 'true', 'false'
  ]
  
  // Check if text contains any technical terms
  if (technicalTerms.some(term => text.includes(term))) {
    return false
  }
  
  // Filter out URLs, paths, and technical patterns
  if (text.startsWith('/') || 
      text.startsWith('http') || 
      text.startsWith('mailto:') ||
      text.startsWith('tel:') ||
      text.includes('{') || text.includes('}') || // Template literals
      text.includes('$') // Template literals
  ) {
    return false
  }
  
  return true
}

// Function to extract text content from source code
function extractTextContent(sourceCode, filePath) {
  const texts = []
  const lines = sourceCode.split('\n')
  const seenTexts = new Set()

  // --- Universal getContent() fallback extraction (entire file) ---
  const getContentGlobalRegex = /getContent\(([^)]*)\)/g
  let match
  while ((match = getContentGlobalRegex.exec(sourceCode)) !== null) {
    // Find all string arguments
    const stringArgs = [...match[1].matchAll(/['"`]([^'"`]+)['"`]/g)].map(m => m[1])
    if (stringArgs.length > 0) {
      const text = stringArgs[stringArgs.length - 1]
      if (isUserFacingText(text) && !seenTexts.has(text)) {
        seenTexts.add(text)
        // Find the line number for this match
        const before = sourceCode.slice(0, match.index)
        const line = before.split('\n').length
        texts.push({
          id: `${filePath}-getcontent-global-${line}`,
          text,
          line,
          description: `Content fallback: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`,
          filePath
        })
      }
    }
  }

  // --- Line-by-line extraction for other patterns ---
  lines.forEach((line, lineNumber) => {
    // Skip comments, imports, exports, and technical lines
    if (line.trim().startsWith('//') || 
        line.trim().startsWith('import') || 
        line.trim().startsWith('export') ||
        line.trim().startsWith('/*') ||
        line.trim().startsWith('*') ||
        line.includes('useState') ||
        line.includes('useEffect') ||
        line.includes('className') ||
        line.includes('src=') ||
        line.includes('href=') ||
        line.includes('process.env') ||
        line.includes('NEXT_PUBLIC')) {
      return
    }

    // Look for JSX text content (text between tags) - this is what users see
    const jsxTextMatches = line.match(/>([^<>{}\n]{3,})</g)
    if (jsxTextMatches) {
      jsxTextMatches.forEach((match, index) => {
        const text = match.replace(/[><]/g, '').trim()
        if (isUserFacingText(text) && !seenTexts.has(text)) {
          seenTexts.add(text)
          texts.push({
            id: `${filePath}-jsx-${lineNumber + 1}-${index}`,
            text,
            line: lineNumber + 1,
            description: `Display text: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`,
            filePath
          })
        }
      })
    }

    // Look for static data arrays that contain user-facing content
    const staticDataMatches = line.match(/(name|title|description|value):\s*['"`]([^'"`]{3,})['"`]/g)
    if (staticDataMatches) {
      staticDataMatches.forEach((match, index) => {
        const parts = match.match(/(name|title|description|value):\s*['"`]([^'"`]+)['"`]/)
        if (parts && parts[2]) {
          const text = parts[2]
          if (isUserFacingText(text) && !seenTexts.has(text)) {
            seenTexts.add(text)
            texts.push({
              id: `${filePath}-static-${lineNumber + 1}-${index}`,
              text,
              line: lineNumber + 1,
              description: `Static ${parts[1]}: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`,
              filePath
            })
          }
        }
      })
    }

    // Look for array items (like features, pricing info)
    const arrayItemMatches = line.match(/^\s*['"`]([^'"`]{3,})['"`],?\s*$/g)
    if (arrayItemMatches) {
      arrayItemMatches.forEach((match, index) => {
        const parts = match.match(/['"`]([^'"`]+)['"`]/)
        if (parts && parts[1]) {
          const text = parts[1]
          if (isUserFacingText(text) && !seenTexts.has(text)) {
            seenTexts.add(text)
            texts.push({
              id: `${filePath}-array-${lineNumber + 1}-${index}`,
              text,
              line: lineNumber + 1,
              description: `List item: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`,
              filePath
            })
          }
        }
      })
    }
  })

  return texts
}

// Test with the about page
const sourceCode = fs.readFileSync('src/app/about/page.tsx', 'utf-8')
const texts = extractTextContent(sourceCode, 'src/app/about/page.tsx')

console.log(`Extracted ${texts.length} user-facing texts from about page:`)
texts.forEach((text, index) => {
  console.log(`${index + 1}. ${text.description}`)
  console.log(`   Text: "${text.text}"`)
  console.log(`   Line: ${text.line}`)
  console.log('')
}) 