#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Homepage Image Assets...\n');

// Check if all required images exist
const requiredImages = [
  'public/images/hero-bg.svg',
  'public/images/clients/microsoft.svg',
  'public/images/clients/google.svg',
  'public/images/clients/amazon.svg',
  'public/images/clients/apple.svg',
  'public/images/clients/meta.svg',
  'public/images/clients/netflix.svg',
  'public/images/og-homepage.svg'
];

let allImagesExist = true;
let missingImages = [];

console.log('📋 Checking required images:\n');

requiredImages.forEach((imagePath, index) => {
  const exists = fs.existsSync(imagePath);
  const status = exists ? '✅' : '❌';
  const size = exists ? `(${Math.round(fs.statSync(imagePath).size / 1024)}KB)` : '(missing)';
  
  console.log(`${index + 1}. ${status} ${imagePath} ${size}`);
  
  if (!exists) {
    allImagesExist = false;
    missingImages.push(imagePath);
  }
});

console.log(`\n📊 Results:`);
console.log(`✅ Found: ${requiredImages.length - missingImages.length}/${requiredImages.length} images`);

if (missingImages.length > 0) {
  console.log(`❌ Missing: ${missingImages.length} images`);
  console.log(`\n🔧 To fix missing images, run:`);
  console.log(`node scripts/create-placeholder-images.js`);
  allImagesExist = false;
} else {
  console.log(`🎉 All required images are present!`);
}

// Check homepage component references
console.log(`\n🔍 Checking homepage component references:`);

const checkFileReferences = (filePath, expectedRefs) => {
  if (!fs.existsSync(filePath)) {
    console.log(`❌ File not found: ${filePath}`);
    return false;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  let allRefsFound = true;
  
  expectedRefs.forEach(ref => {
    if (content.includes(ref)) {
      console.log(`✅ ${path.basename(filePath)}: Found reference to ${ref}`);
    } else {
      console.log(`❌ ${path.basename(filePath)}: Missing reference to ${ref}`);
      allRefsFound = false;
    }
  });
  
  return allRefsFound;
};

const heroRefsOk = checkFileReferences('src/components/home/<USER>', [
  '/images/hero-bg.svg'
]);

const pageRefsOk = checkFileReferences('src/app/page.tsx', [
  '/images/clients/microsoft.svg',
  '/images/clients/google.svg',
  '/images/og-homepage.svg'
]);

console.log(`\n🎯 Component Status:`);
console.log(`${heroRefsOk ? '✅' : '❌'} Hero Section: Image references`);
console.log(`${pageRefsOk ? '✅' : '❌'} Main Page: Client logos and OG image`);

// Overall status
const overallStatus = allImagesExist && heroRefsOk && pageRefsOk;

console.log(`\n🏆 Overall Status: ${overallStatus ? '✅ READY' : '❌ NEEDS ATTENTION'}`);

if (overallStatus) {
  console.log(`\n🚀 Your homepage is ready! No more 404 image errors.`);
  console.log(`\n📝 Next steps:`);
  console.log(`1. Run: npm run dev`);
  console.log(`2. Visit: http://localhost:3000`);
  console.log(`3. Verify images load correctly`);
  console.log(`4. Replace SVG placeholders with optimized images when ready`);
} else {
  console.log(`\n🔧 Action needed:`);
  if (!allImagesExist) {
    console.log(`- Run: node scripts/create-placeholder-images.js`);
  }
  if (!heroRefsOk || !pageRefsOk) {
    console.log(`- Check component file references`);
  }
}

console.log(`\n📚 Documentation:`);
console.log(`- Image optimization guide: IMAGE-OPTIMIZATION-GUIDE.md`);
console.log(`- Homepage optimization: HOMEPAGE-OPTIMIZATION-GUIDE.md`);

process.exit(overallStatus ? 0 : 1);
