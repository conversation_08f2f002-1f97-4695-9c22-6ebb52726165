#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🖼️  Creating placeholder images for optimized homepage...\n');

// Create SVG placeholder images
const createSVGPlaceholder = (width, height, text, bgColor = '#f3f4f6', textColor = '#6b7280') => {
  return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="${bgColor}"/>
  <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="${textColor}" text-anchor="middle" dominant-baseline="middle">${text}</text>
</svg>`;
};

// Create gradient hero background
const createHeroBackground = () => {
  return `<svg width="1920" height="1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    <pattern id="dots" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#heroGradient)"/>
  <rect width="100%" height="100%" fill="url(#dots)"/>
  <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="48" fill="white" text-anchor="middle" dominant-baseline="middle" opacity="0.8">Hero Background</text>
</svg>`;
};

// Image configurations
const images = [
  {
    path: 'public/images/hero-bg.webp',
    content: createHeroBackground(),
    description: 'Hero background image (1920x1080)'
  },
  {
    path: 'public/images/clients/microsoft.webp',
    content: createSVGPlaceholder(120, 60, 'Microsoft', '#0078d4', 'white'),
    description: 'Microsoft logo placeholder'
  },
  {
    path: 'public/images/clients/google.webp',
    content: createSVGPlaceholder(120, 60, 'Google', '#4285f4', 'white'),
    description: 'Google logo placeholder'
  },
  {
    path: 'public/images/clients/amazon.webp',
    content: createSVGPlaceholder(120, 60, 'Amazon', '#ff9900', 'white'),
    description: 'Amazon logo placeholder'
  },
  {
    path: 'public/images/clients/apple.webp',
    content: createSVGPlaceholder(120, 60, 'Apple', '#000000', 'white'),
    description: 'Apple logo placeholder'
  },
  {
    path: 'public/images/clients/meta.webp',
    content: createSVGPlaceholder(120, 60, 'Meta', '#1877f2', 'white'),
    description: 'Meta logo placeholder'
  },
  {
    path: 'public/images/clients/netflix.webp',
    content: createSVGPlaceholder(120, 60, 'Netflix', '#e50914', 'white'),
    description: 'Netflix logo placeholder'
  },
  {
    path: 'public/images/og-homepage.jpg',
    content: createSVGPlaceholder(1200, 630, 'Technoloway - Software Development', '#1e40af', 'white'),
    description: 'Open Graph image for social sharing'
  }
];

// Create placeholder images
let created = 0;
let errors = 0;

images.forEach(({ path: imagePath, content, description }) => {
  try {
    // Ensure directory exists
    const dir = path.dirname(imagePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Write SVG content (we'll use SVG as placeholder since it's lightweight)
    const svgPath = imagePath.replace(/\.(webp|jpg|png)$/, '.svg');
    fs.writeFileSync(svgPath, content);
    
    console.log(`✅ Created: ${svgPath}`);
    console.log(`   ${description}`);
    created++;
  } catch (error) {
    console.error(`❌ Failed to create ${imagePath}:`, error.message);
    errors++;
  }
});

console.log(`\n📊 Summary:`);
console.log(`✅ Created: ${created} placeholder images`);
if (errors > 0) {
  console.log(`❌ Errors: ${errors}`);
}

console.log(`\n🔧 Next Steps:`);
console.log(`1. The placeholders are created as SVG files for now`);
console.log(`2. Replace them with optimized WebP/AVIF images for production`);
console.log(`3. Use tools like Sharp, Squoosh, or online converters`);
console.log(`4. Recommended sizes:`);
console.log(`   - Hero background: 1920x1080px (WebP, ~200KB)`);
console.log(`   - Client logos: 120x60px (WebP, ~5KB each)`);
console.log(`   - OG image: 1200x630px (JPG, ~100KB)`);

console.log(`\n🎨 For real images:`);
console.log(`- Hero: Use a technology/coding themed background`);
console.log(`- Logos: Download official brand assets from company websites`);
console.log(`- Optimize with: https://squoosh.app/ or Sharp CLI`);

console.log(`\n✨ The homepage should now load without 404 errors!`);
