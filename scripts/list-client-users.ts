import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function listClientUsers() {
  try {
    console.log('🔍 Finding CLIENT users and their linked client records...\n')
    
    // Get all CLIENT role users
    const clientUsers = await prisma.users.findMany({
      where: {
        role: 'CLIENT'
      },
      select: {
        id: true,
        email: true,
        firstname: true,
        lastname: true,
        role: true,
        isactive: true,
        clients: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true,
            isactive: true
          }
        }
      },
      orderBy: {
        email: 'asc'
      }
    })

    console.log(`Found ${clientUsers.length} CLIENT users:\n`)
    
    clientUsers.forEach((user, index) => {
      console.log(`${index + 1}. User ID: ${user.id}`)
      console.log(`   Email: ${user.email}`)
      console.log(`   Name: ${user.firstname} ${user.lastname}`)
      console.log(`   Role: ${user.role}`)
      console.log(`   Active: ${user.isactive}`)
      console.log(`   Password: password123 (all users have same password)`)
      
      if (user.clients && user.clients.length > 0) {
        console.log(`   ✅ Linked to ${user.clients.length} client record(s):`)
        user.clients.forEach((client, clientIndex) => {
          console.log(`      ${clientIndex + 1}. Client ID: ${client.id}`)
          console.log(`         Company: ${client.companyname}`)
          console.log(`         Contact: ${client.contactname}`)
          console.log(`         Email: ${client.contactemail}`)
          console.log(`         Active: ${client.isactive}`)
        })
      } else {
        console.log(`   ❌ No client records linked`)
      }
      console.log('')
    })

    // Also check if there are any clients linked to CLIENT users
    const clientsWithUsers = await prisma.clients.findMany({
      where: {
        userid: {
          not: null
        },
        users: {
          role: 'CLIENT'
        }
      },
      include: {
        users: {
          select: {
            id: true,
            email: true,
            firstname: true,
            lastname: true,
            role: true
          }
        }
      }
    })

    console.log(`\n📊 Summary:`)
    console.log(`   Total CLIENT users: ${clientUsers.length}`)
    console.log(`   CLIENT users with linked client records: ${clientUsers.filter(u => u.clients.length > 0).length}`)
    console.log(`   Client records linked to CLIENT users: ${clientsWithUsers.length}`)
    
    if (clientsWithUsers.length > 0) {
      console.log(`\n🔗 Client records linked to CLIENT users:`)
      clientsWithUsers.forEach((client, index) => {
        console.log(`   ${index + 1}. ${client.companyname} (ID: ${client.id}) → User: ${client.users?.email}`)
      })
    }

  } catch (error) {
    console.error('Error listing client users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

listClientUsers()
