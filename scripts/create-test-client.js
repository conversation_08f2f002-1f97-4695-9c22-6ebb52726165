const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})

async function createTestClient() {
  try {
    // Test client credentials
    const email = '<EMAIL>'
    const password = 'TestClient123'
    const firstName = 'John'
    const lastName = 'Doe'
    const companyName = 'Test Company Inc.'

    // Check if user already exists
    const existingUser = await prisma.users.findUnique({
      where: { email }
    })

    if (existingUser) {
      console.log('Test client already exists!')
      console.log('Email:', email)
      console.log('Password:', password)
      return
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Create user
    const user = await prisma.users.create({
      data: {
        firstname: firstName,
        lastname: lastName,
        email,
        password: hashedPassword,
        role: 'CLIENT',
        emailverified: new Date(),
        isactive: true
      }
    })

    // Create client record
    await prisma.clients.create({
      data: {
        companyname: companyName,
        contactname: `${firstName} ${lastName}`,
        contactemail: email,
        contactphone: '+****************',
        address: '123 Test Street',
        city: 'Test City',
        state: 'Test State',
        zipcode: '12345',
        country: 'United States',
        isactive: true
      }
    })

    console.log('✅ Test client created successfully!')
    console.log('📧 Email:', email)
    console.log('🔑 Password:', password)
    console.log('🏢 Company:', companyName)
    console.log('👤 Name:', `${firstName} ${lastName}`)
    console.log('')
    console.log('You can now use these credentials to test the client login page.')

  } catch (error) {
    console.error('❌ Error creating test client:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestClient()
