{"dashboard_specification": {"title": "Client Dashboard - Feedback Board Style", "description": "A modern client dashboard inspired by Frontend Mentor feedback board design with gradient backgrounds, clean cards, and interactive elements", "design_system": {"colors": {"primary_gradient": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "secondary_gradient": "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)", "background": "#f7f8fd", "card_background": "#ffffff", "text_primary": "#3a4374", "text_secondary": "#647196", "text_muted": "#8c92b3", "accent_blue": "#4661e6", "accent_purple": "#ad1fea", "accent_orange": "#f49f85", "accent_light_blue": "#62bcfa", "border_color": "#e1e5f2", "hover_background": "#f2f4ff"}, "typography": {"font_family": "'Jost', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif", "font_weights": {"regular": 400, "medium": 500, "semibold": 600, "bold": 700}, "font_sizes": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "1.875rem"}}, "spacing": {"xs": "0.25rem", "sm": "0.5rem", "md": "1rem", "lg": "1.5rem", "xl": "2rem", "2xl": "3rem"}, "border_radius": {"sm": "0.375rem", "md": "0.5rem", "lg": "0.75rem", "xl": "1rem"}}, "layout": {"container": {"max_width": "1200px", "padding": "1.5rem", "margin": "0 auto"}, "grid": {"desktop": "300px 1fr", "tablet": "1fr", "mobile": "1fr", "gap": "2rem"}}, "components": {"header_card": {"type": "gradient_card", "background": "primary_gradient", "padding": "2rem", "border_radius": "lg", "text_color": "white", "elements": {"title": {"text": "Client Dashboard", "font_size": "2xl", "font_weight": "bold", "margin_bottom": "sm"}, "subtitle": {"text": "Project Management Hub", "font_size": "base", "opacity": 0.9}}}, "navigation_tabs": {"type": "tab_group", "background": "card_background", "border_radius": "lg", "padding": "sm", "margin_bottom": "lg", "tabs": [{"id": "all", "label": "All", "active": true, "background": "accent_blue", "text_color": "white"}, {"id": "projects", "label": "Projects", "background": "transparent", "text_color": "text_secondary"}, {"id": "invoices", "label": "Invoices", "background": "transparent", "text_color": "text_secondary"}, {"id": "payments", "label": "Payments", "background": "transparent", "text_color": "text_secondary"}]}, "filter_tags": {"type": "tag_group", "margin_bottom": "lg", "tags": [{"label": "Active Projects", "background": "accent_blue", "text_color": "white"}, {"label": "Pending Invoices", "background": "accent_purple", "text_color": "white"}, {"label": "Completed", "background": "accent_orange", "text_color": "white"}]}, "roadmap_sidebar": {"type": "sidebar_card", "background": "card_background", "border_radius": "lg", "padding": "lg", "elements": {"header": {"title": "Project Status", "action_button": {"text": "View Details", "style": "link", "color": "accent_blue"}}, "status_items": [{"label": "In Progress", "count": 3, "color": "accent_orange", "indicator": "dot"}, {"label": "Under Review", "count": 2, "color": "accent_purple", "indicator": "dot"}, {"label": "Completed", "count": 1, "color": "accent_blue", "indicator": "dot"}]}}, "main_content_header": {"type": "content_header", "background": "text_primary", "color": "white", "border_radius": "lg", "padding": "lg", "elements": {"icon": {"type": "lightbulb", "size": "lg"}, "title": {"text": "6 Active Items", "font_size": "lg", "font_weight": "semibold"}, "sort_dropdown": {"label": "Sort by: Most Recent", "options": ["Most Recent", "Oldest", "Priority", "Status"]}, "action_button": {"text": "+ Add New", "background": "secondary_gradient", "text_color": "white", "border_radius": "md", "padding": "sm lg"}}}, "feedback_card": {"type": "interactive_card", "background": "card_background", "border_radius": "lg", "padding": "lg", "margin_bottom": "md", "hover_effect": {"transform": "translateY(-2px)", "shadow": "0 8px 25px rgba(70, 97, 230, 0.15)"}, "elements": {"vote_section": {"type": "vote_counter", "background": "hover_background", "border_radius": "lg", "padding": "sm", "text_align": "center", "elements": {"arrow_up": {"type": "icon", "color": "accent_blue"}, "vote_count": {"font_size": "sm", "font_weight": "bold", "color": "text_primary"}}}, "content_section": {"flex": 1, "padding_left": "lg", "elements": {"title": {"font_size": "lg", "font_weight": "semibold", "color": "text_primary", "margin_bottom": "sm", "hover_color": "accent_blue", "cursor": "pointer"}, "description": {"font_size": "base", "color": "text_secondary", "line_height": 1.6, "margin_bottom": "md"}, "category_tag": {"type": "tag", "padding": "xs sm", "border_radius": "xl", "font_size": "sm", "font_weight": "medium", "variants": {"enhancement": {"background": "rgba(70, 97, 230, 0.1)", "color": "accent_blue"}, "feature": {"background": "rgba(173, 31, 234, 0.1)", "color": "accent_purple"}, "bug": {"background": "rgba(244, 159, 133, 0.1)", "color": "accent_orange"}}}}}, "comment_section": {"type": "comment_indicator", "elements": {"icon": {"type": "chat_bubble", "color": "text_muted"}, "count": {"font_size": "sm", "font_weight": "medium", "color": "text_primary", "margin_left": "xs"}}}}}, "detail_view": {"type": "modal_overlay", "background": "rgba(0, 0, 0, 0.5)", "elements": {"modal_content": {"background": "card_background", "border_radius": "xl", "max_width": "800px", "margin": "auto", "padding": "2xl", "elements": {"header": {"elements": {"back_button": {"text": "← Go Back", "color": "text_secondary", "font_size": "sm", "margin_bottom": "lg"}, "edit_button": {"text": "<PERSON>", "background": "accent_blue", "color": "white", "border_radius": "md", "padding": "sm lg", "float": "right"}}}, "main_content": {"elements": {"vote_counter": {"type": "horizontal_vote", "background": "hover_background", "border_radius": "lg", "padding": "sm lg", "display": "inline-flex", "align_items": "center", "margin_bottom": "lg"}, "title": {"font_size": "2xl", "font_weight": "bold", "color": "text_primary", "margin_bottom": "sm"}, "description": {"font_size": "base", "color": "text_secondary", "line_height": 1.6, "margin_bottom": "lg"}, "category_tag": {"type": "tag", "margin_bottom": "xl"}}}, "comments_section": {"elements": {"comments_header": {"text": "4 Comments", "font_size": "lg", "font_weight": "bold", "color": "text_primary", "margin_bottom": "lg"}, "comment_list": {"type": "comment_thread"}, "add_comment": {"type": "comment_form", "elements": {"title": {"text": "Add Comment", "font_size": "lg", "font_weight": "bold", "margin_bottom": "md"}, "textarea": {"placeholder": "Type your comment here", "border_radius": "md", "border": "1px solid border_color", "padding": "md", "min_height": "100px", "margin_bottom": "sm"}, "character_count": {"text": "250 Characters left", "font_size": "sm", "color": "text_muted", "margin_bottom": "md"}, "submit_button": {"text": "Post Comment", "background": "secondary_gradient", "color": "white", "border_radius": "md", "padding": "sm xl", "font_weight": "medium"}}}}}}}}}, "comment_item": {"type": "comment_card", "padding": "lg", "border_bottom": "1px solid border_color", "elements": {"avatar": {"type": "user_avatar", "size": "40px", "border_radius": "50%", "float": "left", "margin_right": "md"}, "content": {"elements": {"user_info": {"elements": {"name": {"font_weight": "semibold", "color": "text_primary", "font_size": "sm"}, "username": {"color": "text_muted", "font_size": "sm", "margin_left": "xs"}, "reply_button": {"text": "Reply", "color": "accent_blue", "font_size": "sm", "font_weight": "medium", "float": "right", "cursor": "pointer"}}}, "comment_text": {"color": "text_secondary", "line_height": 1.6, "margin_top": "sm"}, "mentions": {"color": "accent_purple", "font_weight": "medium"}}}}}}, "responsive_design": {"breakpoints": {"mobile": "320px", "tablet": "768px", "desktop": "1024px", "large": "1200px"}, "mobile_adaptations": {"layout": {"grid": "1fr", "sidebar": "hidden", "padding": "1rem"}, "header_card": {"padding": "1.5rem", "font_size": "xl"}, "navigation_tabs": {"scroll": "horizontal", "padding": "xs"}, "feedback_card": {"padding": "md", "vote_section": {"position": "bottom", "orientation": "horizontal"}}}, "tablet_adaptations": {"layout": {"grid": "1fr", "sidebar": "top_section"}, "roadmap_sidebar": {"orientation": "horizontal", "items": "inline"}}}, "interactions": {"hover_effects": {"cards": {"transform": "translateY(-2px)", "shadow": "0 8px 25px rgba(0, 0, 0, 0.1)", "transition": "all 0.2s ease"}, "buttons": {"transform": "translateY(-1px)", "shadow": "0 4px 12px rgba(70, 97, 230, 0.3)", "transition": "all 0.2s ease"}, "tags": {"opacity": 0.8, "transition": "opacity 0.2s ease"}}, "click_effects": {"vote_button": {"animation": "pulse", "color_change": "accent_blue", "feedback": "haptic"}, "card_click": {"action": "navigate_to_detail", "animation": "fade_in"}}, "loading_states": {"skeleton_cards": {"background": "linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)", "animation": "shimmer 1.5s infinite"}, "button_loading": {"spinner": true, "disabled": true, "opacity": 0.7}}}, "data_structure": {"dashboard_items": [{"id": 1, "type": "project", "title": "Website Redesign Project", "description": "Complete overhaul of company website with modern design and improved UX", "category": "enhancement", "votes": 112, "comments": 2, "status": "in_progress", "priority": "high", "created_at": "2024-01-15", "updated_at": "2024-01-20"}, {"id": 2, "type": "invoice", "title": "Monthly Development Invoice", "description": "Invoice for development services rendered in January 2024", "category": "feature", "votes": 99, "comments": 4, "status": "pending", "amount": "$5,500", "due_date": "2024-02-15"}, {"id": 3, "type": "support", "title": "Technical Support Request", "description": "Need assistance with API integration and documentation", "category": "bug", "votes": 65, "comments": 2, "status": "open", "priority": "medium"}], "user_profile": {"name": "<PERSON>", "company": "Tech Solutions Inc", "avatar": "/avatars/john-doe.jpg", "role": "Project Manager", "permissions": ["view_projects", "create_feedback", "manage_invoices"]}}}}