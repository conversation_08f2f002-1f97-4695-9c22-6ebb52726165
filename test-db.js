// Simple test to check database connection
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testConnection() {
  try {
    console.log('Testing database connection...')
    
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Database connected successfully')
    
    // Test invoices table
    const invoiceCount = await prisma.invoices.count()
    console.log(`📊 Found ${invoiceCount} invoices in database`)
    
    // Test clients table
    const clientCount = await prisma.clients.count()
    console.log(`👥 Found ${clientCount} clients in database`)
    
    // Test contracts table
    const contractCount = await prisma.contracts.count()
    console.log(`📄 Found ${contractCount} contracts in database`)
    
    // Test orders table
    const orderCount = await prisma.orders.count()
    console.log(`📦 Found ${orderCount} orders in database`)
    
    // Get a sample invoice if any exist
    if (invoiceCount > 0) {
      const sampleInvoice = await prisma.invoices.findFirst({
        include: {
          clients: true,
          contracts: true,
          orders: true,
          projects: true
        }
      })
      console.log('📋 Sample invoice:', JSON.stringify(sampleInvoice, null, 2))
    }
    
  } catch (error) {
    console.error('❌ Database connection failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testConnection()
