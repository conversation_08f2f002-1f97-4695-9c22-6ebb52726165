import { NextResponse } from 'next/server'

/**
 * Sanitize HTML content to prevent XSS attacks
 */
export function sanitizeHtml(input: string): string {
  if (!input) return ''
  
  // Basic HTML entity encoding
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
}

/**
 * Sanitize text input by removing potentially dangerous characters
 */
export function sanitizeText(input: string): string {
  if (!input) return ''
  
  // Remove null bytes and control characters except newlines and tabs
  return input.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
}

/**
 * Validate and sanitize email addresses
 */
export function sanitizeEmail(email: string): string {
  if (!email) return ''
  
  // Basic email sanitization - remove dangerous characters
  return email
    .toLowerCase()
    .trim()
    .replace(/[<>]/g, '')
}

/**
 * Validate phone number format
 */
export function sanitizePhone(phone: string): string {
  if (!phone) return ''
  
  // Remove all non-digit characters except +, -, (, ), and spaces
  return phone.replace(/[^\d\+\-\(\)\s]/g, '').trim()
}

/**
 * Sanitize contact form data
 */
export function sanitizeContactFormData(data: any) {
  return {
    name: sanitizeText(data.name || ''),
    email: sanitizeEmail(data.email || ''),
    phone: sanitizePhone(data.phone || ''),
    subject: sanitizeText(data.subject || ''),
    message: sanitizeText(data.message || ''),
  }
}

/**
 * Add security headers to response
 */
export function addSecurityHeaders(response: NextResponse): NextResponse {
  // Prevent XSS attacks
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  
  // Content Security Policy for API responses
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'none'; frame-ancestors 'none';"
  )
  
  return response
}

/**
 * Validate request origin for CSRF protection
 */
export function validateOrigin(request: Request, allowedOrigins: string[]): boolean {
  const origin = request.headers.get('origin')
  const referer = request.headers.get('referer')
  
  // For same-origin requests, origin might be null
  if (!origin && !referer) {
    return false
  }
  
  const requestOrigin = origin || (referer ? new URL(referer).origin : '')
  
  return allowedOrigins.some(allowed => {
    if (allowed === '*') return true
    return requestOrigin === allowed
  })
}

/**
 * Check for suspicious patterns in input
 */
export function detectSuspiciousContent(text: string): boolean {
  if (!text) return false
  
  const suspiciousPatterns = [
    // SQL injection patterns
    /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b)/i,
    // Script injection patterns
    /<script[^>]*>/i,
    /<\/script>/i,
    // Common XSS patterns
    /javascript:/i,
    /on\w+\s*=/i,
    // HTML tags that could be dangerous
    /<[^>]*>/,
    // Excessive special characters (potential obfuscation)
    /[<>'"&%]{5,}/,
    // Excessive length (potential DoS)
    /.{10000,}/
  ]
  
  return suspiciousPatterns.some(pattern => pattern.test(text))
}

/**
 * Validate contact form submission for security
 */
export function validateContactFormSecurity(data: any): { valid: boolean; reason?: string } {
  const fields = ['name', 'email', 'subject', 'message']
  
  for (const field of fields) {
    const value = data[field]
    if (value && detectSuspiciousContent(value)) {
      return {
        valid: false,
        reason: `Suspicious content detected in ${field}`
      }
    }
  }
  
  // Check for honeypot fields (common bot detection)
  if (data.website || data.url || data.link) {
    return {
      valid: false,
      reason: 'Bot detected'
    }
  }
  
  // Check for excessive length
  if (data.message && data.message.length > 5000) {
    return {
      valid: false,
      reason: 'Message too long'
    }
  }
  
  return { valid: true }
}

/**
 * Generate a simple CSRF token (for basic protection)
 */
export function generateCSRFToken(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

/**
 * Validate CSRF token
 */
export function validateCSRFToken(token: string, sessionToken: string): boolean {
  return token === sessionToken
}

/**
 * Rate limit key generator for different types of requests
 */
export function generateRateLimitKey(request: Request, type: string): string {
  const ip = getClientIP(request)
  const userAgent = request.headers.get('user-agent')?.slice(0, 50) || 'unknown'
  
  return `${type}:${ip}:${userAgent}`
}

/**
 * Extract client IP address from request
 */
export function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const cfConnectingIp = request.headers.get('cf-connecting-ip')
  
  return (
    cfConnectingIp ||
    (forwarded ? forwarded.split(',')[0].trim() : null) ||
    realIp ||
    'unknown'
  )
}

/**
 * Log security events
 */
export function logSecurityEvent(event: {
  type: string
  ip: string
  userAgent?: string
  details?: any
  severity: 'low' | 'medium' | 'high'
}) {
  console.warn(`[SECURITY] ${event.type}`, {
    timestamp: new Date().toISOString(),
    ip: event.ip,
    userAgent: event.userAgent,
    details: event.details,
    severity: event.severity
  })
  
  // In production, you might want to send this to a security monitoring service
}
