/**
 * Safe date formatting utilities to prevent "Invalid time value" errors
 */

/**
 * Safely formats a date to ISO string
 * @param dateValue - Date string, Date object, or null/undefined
 * @returns ISO string or empty string if invalid
 */
export function safeToISOString(dateValue: any): string {
  if (!dateValue) return ''
  
  try {
    const date = dateValue instanceof Date ? dateValue : new Date(dateValue)
    return isNaN(date.getTime()) ? '' : date.toISOString()
  } catch {
    return ''
  }
}

/**
 * Safely formats a date to ISO string and slices for datetime-local input
 * @param dateValue - Date string, Date object, or null/undefined
 * @returns ISO string sliced to 16 characters (YYYY-MM-DDTHH:mm) or empty string if invalid
 */
export function safeToDateTimeLocal(dateValue: any): string {
  const isoString = safeToISOString(dateValue)
  return isoString ? isoString.slice(0, 16) : ''
}

/**
 * Safely formats a date to date string for date input
 * @param dateValue - Date string, Date object, or null/undefined
 * @returns Date string (YYYY-MM-DD) or empty string if invalid
 */
export function safeToDateString(dateValue: any): string {
  const isoString = safeToISOString(dateValue)
  return isoString ? isoString.split('T')[0] : ''
}

/**
 * Safely formats a date to localized date string
 * @param dateValue - Date string, Date object, or null/undefined
 * @param options - Intl.DateTimeFormatOptions
 * @returns Formatted date string or 'Unknown' if invalid
 */
export function safeToLocaleDateString(
  dateValue: any, 
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }
): string {
  if (!dateValue) return 'Unknown'
  
  try {
    const date = dateValue instanceof Date ? dateValue : new Date(dateValue)
    return isNaN(date.getTime()) ? 'Unknown' : date.toLocaleDateString('en-US', options)
  } catch {
    return 'Unknown'
  }
}

/**
 * Safely creates a Date object
 * @param dateValue - Date string, Date object, or null/undefined
 * @returns Valid Date object or null if invalid
 */
export function safeCreateDate(dateValue: any): Date | null {
  if (!dateValue) return null
  
  try {
    const date = dateValue instanceof Date ? dateValue : new Date(dateValue)
    return isNaN(date.getTime()) ? null : date
  } catch {
    return null
  }
}

/**
 * Checks if a date value is valid
 * @param dateValue - Date string, Date object, or null/undefined
 * @returns true if valid date, false otherwise
 */
export function isValidDate(dateValue: any): boolean {
  if (!dateValue) return false
  
  try {
    const date = dateValue instanceof Date ? dateValue : new Date(dateValue)
    return !isNaN(date.getTime())
  } catch {
    return false
  }
}

/**
 * Safely formats a date with fallback
 * @param dateValue - Date string, Date object, or null/undefined
 * @param fallback - Fallback value if date is invalid
 * @returns Formatted date or fallback
 */
export function safeDateWithFallback(dateValue: any, fallback: string = 'Unknown'): string {
  const formatted = safeToLocaleDateString(dateValue)
  return formatted === 'Unknown' ? fallback : formatted
}

/**
 * Gets days until a date
 * @param dateValue - Date string, Date object, or null/undefined
 * @returns Number of days until date, or null if invalid
 */
export function getDaysUntil(dateValue: any): number | null {
  const date = safeCreateDate(dateValue)
  if (!date) return null
  
  const today = new Date()
  const diffTime = date.getTime() - today.getTime()
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

/**
 * Formats currency safely
 * @param amount - Number or string amount
 * @param currency - Currency code (default: USD)
 * @returns Formatted currency string
 */
export function safeFormatCurrency(amount: any, currency: string = 'USD'): string {
  try {
    const numAmount = typeof amount === 'number' ? amount : parseFloat(amount)
    if (isNaN(numAmount)) return '$0.00'
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(numAmount)
  } catch {
    return '$0.00'
  }
}
