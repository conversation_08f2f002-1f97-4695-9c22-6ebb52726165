import { prisma } from '@/lib/prisma'

export interface ProjectListItem {
  id: string | number
  name: string
  description: string
  status?: string
  projStartDate?: string
  projCompletionDate?: string
  estimateCost?: number
  estimateTime?: string
  imageUrl?: string
  projectUrl?: string
  githubUrl?: string
  tags?: string
  createdAt: string
  updatedAt?: string
}

export interface ProjectDetails extends ProjectListItem {
  projGoals?: string
  projManager?: {
    id: string | number
    name: string
    position: string
  }
  estimateEffort?: string
  isFeatured?: boolean
  isPublic?: boolean
  displayOrder: number
  technologies: Array<{
    id: string | number
    name: string
    iconClass?: string
  }>
  tasks: Array<{
    id: string | number
    taskDesc?: string
    taskStartDate?: string
    taskEndDate?: string
    workHours?: number
    status?: string
    teamMember: {
      id: string | number
      name: string
      position: string
    }
  }>
}

/**
 * Fetch projects for a specific client
 */
export async function getClientProjects(
  clientId: string | number,
  options: {
    page?: number
    limit?: number
    search?: string
    status?: string
  } = {}
): Promise<{ projects: ProjectListItem[]; total: number }> {
  try {
    const { page = 1, limit = 10, search, status } = options
    const skip = (page - 1) * limit

    const where: any = {
      clientid: Number(clientId),
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { tags: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (status) {
      where.status = status
    }

    const [projects, total] = await Promise.all([
      prisma.projects.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdat: 'desc',
        },
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          projstartdate: true,
          projcompletiondate: true,
          estimatecost: true,
          estimatetime: true,
          imageurl: true,
          projecturl: true,
          githuburl: true,
          tags: true,
          createdat: true,
          updatedat: true,
        },
      }),
      prisma.projects.count({ where }),
    ])

    return {
      projects: projects.map(project => ({
        id: project.id.toString(),
        name: project.name,
        description: project.description,
        status: project.status || undefined,
        projStartDate: project.projstartdate?.toISOString(),
        projCompletionDate: project.projcompletiondate?.toISOString(),
        estimateCost: project.estimatecost || undefined,
        estimateTime: project.estimatetime || undefined,
        imageUrl: project.imageurl || undefined,
        projectUrl: project.projecturl || undefined,
        githubUrl: project.githuburl || undefined,
        tags: project.tags || undefined,
        createdAt: project.createdat.toISOString(),
        updatedAt: project.updatedat?.toISOString(),
      })),
      total,
    }
  } catch (error) {
    console.error('Error fetching client projects:', error)
    throw new Error('Failed to fetch client projects')
  }
}

/**
 * Fetch detailed information for a specific project
 */
export async function getProjectDetails(projectId: string | number): Promise<ProjectDetails | null> {
  try {
    const project = await prisma.projects.findUnique({
      where: { id: Number(projectId) },
      include: {
        teammembers: {
          select: {
            id: true,
            name: true,
            position: true,
          },
        },
        projecttechnologies: {
          include: {
            technologies: {
              select: {
                id: true,
                name: true,
                iconclass: true,
              },
            },
          },
        },
        tasks: {
          include: {
            teammembers: {
              select: {
                id: true,
                name: true,
                position: true,
              },
            },
          },
          orderBy: {
            createdat: 'desc',
          },
        },
      },
    })

    if (!project) {
      return null
    }

    return {
      id: project.id.toString(),
      name: project.name,
      description: project.description,
      projGoals: project.projgoals || undefined,
      status: project.status || undefined,
      projStartDate: project.projstartdate?.toISOString(),
      projCompletionDate: project.projcompletiondate?.toISOString(),
      estimateCost: project.estimatecost || undefined,
      estimateTime: project.estimatetime || undefined,
      estimateEffort: project.estimateeffort || undefined,
      imageUrl: project.imageurl || undefined,
      projectUrl: project.projecturl || undefined,
      githubUrl: project.githuburl || undefined,
      tags: project.tags || undefined,
      isFeatured: project.isfeatured || undefined,
      isPublic: project.ispublic || undefined,
      displayOrder: project.displayorder,
      createdAt: project.createdat.toISOString(),
      updatedAt: project.updatedat?.toISOString(),
      projManager: project.teammembers ? {
        id: project.teammembers.id.toString(),
        name: project.teammembers.name,
        position: project.teammembers.position,
      } : undefined,
      technologies: project.projecttechnologies.map(pt => ({
        id: pt.technologies.id.toString(),
        name: pt.technologies.name,
        iconClass: pt.technologies.iconclass || undefined,
      })),
      tasks: project.tasks.map(task => ({
        id: task.id.toString(),
        taskDesc: task.taskdesc || undefined,
        taskStartDate: task.taskstartdate?.toISOString(),
        taskEndDate: task.taskenddate?.toISOString(),
        workHours: task.workhours || undefined,
        status: task.status || undefined,
        teamMember: {
          id: task.teammembers.id.toString(),
          name: task.teammembers.name,
          position: task.teammembers.position,
        },
      })),
    }
  } catch (error) {
    console.error('Error fetching project details:', error)
    throw new Error('Failed to fetch project details')
  }
}

/**
 * Get project status options
 */
export function getProjectStatusOptions() {
  return [
    { value: 'PLANNING', label: 'Planning' },
    { value: 'IN_PROGRESS', label: 'In Progress' },
    { value: 'COMPLETED', label: 'Completed' },
    { value: 'ON_HOLD', label: 'On Hold' },
    { value: 'CANCELLED', label: 'Cancelled' },
  ]
}
