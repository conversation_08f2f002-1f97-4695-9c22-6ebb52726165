import { NextRequest } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'
import sharp from 'sharp'

// File upload configuration
export const UPLOAD_CONFIG = {
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB default
  allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/jpg,image/png,image/gif,image/webp,image/svg+xml,image/bmp,image/tiff,image/ico,image/avif,application/pdf').split(','),
  uploadDir: process.env.UPLOAD_DIR || './public/uploads',
  imageQuality: 85,
  maxImageWidth: 1920,
  maxImageHeight: 1080,
}

export interface UploadedFile {
  filename: string
  originalName: string
  size: number
  mimeType: string
  url: string
  path: string
}

export class FileUploadError extends Error {
  constructor(message: string, public code: string) {
    super(message)
    this.name = 'FileUploadError'
  }
}

// Validate file type
export function validateFileType(file: File): boolean {
  return UPLOAD_CONFIG.allowedTypes.includes(file.type)
}

// Validate file size
export function validateFileSize(file: File): boolean {
  return file.size <= UPLOAD_CONFIG.maxFileSize
}

// Generate unique filename
export function generateFileName(originalName: string): string {
  const ext = path.extname(originalName)
  const name = path.basename(originalName, ext)
  const timestamp = Date.now()
  const uuid = uuidv4().slice(0, 8)
  return `${name}-${timestamp}-${uuid}${ext}`
}

// Ensure upload directory exists
export async function ensureUploadDir(subDir?: string): Promise<string> {
  const uploadPath = subDir
    ? path.join(UPLOAD_CONFIG.uploadDir, subDir).replace(/\\/g, '/')
    : UPLOAD_CONFIG.uploadDir

  if (!existsSync(uploadPath)) {
    await mkdir(uploadPath, { recursive: true })
  }

  return uploadPath
}

// Process image (resize and optimize)
export async function processImage(
  buffer: Buffer,
  filename: string,
  mimeType: string,
  options?: {
    width?: number
    height?: number
    quality?: number
  }
): Promise<Buffer> {
  const { width, height, quality } = {
    width: options?.width || UPLOAD_CONFIG.maxImageWidth,
    height: options?.height || UPLOAD_CONFIG.maxImageHeight,
    quality: options?.quality || UPLOAD_CONFIG.imageQuality,
  }

  // Skip processing for SVG and other vector formats
  if (mimeType === 'image/svg+xml' || mimeType === 'image/ico') {
    return buffer
  }

  try {
    const sharpInstance = sharp(buffer)
      .resize(width, height, {
        fit: 'inside',
        withoutEnlargement: true,
      })

    // Choose output format based on input
    if (mimeType === 'image/png') {
      return sharpInstance.png({ quality }).toBuffer()
    } else if (mimeType === 'image/webp') {
      return sharpInstance.webp({ quality }).toBuffer()
    } else if (mimeType === 'image/avif') {
      return sharpInstance.avif({ quality }).toBuffer()
    } else {
      // Default to JPEG for other formats
      return sharpInstance.jpeg({ quality }).toBuffer()
    }
  } catch (error) {
    // If processing fails, return original buffer
    console.warn('Image processing failed:', error)
    return buffer
  }
}

// Upload single file
export async function uploadFile(
  file: File,
  subDir?: string,
  processImages = true
): Promise<UploadedFile> {
  // Validate file type
  if (!validateFileType(file)) {
    throw new FileUploadError(
      `File type ${file.type} is not allowed`,
      'INVALID_FILE_TYPE'
    )
  }

  // Validate file size
  if (!validateFileSize(file)) {
    throw new FileUploadError(
      `File size ${file.size} exceeds maximum allowed size of ${UPLOAD_CONFIG.maxFileSize} bytes`,
      'FILE_TOO_LARGE'
    )
  }

  // Generate filename and ensure directory exists
  const filename = generateFileName(file.name)
  const uploadDir = await ensureUploadDir(subDir)
  const filePath = path.join(uploadDir, filename).replace(/\\/g, '/')

  // Get file buffer
  const bytes = await file.arrayBuffer()
  let buffer = Buffer.from(bytes)

  // Process images if enabled
  if (processImages && file.type.startsWith('image/')) {
    try {
      buffer = await processImage(buffer, filename, file.type)
    } catch (error) {
      console.warn('Image processing failed, using original:', error)
    }
  }

  // Write file to disk
  await writeFile(filePath, buffer)

  // Generate public URL - ensure proper path separators
  let publicPath = filePath.replace('./public', '').replace(/\\/g, '/')
  // Ensure the path starts with / and doesn't have double slashes
  publicPath = publicPath.startsWith('/') ? publicPath : `/${publicPath}`
  // Remove any remaining 'public' prefix that might have been added
  publicPath = publicPath.replace(/^\/public\//, '/')
  const url = publicPath.replace(/\/+/g, '/') // Remove any double slashes

  return {
    filename,
    originalName: file.name,
    size: buffer.length,
    mimeType: file.type,
    url,
    path: filePath,
  }
}

// Upload multiple files
export async function uploadFiles(
  files: File[],
  subDir?: string,
  processImages = true
): Promise<UploadedFile[]> {
  const uploadPromises = files.map(file => 
    uploadFile(file, subDir, processImages)
  )
  
  return Promise.all(uploadPromises)
}

// Parse multipart form data from NextRequest
export async function parseFormData(request: NextRequest): Promise<{
  files: File[]
  fields: Record<string, string>
}> {
  const formData = await request.formData()
  const files: File[] = []
  const fields: Record<string, string> = {}

  for (const [key, value] of formData.entries()) {
    if (value instanceof File) {
      files.push(value)
    } else {
      fields[key] = value
    }
  }

  return { files, fields }
}

// Get file type category
export function getFileCategory(mimeType: string): string {
  if (mimeType.startsWith('image/')) return 'image'
  if (mimeType.startsWith('video/')) return 'video'
  if (mimeType.startsWith('audio/')) return 'audio'
  if (mimeType.includes('pdf')) return 'pdf'
  if (mimeType.includes('word') || mimeType.includes('document')) return 'document'
  if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return 'spreadsheet'
  return 'other'
}

// Format file size for display
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
