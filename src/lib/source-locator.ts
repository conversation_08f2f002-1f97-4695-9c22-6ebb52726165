import fs from 'fs/promises'
import path from 'path'

export interface TextLocation {
  filePath: string
  lineNumber: number
  columnStart: number
  columnEnd: number
  fullLine: string
  textContent: string
}

export interface SourceUpdate {
  location: TextLocation
  newText: string
}

export class SourceLocator {
  /**
   * Finds all occurrences of text in a source file
   */
  static async findAllTextLocations(filePath: string, searchText: string): Promise<TextLocation[]> {
    try {
      const absolutePath = path.resolve(process.cwd(), filePath)
      
      // Read the file
      const fileContent = await fs.readFile(absolutePath, 'utf-8')
      const lines = fileContent.split('\n')
      
      const locations: TextLocation[] = []
      
      // Search for the text in each line
      for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
        const line = lines[lineIndex]
        let textIndex = line.indexOf(searchText)
        
        while (textIndex !== -1) {
          locations.push({
            filePath,
            lineNumber: lineIndex + 1,
            columnStart: textIndex + 1,
            columnEnd: textIndex + searchText.length + 1,
            fullLine: line,
            textContent: searchText
          })
          
          // Find next occurrence in the same line
          textIndex = line.indexOf(searchText, textIndex + 1)
        }
      }
      
      return locations
      
    } catch (error) {
      console.error('Error finding text locations:', error)
      return []
    }
  }

  /**
   * Finds the exact location of text in a source file (first occurrence)
   */
  static async findTextLocation(filePath: string, searchText: string): Promise<TextLocation | null> {
    const locations = await this.findAllTextLocations(filePath, searchText)
    return locations.length > 0 ? locations[0] : null
  }
  
  /**
   * Updates text at a specific location in a source file
   */
  static async updateTextAtLocation(update: SourceUpdate): Promise<{ success: boolean; message: string }> {
    try {
      const { location, newText } = update
      const absolutePath = path.resolve(process.cwd(), location.filePath)
      
      console.log(`Updating text at ${location.filePath}:${location.lineNumber}:${location.columnStart}`)
      console.log(`"${location.textContent}" -> "${newText}"`)
      
      // Read the file
      const fileContent = await fs.readFile(absolutePath, 'utf-8')
      const lines = fileContent.split('\n')
      
      // Get the line to update (0-indexed)
      const lineIndex = location.lineNumber - 1
      const currentLine = lines[lineIndex]
      
      // Use columnStart for precise replacement
      const colStart = location.columnStart - 1
      const colEnd = location.columnEnd - 1
      const actualText = currentLine.substring(colStart, colEnd)
      
      if (actualText !== location.textContent) {
        return {
          success: false,
          message: `Text at line ${location.lineNumber}, column ${location.columnStart} does not match expected text. Found: "${actualText}"`
        }
      }
      
      // Update the line by replacing the text at the exact column
      const updatedLine =
        currentLine.substring(0, colStart) +
        newText +
        currentLine.substring(colEnd)
      
      lines[lineIndex] = updatedLine
      
      // Write the updated content back to the file
      await fs.writeFile(absolutePath, lines.join('\n'), 'utf-8')
      
      return {
        success: true,
        message: `Successfully updated text at ${location.filePath}:${location.lineNumber}:${location.columnStart}`
      }
      
    } catch (error) {
      console.error('Error updating text at location:', error)
      return {
        success: false,
        message: `Failed to update text: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }
  
  /**
   * Searches for text across multiple potential source files
   */
  static async searchTextInFiles(searchText: string, pagePath: string): Promise<TextLocation | null> {
    // Clean the pagePath to avoid double paths
    const cleanPagePath = pagePath.replace('src/app', '').replace('/page.tsx', '')

    // Normalize the path to avoid double slashes
    const normalizedPath = cleanPagePath === '/' || cleanPagePath === '' ? '' : cleanPagePath

    // Define potential source files based on the page path
    const potentialFiles = [
      `src/app${normalizedPath}/page.tsx`,
      `src/app${normalizedPath}/layout.tsx`,
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
    ]
    
    // Search in each potential file
    for (const filePath of potentialFiles) {
      try {
        const location = await this.findTextLocation(filePath, searchText)
        if (location) {
          console.log(`Found text in: ${filePath}`)
          return location
        }
      } catch (error) {
        // File doesn't exist or can't be read, continue to next file
        continue
      }
    }
    
    return null
  }
  
  /**
   * Gets a human-readable description of the text location
   */
  static getLocationDescription(location: TextLocation): string {
    const fileName = path.basename(location.filePath)
    const fileDir = path.dirname(location.filePath)
    
    return `${fileDir}/${fileName} (line ${location.lineNumber}, column ${location.columnStart})`
  }
} 