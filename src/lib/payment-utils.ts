import { PaymentFormData, PaymentMethodData } from './payment-schema'

export interface EnhancedPaymentData {
  // Basic payment fields
  amount: number
  paymentMethod: string
  paymentDate: string
  notes?: string
  status?: string
  reference?: string
  transactionId?: string
  processingFee?: number
  
  // Enhanced payment form fields
  currency: string
  promoCode?: string
  discount: number
  emailReceipt: boolean
  receiptEmail?: string
  termsAccepted: boolean
  paymentDetails: any
  
  // Stripe specific fields
  stripePaymentIntentId?: string
  stripeClientSecret?: string
  
  // Related entity IDs
  invoiceId: string | number
  projectId?: string | number
  clientId?: string | number
}

/**
 * Transform form data to payment API format
 */
export function transformPaymentFormData(
  formData: any,
  selectedPaymentMethod: any,
  appliedPromo: any,
  invoice: any,
  project?: any,
  client?: any
): EnhancedPaymentData {
  // Collect payment method specific details
  const paymentMethodDetails: any = {}
  
  // Extract payment method fields from form
  if (selectedPaymentMethod.fields && selectedPaymentMethod.fields.length > 0) {
    selectedPaymentMethod.fields.forEach((field: string) => {
      if (formData[field]) {
        paymentMethodDetails[field] = formData[field]
      }
    })
  }

  // Calculate discount amount
  const discountAmount = appliedPromo ? (
    appliedPromo.type === 'percentage'
      ? (formData.amount * appliedPromo.discount / 100)
      : appliedPromo.discount
  ) : 0

  return {
    amount: formData.amount,
    paymentMethod: selectedPaymentMethod.id,
    paymentDate: formData.paymentDate,
    notes: formData.notes || '',
    currency: formData.currency || 'USD',
    promoCode: appliedPromo?.code,
    discount: discountAmount,
    emailReceipt: formData.emailReceipt || false,
    receiptEmail: formData.receiptEmail || '',
    termsAccepted: formData.termsAccepted || false,
    paymentDetails: paymentMethodDetails,
    invoiceId: invoice.id,
    projectId: project?.id,
    clientId: client?.id
  }
}

/**
 * Validate payment method specific fields
 */
export function validatePaymentMethodFields(
  paymentMethod: any,
  formData: any
): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (!paymentMethod.fields || paymentMethod.fields.length === 0) {
    return { isValid: true, errors: [] }
  }

  paymentMethod.fields.forEach((field: string) => {
    const value = formData[field]
    
    switch (field) {
      case 'cardNumber':
        if (!value || value.length < 13) {
          errors.push('Card number must be at least 13 digits')
        }
        break
      case 'expiryDate':
        if (!value || !/^\d{2}\/\d{2}$/.test(value)) {
          errors.push('Expiry date must be in MM/YY format')
        }
        break
      case 'cvv':
        if (!value || value.length < 3) {
          errors.push('CVV must be at least 3 digits')
        }
        break
      case 'cardholderName':
        if (!value || value.trim().length < 2) {
          errors.push('Cardholder name is required')
        }
        break
      case 'paypalEmail':
        if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          errors.push('Valid PayPal email is required')
        }
        break
      case 'bankName':
      case 'accountNumber':
      case 'routingNumber':
      case 'accountHolderName':
      case 'swiftCode':
      case 'bankAddress':
      case 'checkNumber':
        if (!value || value.trim().length === 0) {
          errors.push(`${field} is required`)
        }
        break
    }
  })

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Format payment method details for display
 */
export function formatPaymentDetails(paymentDetails: any, paymentMethod: string): string {
  if (!paymentDetails) return ''

  switch (paymentMethod) {
    case 'stripe_card':
      return `Card ending in ${paymentDetails.cardNumber?.slice(-4) || '****'}`
    case 'paypal':
      return `PayPal: ${paymentDetails.paypalEmail || 'N/A'}`
    case 'bank_transfer':
      return `Bank: ${paymentDetails.bankName || 'N/A'} - Account: ***${paymentDetails.accountNumber?.slice(-4) || '****'}`
    case 'wire_transfer':
      return `Wire: ${paymentDetails.bankName || 'N/A'} - SWIFT: ${paymentDetails.swiftCode || 'N/A'}`
    case 'ach_transfer':
      return `ACH: ${paymentDetails.bankName || 'N/A'} - Account: ***${paymentDetails.accountNumber?.slice(-4) || '****'}`
    case 'check':
      return `Check #${paymentDetails.checkNumber || 'N/A'} - Bank: ${paymentDetails.bankName || 'N/A'}`
    case 'cash':
      return 'Cash payment'
    case 'apple_pay':
      return 'Apple Pay'
    case 'google_pay':
      return 'Google Pay'
    default:
      return paymentMethod
  }
}

/**
 * Validate minimum payment amount for different payment methods
 */
export function validateMinimumAmount(amount: number, paymentMethod: string, currency: string = 'USD'): {
  isValid: boolean
  minimumAmount: number
  errorMessage?: string
} {
  const minimumAmounts: Record<string, number> = {
    'USD': 0.50,
    'EUR': 0.50,
    'GBP': 0.30,
    'CAD': 0.50,
    // Add more currencies as needed
  }

  const minimum = minimumAmounts[currency.toUpperCase()] || 0.50

  // Stripe and card-based payments have minimum requirements
  const requiresMinimum = [
    'stripe_card',
    'apple_pay',
    'google_pay',
    'paypal'
  ].includes(paymentMethod)

  if (requiresMinimum && amount < minimum) {
    return {
      isValid: false,
      minimumAmount: minimum,
      errorMessage: `Payment amount must be at least $${minimum.toFixed(2)} ${currency.toUpperCase()} for ${formatPaymentMethodName(paymentMethod)} payments.`
    }
  }

  return {
    isValid: true,
    minimumAmount: minimum
  }
}

/**
 * Calculate processing fee based on payment method and amount
 */
export function calculateProcessingFee(paymentMethod: string, amount: number): number {
  switch (paymentMethod) {
    case 'stripe_card':
    case 'apple_pay':
    case 'google_pay':
      return Math.round((amount * 0.029 + 0.30) * 100) / 100 // 2.9% + $0.30
    case 'paypal':
      return Math.round((amount * 0.029 + 0.30) * 100) / 100 // 2.9% + $0.30
    case 'bank_transfer':
    case 'ach_transfer':
      return 0.80 // Flat fee
    case 'wire_transfer':
      return 20.00 // Flat fee
    case 'check':
    case 'cash':
      return 0 // No processing fee
    default:
      return 0
  }
}

/**
 * Get payment method display name
 */
export function getPaymentMethodDisplayName(paymentMethod: string): string {
  const methodNames: Record<string, string> = {
    stripe_card: 'Credit/Debit Card',
    paypal: 'PayPal',
    bank_transfer: 'Bank Transfer',
    wire_transfer: 'Wire Transfer',
    ach_transfer: 'ACH Transfer',
    apple_pay: 'Apple Pay',
    google_pay: 'Google Pay',
    check: 'Check',
    cash: 'Cash'
  }
  
  return methodNames[paymentMethod] || paymentMethod
}
