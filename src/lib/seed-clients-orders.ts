import { prisma } from './prisma'

export async function seedClientsAndOrders() {
  try {
    // Sample client data
    const sampleClients = [
      {
        companyName: 'GreenTech Solutions',
        contactName: '<PERSON>',
        contactEmail: '<EMAIL>',
        contactPhone: '******-0123',
        companyWebsite: 'https://greentech.com',
        address: '123 Tech Street',
        city: 'San Francisco',
        state: 'CA',
        country: 'USA',
        zipCode: '94105',
        notes: 'Sustainable technology company focused on green solutions',
      },
      {
        companyName: 'MedTech Innovations',
        contactName: 'Dr. <PERSON>',
        contactEmail: '<EMAIL>',
        contactPhone: '******-0124',
        companyWebsite: 'https://medtech.com',
        address: '456 Medical Ave',
        city: 'Boston',
        state: 'MA',
        country: 'USA',
        zipCode: '02101',
        notes: 'Healthcare technology and medical device company',
      },
      {
        companyName: 'FinanceFlow Corp',
        contactName: '<PERSON>',
        contactEmail: '<EMAIL>',
        contactPhone: '******-0125',
        companyWebsite: 'https://financeflow.com',
        address: '789 Finance Blvd',
        city: 'New York',
        state: 'NY',
        country: 'USA',
        zipCode: '10001',
        notes: 'Financial services and investment management',
      },
      {
        companyName: 'EduTech Academy',
        contactName: 'Lisa Wang',
        contactEmail: '<EMAIL>',
        contactPhone: '******-0126',
        companyWebsite: 'https://edutech.com',
        address: '321 Learning Lane',
        city: 'Austin',
        state: 'TX',
        country: 'USA',
        zipCode: '73301',
        notes: 'Online education and e-learning platform',
      },
      {
        companyName: 'LogiFlow Solutions',
        contactName: 'David Kim',
        contactEmail: '<EMAIL>',
        contactPhone: '******-0127',
        companyWebsite: 'https://logiflow.com',
        address: '654 Supply Chain St',
        city: 'Chicago',
        state: 'IL',
        country: 'USA',
        zipCode: '60601',
        notes: 'Supply chain and logistics management solutions',
      },
    ]

    // Create clients
    const createdClients = []
    for (const clientData of sampleClients) {
      const existingClient = await prisma.client.findFirst({
        where: { contactEmail: clientData.contactEmail },
      })

      if (!existingClient) {
        const client = await prisma.client.create({
          data: clientData,
        })
        createdClients.push(client)
        console.log(`Created client: ${clientData.companyName}`)
      } else {
        createdClients.push(existingClient)
        console.log(`Client already exists: ${clientData.companyName}`)
      }
    }

    // Sample order data
    const sampleOrders = [
      {
        clientId: createdClients[0].id,
        orderNumber: 'ORD-2024-001',
        description: 'E-commerce platform development project',
        status: 'CONFIRMED',
        totalAmount: 45000,
        orderDate: new Date('2024-01-15'),
      },
      {
        clientId: createdClients[1].id,
        orderNumber: 'ORD-2024-002',
        description: 'Healthcare mobile application development',
        status: 'CONFIRMED',
        totalAmount: 65000,
        orderDate: new Date('2024-02-01'),
      },
      {
        clientId: createdClients[2].id,
        orderNumber: 'ORD-2024-003',
        description: 'Financial analytics dashboard',
        status: 'CONFIRMED',
        totalAmount: 85000,
        orderDate: new Date('2024-01-10'),
      },
      {
        clientId: createdClients[3].id,
        orderNumber: 'ORD-2024-004',
        description: 'Online learning platform development',
        status: 'PENDING',
        totalAmount: 55000,
        orderDate: new Date('2024-03-01'),
      },
      {
        clientId: createdClients[4].id,
        orderNumber: 'ORD-2024-005',
        description: 'Logistics management system',
        status: 'CONFIRMED',
        totalAmount: 70000,
        orderDate: new Date('2024-02-15'),
      },
    ]

    // Create orders
    for (const orderData of sampleOrders) {
      const existingOrder = await prisma.order.findUnique({
        where: { orderNumber: orderData.orderNumber },
      })

      if (!existingOrder) {
        await prisma.order.create({
          data: orderData,
        })
        console.log(`Created order: ${orderData.orderNumber}`)
      } else {
        console.log(`Order already exists: ${orderData.orderNumber}`)
      }
    }

    console.log('Clients and orders seeding completed!')
  } catch (error) {
    console.error('Error seeding clients and orders:', error)
  }
}

// Run this function to seed clients and orders
if (require.main === module) {
  seedClientsAndOrders()
    .then(() => {
      console.log('Seeding finished')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Seeding failed:', error)
      process.exit(1)
    })
}
