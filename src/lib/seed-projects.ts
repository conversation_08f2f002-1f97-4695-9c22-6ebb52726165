import { prisma } from './prisma'

export async function seedProjects() {
  try {
    // First, let's check if we have any orders to link projects to
    const orders = await prisma.order.findMany({
      take: 5,
      include: {
        client: true,
      },
    })

    if (orders.length === 0) {
      console.log('No orders found. Please create some orders first.')
      return
    }

    // Sample project data
    const sampleProjects = [
      {
        name: 'EcoCommerce Platform',
        slug: 'ecocommerce-platform',
        description: 'A comprehensive e-commerce platform for sustainable products with advanced filtering, payment integration, and inventory management. Built with modern technologies to provide a seamless shopping experience.',
        excerpt: 'Sustainable e-commerce platform with advanced features',
        orderId: orders[0].id,
        clientId: orders[0].clientId,
        status: 'COMPLETED',
        manager: '<PERSON>',
        estimatedCost: 45000,
        estimatedTime: '6 months',
        estimatedEffort: '1200 hours',
        startDate: new Date('2023-07-01'),
        completionDate: new Date('2024-01-15'),
        imageUrl: 'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=800&h=600&fit=crop',
        projectUrl: 'https://ecocommerce-demo.vercel.app',
        githubUrl: 'https://github.com/technoloway/ecocommerce-platform',
        tags: 'e-commerce, sustainability, web, react, nextjs',
        isFeatured: true,
        isPublic: true,
        displayOrder: 1,
      },
      {
        name: 'HealthTracker Mobile App',
        slug: 'healthtracker-mobile-app',
        description: 'A comprehensive health tracking mobile application with real-time monitoring, doctor consultations, and personalized health insights. Connects patients with healthcare providers seamlessly.',
        excerpt: 'Health monitoring mobile app with real-time features',
        orderId: orders[1]?.id || orders[0].id,
        clientId: orders[1]?.clientId || orders[0].clientId,
        status: 'COMPLETED',
        manager: 'Dr. Michael Chen',
        estimatedCost: 65000,
        estimatedTime: '8 months',
        estimatedEffort: '1600 hours',
        startDate: new Date('2023-06-01'),
        completionDate: new Date('2024-02-20'),
        imageUrl: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop',
        projectUrl: 'https://healthtracker-app.com',
        githubUrl: 'https://github.com/technoloway/healthtracker-app',
        tags: 'healthcare, mobile, react-native, telemedicine',
        isFeatured: true,
        isPublic: true,
        displayOrder: 2,
      },
      {
        name: 'Financial Analytics Dashboard',
        slug: 'financial-analytics-dashboard',
        description: 'A sophisticated financial analytics platform providing real-time insights, risk assessment, and portfolio management for investment firms. Features advanced data visualization and AI-powered recommendations.',
        excerpt: 'Advanced financial analytics with AI insights',
        orderId: orders[2]?.id || orders[0].id,
        clientId: orders[2]?.clientId || orders[0].clientId,
        status: 'COMPLETED',
        manager: 'Emily Rodriguez',
        estimatedCost: 85000,
        estimatedTime: '10 months',
        estimatedEffort: '2000 hours',
        startDate: new Date('2023-03-01'),
        completionDate: new Date('2023-12-10'),
        imageUrl: 'https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=600&fit=crop',
        projectUrl: 'https://finanalytics-dashboard.com',
        githubUrl: 'https://github.com/technoloway/financial-dashboard',
        tags: 'fintech, analytics, dashboard, ai, data-visualization',
        isFeatured: true,
        isPublic: true,
        displayOrder: 3,
      },
      {
        name: 'Smart Logistics Management',
        slug: 'smart-logistics-management',
        description: 'An intelligent logistics management system with route optimization, real-time tracking, and automated scheduling for delivery companies. Reduces costs and improves efficiency.',
        excerpt: 'AI-powered logistics optimization system',
        orderId: orders[3]?.id || orders[0].id,
        clientId: orders[3]?.clientId || orders[0].clientId,
        status: 'IN_PROGRESS',
        manager: 'David Kim',
        estimatedCost: 70000,
        estimatedTime: '9 months',
        estimatedEffort: '1800 hours',
        startDate: new Date('2024-01-01'),
        completionDate: null,
        imageUrl: 'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?w=800&h=600&fit=crop',
        projectUrl: null,
        githubUrl: 'https://github.com/technoloway/logistics-system',
        tags: 'logistics, optimization, tracking, automation',
        isFeatured: false,
        isPublic: true,
        displayOrder: 4,
      },
      {
        name: 'EduLearn Online Platform',
        slug: 'edulearn-online-platform',
        description: 'An interactive online learning platform with video courses, live sessions, progress tracking, and certification management. Revolutionizing digital education.',
        excerpt: 'Interactive online learning with certifications',
        orderId: orders[4]?.id || orders[0].id,
        clientId: orders[4]?.clientId || orders[0].clientId,
        status: 'PLANNING',
        manager: 'Lisa Wang',
        estimatedCost: 55000,
        estimatedTime: '7 months',
        estimatedEffort: '1400 hours',
        startDate: new Date('2024-03-01'),
        completionDate: null,
        imageUrl: 'https://images.unsplash.com/photo-1501504905252-473c47e087f8?w=800&h=600&fit=crop',
        projectUrl: null,
        githubUrl: null,
        tags: 'education, e-learning, video, certification',
        isFeatured: false,
        isPublic: false,
        displayOrder: 5,
      },
    ]

    // Create projects
    for (const projectData of sampleProjects) {
      const existingProject = await prisma.project.findUnique({
        where: { slug: projectData.slug },
      })

      if (!existingProject) {
        await prisma.project.create({
          data: projectData,
        })
        console.log(`Created project: ${projectData.name}`)
      } else {
        console.log(`Project already exists: ${projectData.name}`)
      }
    }

    console.log('Project seeding completed!')
  } catch (error) {
    console.error('Error seeding projects:', error)
  }
}

// Run this function to seed projects
if (require.main === module) {
  seedProjects()
    .then(() => {
      console.log('Seeding finished')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Seeding failed:', error)
      process.exit(1)
    })
}
