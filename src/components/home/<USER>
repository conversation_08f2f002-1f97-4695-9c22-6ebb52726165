'use client';

import { useState, useEffect } from 'react';
import { useStaticContent } from '@/lib/hooks/use-static-content';
import { NewsletterFormIsolated } from './newsletter-form-isolated';



export function NewsletterSection() {
  const [isNewsletterSubmitted, setIsNewsletterSubmitted] = useState(false);
  const { getContent } = useStaticContent();

  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsNewsletterSubmitted(true);
    setTimeout(() => setIsNewsletterSubmitted(false), 3000);
  };

  return (
    <section className="py-24 bg-white" suppressHydrationWarning>
      <div className="container px-6 mx-auto" suppressHydrationWarning>
        <div className="section-header max-w-4xl mx-auto text-center" suppressHydrationWarning>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6" suppressHydrationWarning>
            {getContent('home', 'newsletter', 'title', 'Stay in the')} <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{getContent('home', 'newsletter', 'title_highlight', 'Loop')}</span>
          </h2>
          <p className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto" suppressHydrationWarning>
            {getContent('home', 'newsletter', 'subtitle', 'Get the latest insights, tips, and updates delivered straight to your inbox. Join thousands of developers and business leaders.')}
          </p>

          <NewsletterFormIsolated
            getContent={getContent}
            isNewsletterSubmitted={isNewsletterSubmitted}
            handleNewsletterSubmit={handleNewsletterSubmit}
          />

          {isNewsletterSubmitted && (
            <div className="mt-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg" suppressHydrationWarning>
              Thank you for subscribing! You'll receive our latest updates soon.
            </div>
          )}
        </div>
      </div>
    </section>
  );
}
