'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { useStaticContent } from '@/lib/hooks/use-static-content';

interface Testimonial {
  id: string;
  content: string;
  clientName: string;
  clientTitle?: string;
  clientCompany?: string;
  clientPhotoUrl?: string;
  rating?: number;
}

interface TestimonialsSectionProps {
  testimonials: Testimonial[];
}

export function TestimonialsSection({ testimonials }: TestimonialsSectionProps) {
  const { getContent } = useStaticContent();
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  // Auto-rotate testimonials
  useEffect(() => {
    if (testimonials.length > 1) {
      const timer = setInterval(() => {
        setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
      }, 5000);
      return () => clearInterval(timer);
    }
  }, [testimonials]);

  if (testimonials.length === 0) {
    return (
      <section className="py-24 bg-gradient-to-br from-[#d0ebff]/8 via-blue-50/40 to-white">
        <div className="container px-6 mx-auto">
          <div className="section-header text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              {getContent('home', 'testimonials', 'title', 'What Our')} <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{getContent('home', 'testimonials', 'title_highlight', 'Clients Sayyyyyy')}</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {getContent('home', 'testimonials', 'subtitle', 'Don\'t just take our word for it. Here\'s what our clients have to say about working with us.')}
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="bg-white p-12 rounded-3xl border border-gray-200 shadow-lg text-center">
              <p className="text-gray-600">{getContent('home', 'testimonials', 'no_content_message', 'No testimonials available at the moment.')}</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  const currentTestimonialData = testimonials[currentTestimonial];

  return (
    <section className="py-24 bg-gradient-to-br from-[#d0ebff]/8 via-blue-50/40 to-white">
      <div className="container px-6 mx-auto">
        <div className="section-header text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {getContent('home', 'testimonials', 'title', 'What Our')} <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{getContent('home', 'testimonials', 'title_highlight', 'Clients Say')}</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {getContent('home', 'testimonials', 'subtitle', 'Don\'t just take our word for it. Here\'s what our clients have to say about working with us.')}
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="relative">
            <div className="testimonial-card bg-white p-12 rounded-3xl border border-gray-200 shadow-lg">
              <div className="text-center">
                <div className="flex justify-center mb-6">
                  {Array.from({ length: currentTestimonialData?.rating || 5 }, (_, i) => (
                    <StarIconSolid key={i} className="w-6 h-6 text-yellow-400" />
                  ))}
                </div>

                <blockquote className="text-2xl text-gray-700 mb-8 leading-relaxed font-medium">
                  "{currentTestimonialData?.content}"
                </blockquote>

                <div className="flex items-center justify-center">
                  {currentTestimonialData?.clientPhotoUrl ? (
                    <Image
                      src={currentTestimonialData.clientPhotoUrl}
                      alt={currentTestimonialData.clientName}
                      width={64}
                      height={64}
                      className="rounded-full mr-4"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gradient-to-br from-[#d0ebff] to-[#e0c3fc] rounded-full flex items-center justify-center mr-4">
                      <span className="text-blue-700 font-bold text-xl">
                        {currentTestimonialData?.clientName?.charAt(0)}
                      </span>
                    </div>
                  )}
                  <div className="text-left">
                    <div className="font-bold text-gray-900 text-lg">
                      {currentTestimonialData?.clientName}
                    </div>
                    <div className="text-gray-600">
                      {currentTestimonialData?.clientTitle} at {currentTestimonialData?.clientCompany}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation */}
            {testimonials.length > 1 && (
              <>
                <div className="flex justify-center mt-8 space-x-4">
                  <button
                    onClick={() => setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length)}
                    className="p-3 bg-white border border-gray-200 rounded-full hover:bg-[#d0ebff]/20 hover:border-[#d0ebff] transition-all duration-300"
                    aria-label="Previous testimonial"
                  >
                    <ChevronLeftIcon className="h-5 w-5 text-gray-600" />
                  </button>
                  <button
                    onClick={() => setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)}
                    className="p-3 bg-white border border-gray-200 rounded-full hover:bg-[#d0ebff]/20 hover:border-[#d0ebff] transition-all duration-300"
                    aria-label="Next testimonial"
                  >
                    <ChevronRightIcon className="h-5 w-5 text-gray-600" />
                  </button>
                </div>

                {/* Dots indicator */}
                <div className="flex justify-center mt-6 space-x-2">
                  {testimonials.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentTestimonial(index)}
                      className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        index === currentTestimonial
                          ? 'bg-blue-600'
                          : 'bg-gray-300 hover:bg-gray-400'
                      }`}
                      aria-label={`Go to testimonial ${index + 1}`}
                    />
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
