'use client';

import Image from 'next/image';
import Link from 'next/link';
import { 
  ArrowRightIcon,
  RocketLaunchIcon,
  HeartIcon,
  CurrencyDollarIcon,
  GlobeAltIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline';
import { useStaticContent } from '@/lib/hooks/use-static-content';

interface Project {
  id: string;
  name: string;
  description?: string;
  excerpt?: string;
  category?: string;
  type?: string;
  slug?: string;
  imageUrl?: string;
  metrics?: Array<{ value: string; label: string }>;
}

interface ProjectsSectionProps {
  projects: Project[];
}

// Get project icon based on category
const getProjectIcon = (category: string | null | undefined) => {
  if (!category || typeof category !== 'string') {
    return RocketLaunchIcon;
  }

  switch (category.toLowerCase()) {
    case 'healthcare':
    case 'health':
      return HeartIcon;
    case 'finance':
    case 'fintech':
    case 'banking':
      return CurrencyDollarIcon;
    case 'ecommerce':
    case 'retail':
      return GlobeAltIcon;
    case 'education':
      return AcademicCapIcon;
    default:
      return RocketLaunchIcon;
  }
};

export function ProjectsSection({ projects }: ProjectsSectionProps) {
  const { getContent } = useStaticContent();

  return (
    <section className="py-24 bg-gradient-to-br from-purple-50/30 to-white">
      <div className="container px-6 mx-auto">
        <div className="section-header text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {getContent('home', 'projects', 'title', '7Featured7')} <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{getContent('home', 'projects', 'title_highlight', 'Case Studies')}</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {getContent('home', 'projects', 'subtitle', 'Real projects, real results. See how we\'ve helped businesses transform with technology.')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
          {projects.length > 0 ? (
            projects.slice(0, 2).map((project, index) => {
              const Icon = getProjectIcon(project.category || '');
              const gradientColors = index === 0
                ? 'from-[#d0ebff] to-[#e0c3fc]'
                : 'from-[#e0c3fc] to-[#d0ebff]';
              const textColor = index === 0 ? 'text-blue-700' : 'text-purple-700';
              const hoverColor = index === 0 ? 'group-hover:text-blue-600' : 'group-hover:text-purple-600';

              return (
                <div
                  key={project.id}
                  className="service-card"
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <Link href={`/projects/${project.slug || project.id}`} className="group block">
                    <div className="bg-gradient-to-br from-white to-[#d0ebff]/10 rounded-3xl border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
                      {project.imageUrl ? (
                        <div className="h-64 relative overflow-hidden">
                          <Image
                            src={project.imageUrl}
                            alt={project.name}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-500"
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                          <div className="absolute bottom-4 left-4">
                            <h3 className="text-2xl font-bold text-white">{project.name}</h3>
                          </div>
                        </div>
                      ) : (
                        <div className={`h-64 bg-gradient-to-br ${gradientColors} flex items-center justify-center`}>
                          <div className="text-center p-8">
                            <div className="inline-flex p-6 bg-white/20 backdrop-blur-sm rounded-2xl mb-4">
                              <Icon className={`h-16 w-16 ${textColor}`} />
                            </div>
                            <h3 className={`text-2xl font-bold ${textColor.replace('text-', 'text-').replace('-700', '-900')}`}>
                              {project.name}
                            </h3>
                          </div>
                        </div>
                      )}

                      <div className="p-8">
                        <div className="mb-6">
                          {project.category && (
                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[${index === 0 ? '#d0ebff' : '#e0c3fc'}]/30 ${textColor} mb-4`}>
                              {project.category} • {project.type || 'Web Platform'}
                            </span>
                          )}
                          <h3 className={`text-2xl font-bold text-gray-900 mb-4 ${hoverColor} transition-colors duration-300`}>
                            {project.excerpt || project.name}
                          </h3>
                          <p className="text-gray-600 leading-relaxed mb-6">
                            {project.description}
                          </p>
                        </div>

                        {project.metrics && (
                          <div className="grid grid-cols-3 gap-4 mb-6">
                            {project.metrics.slice(0, 3).map((metric, metricIndex) => (
                              <div key={metricIndex} className="text-center">
                                <div className={`text-2xl font-bold ${index === 0 ? 'text-blue-600' : 'text-purple-600'}`}>
                                  {metric.value}
                                </div>
                                <div className="text-sm text-gray-600">{metric.label}</div>
                              </div>
                            ))}
                          </div>
                        )}

                        <div className={`flex items-center ${index === 0 ? 'text-blue-600' : 'text-purple-600'} font-semibold group-hover:translate-x-2 transition-transform duration-300`}>
                          {getContent('home', 'projects', 'read_case_study', 'Read Case Study')}
                          <ArrowRightIcon className="ml-2 h-5 w-5" />
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
              );
            })
          ) : (
            // Fallback content when no projects are available
            Array.from({ length: 2 }, (_, index) => (
              <div
                key={index}
                className="service-card"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="bg-gradient-to-br from-white to-[#d0ebff]/10 rounded-3xl border border-gray-200 overflow-hidden">
                  <div className={`h-64 bg-gradient-to-br ${index === 0 ? 'from-[#d0ebff] to-[#e0c3fc]' : 'from-[#e0c3fc] to-[#d0ebff]'} flex items-center justify-center`}>
                    <div className="text-center p-8">
                      <div className="inline-flex p-6 bg-white/20 backdrop-blur-sm rounded-2xl mb-4">
                        <RocketLaunchIcon className={`h-16 w-16 ${index === 0 ? 'text-blue-700' : 'text-purple-700'}`} />
                      </div>
                      <h3 className={`text-2xl font-bold ${index === 0 ? 'text-blue-900' : 'text-purple-900'}`}>
                        {getContent('home', 'projects', 'fallback_title', 'Project Coming Soon')}
                      </h3>
                    </div>
                  </div>
                  <div className="p-8">
                    <div className="mb-6">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[${index === 0 ? '#d0ebff' : '#e0c3fc'}]/30 ${index === 0 ? 'text-blue-700' : 'text-purple-700'} mb-4`}>
                        Technology • Web Platform
                      </span>
                      <h3 className={`text-2xl font-bold text-gray-900 mb-4`}>
                        {getContent('home', 'projects', 'fallback_subtitle', 'Exciting Project in Development')}
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-6">
                        {getContent('home', 'projects', 'fallback_description', 'We\'re working on amazing projects that will showcase our capabilities. Stay tuned for updates.')}
                      </p>
                    </div>
                    <div className={`flex items-center text-gray-400 font-semibold`}>
                      {getContent('home', 'projects', 'fallback_button', 'Coming Soon')}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        <div className="section-header text-center">
          <Link
            href="/projects"
            className="inline-flex items-center px-8 py-4 bg-white border border-gray-200 text-gray-700 font-semibold rounded-xl hover:bg-gray-50 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
          >
            {getContent('home', 'projects', 'view_all_button', 'View All Projects')}
            <ArrowRightIcon className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>
  );
}
