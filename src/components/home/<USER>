'use client';

import { useState, useEffect, useRef } from 'react';

interface NewsletterFormIsolatedProps {
  getContent: any;
  isNewsletterSubmitted: boolean;
  handleNewsletterSubmit: (e: React.FormEvent) => void;
}

export function NewsletterFormIsolated({ 
  getContent, 
  isNewsletterSubmitted, 
  handleNewsletterSubmit 
}: NewsletterFormIsolatedProps) {
  const [mounted, setMounted] = useState(false);
  const [formId] = useState(() => `newsletter-form-${Math.random().toString(36).substr(2, 9)}`);
  const formRef = useRef<HTMLFormElement>(null);

  useEffect(() => {
    setMounted(true);
    
    // Clean up any browser extension artifacts after mount
    const cleanupTimer = setTimeout(() => {
      if (formRef.current) {
        // Remove any random IDs that might have been injected
        const walker = document.createTreeWalker(
          formRef.current,
          NodeFilter.SHOW_ELEMENT,
          null,
          false
        );
        
        let node;
        while (node = walker.nextNode()) {
          const element = node as Element;
          if (element.id && element.id.match(/^abId\d+\.\d+$/)) {
            element.removeAttribute('id');
          }
          
          // Remove other extension attributes
          const extensionAttrs = ['abframeid', 'abineguid', 'data-pwm-inline'];
          extensionAttrs.forEach(attr => {
            if (element.hasAttribute(attr)) {
              element.removeAttribute(attr);
            }
          });
        }
      }
    }, 200);
    
    return () => clearTimeout(cleanupTimer);
  }, []);

  // Don't render anything during SSR to completely avoid hydration issues
  if (!mounted) {
    return (
      <div 
        className="max-w-md mx-auto" 
        suppressHydrationWarning
        style={{ minHeight: '120px' }}
      >
        <div className="flex flex-col sm:flex-row gap-4" suppressHydrationWarning>
          <div 
            className="flex-1 px-6 py-4 border border-gray-300 rounded-xl bg-gray-50 animate-pulse" 
            suppressHydrationWarning
          />
          <div 
            className="px-8 py-4 bg-gray-300 rounded-xl animate-pulse" 
            suppressHydrationWarning
            style={{ width: '120px', height: '56px' }}
          />
        </div>
        <div 
          className="text-sm text-gray-400 mt-4 h-4 bg-gray-200 rounded animate-pulse" 
          suppressHydrationWarning
        />
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto" suppressHydrationWarning>
      <form 
        ref={formRef}
        id={formId}
        onSubmit={handleNewsletterSubmit} 
        className="w-full" 
        suppressHydrationWarning
        style={{ isolation: 'isolate' }}
      >
        <div 
          className="flex flex-col sm:flex-row gap-4" 
          suppressHydrationWarning
          style={{ isolation: 'isolate' }}
        >
          <input
            type="email"
            placeholder={getContent('home', 'newsletter', 'input_placeholder', 'Enter your email')}
            required
            className="flex-1 px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
            suppressHydrationWarning
            style={{ isolation: 'isolate' }}
          />
          <button
            type="submit"
            disabled={isNewsletterSubmitted}
            className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 disabled:opacity-50 disabled:cursor-not-allowed"
            suppressHydrationWarning
            style={{ isolation: 'isolate' }}
          >
            {isNewsletterSubmitted 
              ? getContent('home', 'newsletter', 'button_text_success', 'Subscribed!') 
              : getContent('home', 'newsletter', 'button_text', 'Subscribe')
            }
          </button>
        </div>
        <p 
          className="text-sm text-gray-500 mt-4" 
          suppressHydrationWarning
          style={{ isolation: 'isolate' }}
        >
          {getContent('home', 'newsletter', 'privacy_text', 'No spam, unsubscribe at any time. We respect your privacy.')}
        </p>
      </form>
    </div>
  );
}
