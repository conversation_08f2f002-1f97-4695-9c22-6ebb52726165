'use client'

import { memo } from 'react'
import { useHeroSlides } from './hero-carousel'
import HeroCarousel from './hero-carousel'

interface Stat {
  name: string
  value: string
  iconName: string
}

interface HeroSectionProps {
  pageId?: string
  sectionId?: string
  className?: string
  stats?: Stat[]
}

const HeroSection = memo(function HeroSection({ 
  pageId = 'home', 
  sectionId = 'home-hero',
  className = '',
  stats = []
}: HeroSectionProps) {
  const { slides, loading, error } = useHeroSlides(pageId, sectionId)

  if (loading) {
    return (
      <section 
        className={`relative h-96 bg-gray-100 flex items-center justify-center ${className}`}
        aria-label="Loading hero content"
      >
        <div className="text-center">
          <div 
            className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"
            role="status"
            aria-label="Loading"
          />
          <p className="text-gray-600">Loading hero content...</p>
        </div>
      </section>
    )
  }

  if (error) {
    return (
      <section 
        className={`relative h-96 bg-gray-100 flex items-center justify-center ${className}`}
        aria-label="Hero content error"
      >
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading hero content: {error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            aria-label="Retry loading hero content"
          >
            Retry
          </button>
        </div>
      </section>
    )
  }

  return (
    <section className={className} data-section="home-hero" aria-label="Hero section">
      <HeroCarousel
        slides={slides}
        autoPlay={true}
        autoPlayInterval={5000}
        showNavigation={true}
        showIndicators={true}
      />
      
      {/* Stats Section */}
      {stats && stats.length > 0 && (
        <div className="bg-white py-12" role="region" aria-label="Company statistics">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div key={`${stat.name}-${index}`} className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2" aria-label={`${stat.value} ${stat.name}`}>
                    {stat.value}
                  </div>
                  <div className="text-sm text-gray-600">{stat.name}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </section>
  )
})

// Alternative component that accepts slides directly (for static data or testing)
interface HeroSlide {
  id: string
  title: string
  subtitle: string
  buttonText: string
  buttonUrl: string
  imageUrl: string
  displayOrder: number
  isActive: boolean
}

interface StaticHeroSectionProps {
  slides: HeroSlide[]
  className?: string
}

export const StaticHeroSection = memo(function StaticHeroSection({ 
  slides, 
  className = '' 
}: StaticHeroSectionProps) {
  return (
    <section className={className} data-section="static-hero" aria-label="Static hero section">
      <HeroCarousel
        slides={slides}
        autoPlay={true}
        autoPlayInterval={5000}
        showNavigation={true}
        showIndicators={true}
      />
    </section>
  )
})

export default HeroSection
