'use client'

import { useState, useEffect, useCallback, memo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import { useStaticContent } from '@/lib/hooks/use-static-content'

interface HeroSlide {
  id: string
  title: string
  subtitle: string
  buttonText: string
  buttonUrl: string
  imageUrl: string
  displayOrder: number
  isActive: boolean
}

interface HeroCarouselProps {
  slides: HeroSlide[]
  autoPlay?: boolean
  autoPlayInterval?: number
  showNavigation?: boolean
  showIndicators?: boolean
  className?: string
}

// Navigation button component
const NavigationButton = memo(function NavigationButton({ 
  direction, 
  onClick, 
  disabled = false,
  className = ''
}: { 
  direction: 'prev' | 'next'; 
  onClick: () => void; 
  disabled?: boolean;
  className?: string;
}) {
  const Icon = direction === 'prev' ? ChevronLeftIcon : ChevronRightIcon;
  const label = direction === 'prev' ? 'Previous slide' : 'Next slide';

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`absolute top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-2 rounded-full transition-colors duration-200 backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 ${className}`}
      aria-label={label}
    >
      <Icon className="h-6 w-6" aria-hidden="true" />
    </button>
  );
});

// Indicator button component
const IndicatorButton = memo(function IndicatorButton({ 
  index, 
  isActive, 
  onClick 
}: { 
  index: number; 
  isActive: boolean; 
  onClick: () => void;
}) {
  return (
    <button
      onClick={onClick}
      className={`w-3 h-3 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 ${
        isActive
          ? 'bg-white scale-110'
          : 'bg-white/50 hover:bg-white/75'
      }`}
      aria-label={`Go to slide ${index + 1}`}
      aria-current={isActive ? 'true' : 'false'}
    />
  );
});

const HeroCarousel = memo(function HeroCarousel({
  slides,
  autoPlay = true,
  autoPlayInterval = 5000,
  showNavigation = true,
  showIndicators = true,
  className = ''
}: HeroCarouselProps) {
  const { getContent } = useStaticContent();
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay)

  // Filter active slides and sort by display order
  const activeSlides = slides
    .filter(slide => slide.isActive)
    .sort((a, b) => a.displayOrder - b.displayOrder)

  // Memoized navigation functions
  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index)
    setIsAutoPlaying(false)
    // Resume auto-play after 10 seconds of inactivity
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }, []);

  const goToPrevious = useCallback(() => {
    setCurrentSlide((prev) => (prev - 1 + activeSlides.length) % activeSlides.length)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }, [activeSlides.length]);

  const goToNext = useCallback(() => {
    setCurrentSlide((prev) => (prev + 1) % activeSlides.length)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }, [activeSlides.length]);

  // Auto-play effect
  useEffect(() => {
    if (!isAutoPlaying || activeSlides.length <= 1) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % activeSlides.length)
    }, autoPlayInterval)

    return () => clearInterval(interval)
  }, [isAutoPlaying, autoPlayInterval, activeSlides.length])

  // Handle button click
  const handleButtonClick = useCallback(() => {
    const currentSlideData = activeSlides[currentSlide];
    if (currentSlideData.buttonUrl.startsWith('http')) {
      window.open(currentSlideData.buttonUrl, '_blank', 'noopener,noreferrer');
    } else {
      window.location.href = currentSlideData.buttonUrl;
    }
  }, [activeSlides, currentSlide]);

  if (!activeSlides.length) {
    return (
      <div className={`relative h-96 bg-gray-100 flex items-center justify-center ${className}`} aria-label="No hero content">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-600 mb-2">{getContent('home', 'hero', 'no_content_title', 'No Hero Content')}</h2>
          <p className="text-gray-500">{getContent('home', 'hero', 'no_content_message', 'Please add hero slides in the admin panel')}</p>
        </div>
      </div>
    )
  }

  const currentSlideData = activeSlides[currentSlide];

  return (
    <div className={`relative overflow-hidden ${className}`} role="region" aria-label="Hero carousel">
      {/* Slides */}
      <div className="relative h-96 md:h-[500px] lg:h-[600px]">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentSlide}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="absolute inset-0"
          >
            <div className="relative w-full h-full">
              {/* Background Image */}
              <img
                src={currentSlideData.imageUrl}
                alt=""
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.src = '/images/placeholder.jpg'
                }}
                aria-hidden="true"
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-transparent" aria-hidden="true" />
              
              {/* Content */}
              <div className="absolute inset-0 flex items-center">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                    className="text-white"
                  >
                    <h1 
                      className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight"
                      data-content-section="hero"
                      data-content-key="title"
                      data-edit-id="hero-title"
                    >
                      {currentSlideData.title}
                    </h1>
                    <p 
                      className="text-xl md:text-2xl mb-8 text-gray-200 max-w-2xl"
                      data-content-section="hero"
                      data-content-key="subtitle"
                      data-edit-id="hero-subtitle"
                    >
                      {currentSlideData.subtitle}
                    </p>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      data-slide-field="buttonText"
                      onClick={handleButtonClick}
                      aria-label={currentSlideData.buttonText}
                    >
                      {currentSlideData.buttonText}
                    </motion.button>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Navigation Arrows */}
      {showNavigation && activeSlides.length > 1 && (
        <>
          <NavigationButton 
            direction="prev" 
            onClick={goToPrevious}
            className="left-4"
          />
          <NavigationButton 
            direction="next" 
            onClick={goToNext}
            className="right-4"
          />
        </>
      )}

      {/* Indicators */}
      {showIndicators && activeSlides.length > 1 && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2" role="tablist" aria-label="Slide navigation">
          {activeSlides.map((_, index) => (
            <IndicatorButton
              key={index}
              index={index}
              isActive={index === currentSlide}
              onClick={() => goToSlide(index)}
            />
          ))}
        </div>
      )}

      {/* Slide Counter */}
      {activeSlides.length > 1 && (
        <div className="absolute top-4 right-4 bg-black/20 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm" aria-label="Slide counter">
          {currentSlide + 1} / {activeSlides.length}
        </div>
      )}
    </div>
  )
})

export default HeroCarousel

// Hook for managing hero slides from API
export function useHeroSlides(pageId: string, sectionId: string) {
  const [slides, setSlides] = useState<HeroSlide[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchSlides = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/content/pages')
        
        if (!response.ok) {
          throw new Error('Failed to fetch hero slides')
        }

        const data = await response.json()
        const page = data.pages?.find((p: any) => p.id === pageId)
        const section = page?.sections?.find((s: any) => s.id === sectionId)
        
        if (section?.slides) {
          setSlides(section.slides)
        } else {
          setSlides([])
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
        setSlides([])
      } finally {
        setLoading(false)
      }
    }

    fetchSlides()
  }, [pageId, sectionId])

  return { slides, loading, error }
} 