'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useStaticContent } from '@/lib/hooks/use-static-content';

interface TeamMember {
  id: string;
  firstName?: string;
  lastName?: string;
  position?: string;
  department?: string;
  bio?: string;
  photoUrl?: string;
  linkedInUrl?: string;
  githubUrl?: string;
  twitterUrl?: string;
}

interface TeamSectionProps {
  teamMembers: TeamMember[];
}

export function TeamSection({ teamMembers }: TeamSectionProps) {
  const { getContent } = useStaticContent();

  return (
    <section id="team" className="py-24 bg-gradient-to-br from-[#e0c3fc]/8 via-purple-50/30 to-blue-50/20">
      <div className="container px-6 mx-auto">
        <div className="section-header text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {getContent('home', 'team', 'title', 'Meet Our')} <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{getContent('home', 'team', 'title_highlight', 'Team')}</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {getContent('home', 'team', 'subtitle', 'Passionate experts dedicated to bringing your vision to life')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {teamMembers.length > 0 ? (
            teamMembers.map((member, index) => (
              <div
                key={member.id}
                className="service-card group"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="bg-white p-8 rounded-2xl border border-gray-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 text-center h-full flex flex-col">
                  <div className="relative mb-6">
                    {member.photoUrl ? (
                      <Image
                        src={member.photoUrl}
                        alt={`${member.firstName} ${member.lastName}`}
                        width={120}
                        height={120}
                        className="rounded-full mx-auto group-hover:scale-105 transition-transform duration-300"
                        loading="lazy"
                      />
                    ) : (
                      <div className="w-30 h-30 bg-gradient-to-br from-[#d0ebff] to-[#e0c3fc] rounded-full flex items-center justify-center mx-auto group-hover:scale-105 transition-transform duration-300">
                        <span className="text-blue-700 font-bold text-3xl">
                          {member.firstName?.charAt(0)}{member.lastName?.charAt(0)}
                        </span>
                      </div>
                    )}
                    <div className="absolute inset-0 bg-gradient-to-br from-[#d0ebff]/20 to-[#e0c3fc]/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {member.firstName} {member.lastName}
                  </h3>
                  <p className="text-blue-600 font-semibold mb-4">{member.position}</p>
                  {member.department && (
                    <p className="text-gray-500 text-sm mb-4">{member.department}</p>
                  )}
                  {member.bio && (
                    <p className="text-gray-600 text-sm leading-relaxed mb-6 flex-grow">{member.bio}</p>
                  )}

                  <div className="flex justify-center space-x-3 mt-auto">
                    {member.linkedInUrl && (
                      <Link
                        href={member.linkedInUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors duration-300"
                        aria-label={`${member.firstName}'s LinkedIn profile`}
                      >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clipRule="evenodd" />
                        </svg>
                      </Link>
                    )}
                    {member.githubUrl && (
                      <Link
                        href={member.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-gray-600 hover:text-gray-700 transition-colors duration-300"
                        aria-label={`${member.firstName}'s GitHub profile`}
                      >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                        </svg>
                      </Link>
                    )}
                    {member.twitterUrl && (
                      <Link
                        href={member.twitterUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-blue-400 hover:text-blue-500 transition-colors duration-300"
                        aria-label={`${member.firstName}'s Twitter profile`}
                      >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                        </svg>
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            // Fallback content when no team members are available
            Array.from({ length: 4 }, (_, index) => (
              <div
                key={index}
                className="service-card group"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="bg-white p-8 rounded-2xl border border-gray-200 text-center h-full flex flex-col">
                  <div className="relative mb-6">
                    <div className="w-30 h-30 bg-gradient-to-br from-[#d0ebff] to-[#e0c3fc] rounded-full flex items-center justify-center mx-auto">
                      <span className="text-blue-700 font-bold text-3xl">TM</span>
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {getContent('home', 'team', 'fallback_title', 'Team Member')}
                  </h3>
                  <p className="text-blue-600 font-semibold mb-4">{getContent('home', 'team', 'fallback_position', 'Position')}</p>
                  <p className="text-gray-600 text-sm leading-relaxed mb-6 flex-grow">
                    {getContent('home', 'team', 'fallback_description', 'Meet our amazing team members who are passionate about delivering exceptional results.')}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </section>
  );
}
