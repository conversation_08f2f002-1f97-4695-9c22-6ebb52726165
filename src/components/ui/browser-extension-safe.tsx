'use client';

import { ReactNode } from 'react';
import { useBrowserExtensionHandler } from '@/lib/hooks/use-browser-extension-handler';

interface BrowserExtensionSafeProps {
  children: ReactNode;
  fallback?: ReactNode;
  className?: string;
  [key: string]: any;
}

export function BrowserExtensionSafe({ 
  children, 
  fallback = null, 
  className = '',
  ...props 
}: BrowserExtensionSafeProps) {
  const { isClient, hasBrowserExtensions } = useBrowserExtensionHandler();

  // If we're not on client yet, show fallback
  if (!isClient) {
    return <div className={className} {...props}>{fallback}</div>;
  }

  // If browser extensions are detected, wrap with suppressHydrationWarning
  if (hasBrowserExtensions) {
    return (
      <div className={className} suppressHydrationWarning {...props}>
        {children}
      </div>
    );
  }

  // Normal rendering
  return (
    <div className={className} {...props}>
      {children}
    </div>
  );
} 