'use client'

import {
  XMarkIcon,
  DocumentTextIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ArrowDownTrayIcon,
  CheckIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface Invoice {
  id: number
  totalamount: number
  subtotal?: number
  taxrate?: number
  taxamount?: number
  status: string
  issuedate?: string
  duedate?: string
  paidat?: string
  description?: string
}

interface InvoiceViewModalProps {
  isOpen: boolean
  onClose: () => void
  invoice: Invoice
}

const getStatusColor = (status: string) => {
  if (!status) return 'bg-gray-100 text-gray-800 border-gray-200'
  switch (status.toLowerCase()) {
    case 'paid':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'overdue':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'draft':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'cancelled':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getStatusIcon = (status: string) => {
  if (!status) return ClockIcon
  switch (status.toLowerCase()) {
    case 'paid':
      return CheckIcon
    case 'pending':
      return ClockIcon
    case 'overdue':
      return ExclamationTriangleIcon
    case 'draft':
      return DocumentTextIcon
    case 'cancelled':
      return XMarkIcon
    default:
      return ClockIcon
  }
}

const formatCurrency = (amount: number) => {
  if (!amount && amount !== 0) return 'N/A'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getDaysUntilDue = (dueDate: string) => {
  const today = new Date()
  const due = new Date(dueDate)
  const diffTime = due.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

const handleDownloadPDF = async (invoiceId: number) => {
  try {
    const response = await fetch(`/api/invoices/${invoiceId}/pdf`, {
      method: 'GET',
    })

    if (response.ok) {
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = `invoice-${invoiceId}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } else {
      alert('Failed to download invoice PDF')
    }
  } catch (error) {
    console.error('Error downloading PDF:', error)
    alert('Error downloading invoice PDF')
  }
}

export default function InvoiceViewModal({
  isOpen,
  onClose,
  invoice
}: InvoiceViewModalProps) {
  if (!isOpen) return null

  const StatusIcon = getStatusIcon(invoice.status)
  const daysUntilDue = invoice.duedate ? getDaysUntilDue(invoice.duedate) : null
  const isOverdue = daysUntilDue !== null && daysUntilDue < 0 && invoice.status?.toLowerCase() !== 'paid'

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Background overlay */}
      <div className="fixed inset-0 bg-gray-500 bg-opacity-75" />

      {/* Modal container */}
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20">
        <div
          className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full mx-auto p-6"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <DocumentTextIcon className="h-6 w-6 text-blue-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Invoice Details</h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="space-y-6">
            {/* Invoice Header */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-lg font-medium text-gray-900">Invoice #{invoice.id}</h4>
                <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(invoice.status)}`}>
                  <StatusIcon className="h-4 w-4 inline mr-1" />
                  {invoice.status}
                </div>
              </div>
              {isOverdue && daysUntilDue && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-3">
                  <div className="flex items-center">
                    <ExclamationTriangleIcon className="h-4 w-4 text-red-500 mr-2" />
                    <p className="text-sm text-red-700 font-medium">
                      This invoice is {Math.abs(daysUntilDue)} days overdue
                    </p>
                  </div>
                </div>
              )}
              {invoice.description && (
                <p className="text-sm text-gray-600">{invoice.description}</p>
              )}
            </div>

            {/* Invoice Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Dates */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Dates</label>
                <div className="space-y-2">
                  {invoice.issuedate && (
                    <div className="flex items-center text-sm">
                      <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-gray-600">Issued: </span>
                      <span className="ml-1 text-gray-900">{formatDate(invoice.issuedate)}</span>
                    </div>
                  )}
                  {invoice.duedate && (
                    <div className="flex items-center text-sm">
                      <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-gray-600">Due: </span>
                      <span className={`ml-1 ${isOverdue ? 'text-red-600 font-medium' : 'text-gray-900'}`}>
                        {formatDate(invoice.duedate)}
                      </span>
                    </div>
                  )}
                  {invoice.status?.toLowerCase() === 'paid' && invoice.paidat && (
                    <div className="flex items-center text-sm">
                      <CheckIcon className="h-4 w-4 text-green-500 mr-2" />
                      <span className="text-gray-600">Paid: </span>
                      <span className="ml-1 text-green-600 font-medium">{formatDate(invoice.paidat)}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Amount Breakdown */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Amount Details</label>
                <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                  {invoice.subtotal && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Subtotal:</span>
                      <span className="text-gray-900">{formatCurrency(invoice.subtotal)}</span>
                    </div>
                  )}
                  {invoice.taxamount && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">
                        Tax {invoice.taxrate ? `(${invoice.taxrate}%)` : ''}:
                      </span>
                      <span className="text-gray-900">{formatCurrency(invoice.taxamount)}</span>
                    </div>
                  )}
                  <div className="flex justify-between text-sm font-medium border-t pt-2">
                    <span className="text-gray-900">Total:</span>
                    <span className="text-gray-900">{formatCurrency(invoice.totalamount)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-4 border-t border-gray-200">
              <span className="text-sm text-gray-500">Invoice #{invoice.id}</span>
              <div className="flex space-x-3">
                <button
                  onClick={() => handleDownloadPDF(invoice.id)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
                  Download PDF
                </button>
                {invoice.status?.toLowerCase() !== 'paid' && (
                  <button className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                    Pay Now
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
