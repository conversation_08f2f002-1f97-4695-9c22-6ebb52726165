'use client';

import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { TechnologyFilters } from '@/types/technology';

interface TechnologyFiltersProps {
  filters: TechnologyFilters;
  onFiltersChange: (filters: TechnologyFilters) => void;
  categories: readonly string[];
  proficiencyLevels: readonly string[];
}

export function TechnologyFiltersComponent({ 
  filters, 
  onFiltersChange, 
  categories, 
  proficiencyLevels 
}: TechnologyFiltersProps) {
  const handleSearchChange = (value: string) => {
    onFiltersChange({ ...filters, searchTerm: value });
  };

  const handleCategoryChange = (value: string) => {
    onFiltersChange({ ...filters, selectedCategory: value });
  };

  const handleProficiencyChange = (value: string) => {
    onFiltersChange({ ...filters, selectedProficiency: value });
  };

  return (
    <section className="py-8 bg-gray-50" role="search" aria-label="Filter technologies">
      <div className="container">
        <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            <input
              type="text"
              placeholder="Search technologies..."
              value={filters.searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              aria-label="Search technologies by name, description, or tags"
            />
          </div>

          {/* Filters */}
          <div className="flex gap-4">
            <select
              value={filters.selectedCategory}
              onChange={(e) => handleCategoryChange(e.target.value)}
              className="block pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              aria-label="Filter by technology category"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            <select
              value={filters.selectedProficiency}
              onChange={(e) => handleProficiencyChange(e.target.value)}
              className="block pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              aria-label="Filter by proficiency level"
            >
              {proficiencyLevels.map(level => (
                <option key={level} value={level}>
                  {level}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </section>
  );
} 