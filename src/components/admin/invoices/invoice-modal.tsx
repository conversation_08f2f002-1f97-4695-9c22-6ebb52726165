'use client'

import React, { useState, useEffect } from 'react'

interface InvoiceModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
  title: string
  initialData?: any
  fields?: any[]
  layout?: any
}

export function InvoiceModal({
  isOpen,
  onClose,
  onSubmit,
  title,
  initialData
}: InvoiceModalProps) {
  const [formData, setFormData] = useState({
    duedate: initialData?.duedate || '',
    subtotal: Number(initialData?.subtotal) || 0,
    taxrate: Number(initialData?.taxrate) || 0,
    taxamount: Number(initialData?.taxamount) || 0,
    totalamount: Number(initialData?.totalamount) || 0,
    status: initialData?.status || 'DRAFT',
    description: initialData?.description || '',
    clientid: initialData?.clientid || '',
    contid: initialData?.contid || '',
    orderid: initialData?.orderid || '',
    projectid: initialData?.projectid || '',
    paidat: initialData?.paidat || '',
  })

  const [clients, setClients] = useState([])
  const [contracts, setContracts] = useState([])
  const [orders, setOrders] = useState([])
  const [projects, setProjects] = useState([])
  const [loading, setLoading] = useState(false)
  const [loadingOrders, setLoadingOrders] = useState(false)
  const [loadingProjects, setLoadingProjects] = useState(false)
  const [loadingContracts, setLoadingContracts] = useState(false)

  // Load initial clients data
  useEffect(() => {
    const loadClients = async () => {
      try {
        setLoading(true)

        // Load all clients with sorting
        const clientsResponse = await fetch('/api/admin/clients?limit=1000&sortBy=companyName&sortOrder=asc')
        if (clientsResponse.ok) {
          const clientsData = await clientsResponse.json()
          console.log('Clients data loaded:', clientsData)
          // Sort alphabetically by company name
          const sortedClients = (clientsData.data || []).sort((a: any, b: any) => {
            const nameA = (a.companyName || '').toLowerCase()
            const nameB = (b.companyName || '').toLowerCase()
            return nameA.localeCompare(nameB)
          })
          setClients(sortedClients)
        }
      } catch (error) {
        console.error('Error loading clients:', error)
      } finally {
        setLoading(false)
      }
    }

    if (isOpen) {
      loadClients()
    }
  }, [isOpen])

  // Load orders when client changes
  useEffect(() => {
    const loadOrdersForClient = async () => {
      if (!formData.clientid) {
        setOrders([])
        setProjects([])
        setContracts([])
        return
      }

      try {
        setLoadingOrders(true)
        const ordersResponse = await fetch(`/api/admin/orders?clientId=${formData.clientid}&limit=1000&sortBy=ordertitle&sortOrder=asc`)
        if (ordersResponse.ok) {
          const ordersData = await ordersResponse.json()
          console.log('Orders data loaded for client:', ordersData)
          // Sort alphabetically by order title
          const sortedOrders = (ordersData.data || []).sort((a: any, b: any) => {
            const titleA = (a.ordertitle || '').toLowerCase()
            const titleB = (b.ordertitle || '').toLowerCase()
            return titleA.localeCompare(titleB)
          })
          setOrders(sortedOrders)
        }
      } catch (error) {
        console.error('Error loading orders for client:', error)
        setOrders([])
      } finally {
        setLoadingOrders(false)
      }
    }

    loadOrdersForClient()
  }, [formData.clientid])

  // Load projects when order changes
  useEffect(() => {
    const loadProjectsForOrder = async () => {
      if (!formData.orderid) {
        setProjects([])
        setContracts([])
        return
      }

      try {
        setLoadingProjects(true)
        const projectsResponse = await fetch(`/api/admin/projects?orderId=${formData.orderid}&limit=1000&sortBy=name&sortOrder=asc`)
        if (projectsResponse.ok) {
          const projectsData = await projectsResponse.json()
          console.log('Projects data loaded for order:', projectsData)
          // Sort alphabetically by project name
          const sortedProjects = (projectsData.data || []).sort((a: any, b: any) => {
            const nameA = (a.name || '').toLowerCase()
            const nameB = (b.name || '').toLowerCase()
            return nameA.localeCompare(nameB)
          })
          setProjects(sortedProjects)
        }
      } catch (error) {
        console.error('Error loading projects for order:', error)
        setProjects([])
      } finally {
        setLoadingProjects(false)
      }
    }

    loadProjectsForOrder()
  }, [formData.orderid])

  // Load contracts when project changes
  useEffect(() => {
    const loadContractsForProject = async () => {
      if (!formData.projectid) {
        setContracts([])
        return
      }

      try {
        setLoadingContracts(true)
        const contractsResponse = await fetch(`/api/admin/contracts?projectId=${formData.projectid}&limit=1000&sortBy=contname&sortOrder=asc`)
        if (contractsResponse.ok) {
          const contractsData = await contractsResponse.json()
          console.log('Contracts data loaded for project:', contractsData)
          // Sort alphabetically by contract name
          const sortedContracts = (contractsData.data || []).sort((a: any, b: any) => {
            const nameA = (a.contname || '').toLowerCase()
            const nameB = (b.contname || '').toLowerCase()
            return nameA.localeCompare(nameB)
          })
          setContracts(sortedContracts)
        }
      } catch (error) {
        console.error('Error loading contracts for project:', error)
        setContracts([])
      } finally {
        setLoadingContracts(false)
      }
    }

    loadContractsForProject()
  }, [formData.projectid])

  // Handle client selection and reset dependent fields
  const handleClientChange = (clientId: string) => {
    setFormData(prev => ({
      ...prev,
      clientid: clientId,
      orderid: '', // Reset order when client changes
      projectid: '', // Reset project when client changes
      contid: '' // Reset contract when client changes
    }))
  }

  // Handle order selection and reset dependent fields
  const handleOrderChange = (orderId: string) => {
    setFormData(prev => ({
      ...prev,
      orderid: orderId,
      projectid: '', // Reset project when order changes
      contid: '' // Reset contract when order changes
    }))
  }

  // Handle project selection and reset dependent fields
  const handleProjectChange = (projectId: string) => {
    setFormData(prev => ({
      ...prev,
      projectid: projectId,
      contid: '' // Reset contract when project changes
    }))
  }

  // Update form data when initialData changes
  useEffect(() => {
    if (initialData) {
      console.log('Invoice Modal - Loading initial data:', initialData)
      setFormData({
        duedate: initialData.duedate ? new Date(initialData.duedate).toISOString().split('T')[0] : '',
        subtotal: Number(initialData.subtotal) || 0,
        taxrate: Number(initialData.taxrate) || 0,
        taxamount: Number(initialData.taxamount) || 0,
        totalamount: Number(initialData.totalamount) || 0,
        status: initialData.status || 'DRAFT',
        description: initialData.description || '',
        clientid: initialData.clientid || '',
        contid: initialData.contid || '',
        orderid: initialData.orderid || '',
        projectid: initialData.projectid || '',
        paidat: initialData.paidat ? new Date(initialData.paidat).toISOString().split('T')[0] : '',
      })
    } else {
      // Reset form for create mode
      setFormData({
        duedate: '',
        subtotal: 0,
        taxrate: 0,
        taxamount: 0,
        totalamount: 0,
        status: 'DRAFT',
        description: '',
        clientid: '',
        contid: '',
        orderid: '',
        projectid: '',
        paidat: '',
      })
    }
  }, [initialData])

  // Auto-calculate tax amount and total when subtotal or tax rate changes
  useEffect(() => {
    const subtotal = formData.subtotal || 0
    const taxrate = formData.taxrate || 0
    const taxamount = (subtotal * taxrate) / 100
    const totalamount = subtotal + taxamount

    setFormData(prev => ({
      ...prev,
      taxamount,
      totalamount
    }))
  }, [formData.subtotal, formData.taxrate])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      // Transform form data to match API expectations
      const submitData = {
        duedate: formData.duedate,
        subtotal: formData.subtotal,
        taxrate: formData.taxrate,
        taxamount: formData.taxamount,
        totalamount: formData.totalamount,
        status: formData.status,
        description: formData.description,
        clientid: formData.clientid,
        contid: formData.contid,
        orderid: formData.orderid,
        projectid: formData.projectid || null,
        paidat: formData.paidat || null,
      }

      await onSubmit(submitData)
      onClose()
    } catch (error) {
      console.error('Submit error:', error)
      alert('Failed to save invoice. Please try again.')
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm" onClick={onClose} />

        <div className="relative bg-white rounded-xl shadow-2xl max-w-5xl w-full max-h-[95vh] overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-white">{title}</h2>
              <button
                type="button"
                onClick={onClose}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Form Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Main Content Grid */}
              <div className="grid grid-cols-12 gap-6">

                {/* Left Column - Invoice Details */}
                <div className="col-span-8 space-y-4">
                  {/* Basic Invoice Information */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Invoice Information
                    </h3>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Due Date *
                        </label>
                        <input
                          type="date"
                          required
                          value={formData.duedate}
                          onChange={(e) => setFormData({ ...formData, duedate: e.target.value })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Status *
                        </label>
                        <div className="relative">
                          <select
                            required
                            value={formData.status}
                            onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white cursor-pointer hover:border-gray-400 transition-colors appearance-none pr-8"
                            style={{
                              backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                              backgroundPosition: 'right 0.5rem center',
                              backgroundRepeat: 'no-repeat',
                              backgroundSize: '1.5em 1.5em'
                            }}
                          >
                            <option value="DRAFT">Draft</option>
                            <option value="SENT">Sent</option>
                            <option value="PAID">Paid</option>
                            <option value="OVERDUE">Overdue</option>
                            <option value="CANCELLED">Cancelled</option>
                          </select>
                        </div>
                      </div>
                    </div>

                    <div className="mt-3">
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Description
                      </label>
                      <textarea
                        rows={2}
                        value={formData.description}
                        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Invoice description or notes"
                      />
                    </div>
                  </div>

                  {/* Client & Project Details Card */}
                  <div className="bg-green-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6" />
                      </svg>
                      Client & Project Information
                    </h3>

                    {/* Top Row: Client (left) and Order (right) */}
                    <div className="grid grid-cols-2 gap-3 mb-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Client *
                        </label>
                        <div className="relative">
                          <select
                            required
                            value={formData.clientid}
                            onChange={(e) => handleClientChange(e.target.value)}
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white cursor-pointer hover:border-gray-400 transition-colors appearance-none pr-8"
                            style={{
                              backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                              backgroundPosition: 'right 0.5rem center',
                              backgroundRepeat: 'no-repeat',
                              backgroundSize: '1.5em 1.5em'
                            }}
                          >
                            <option value="" disabled>Select Client</option>
                            {clients.map((client: any) => (
                              <option key={client.id} value={client.id}>
                                {client.companyName} - {client.contactName}
                              </option>
                            ))}
                          </select>
                        </div>
                        {loading && (
                          <div className="text-xs text-gray-500 mt-1">Loading clients...</div>
                        )}
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Order *
                        </label>
                        <div className="relative">
                          <select
                            required
                            value={formData.orderid}
                            onChange={(e) => handleOrderChange(e.target.value)}
                            disabled={!formData.clientid || loadingOrders}
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white cursor-pointer hover:border-gray-400 transition-colors appearance-none pr-8 disabled:bg-gray-100 disabled:cursor-not-allowed disabled:text-gray-500"
                            style={{
                              backgroundImage: !(!formData.clientid || loadingOrders) ? `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")` : 'none',
                              backgroundPosition: 'right 0.5rem center',
                              backgroundRepeat: 'no-repeat',
                              backgroundSize: '1.5em 1.5em'
                            }}
                          >
                            <option value="" disabled>
                              {!formData.clientid ? 'Select Client First' : 'Select Order'}
                            </option>
                            {orders.map((order: any) => (
                              <option key={order.id} value={order.id}>
                                {order.ordertitle}
                              </option>
                            ))}
                          </select>
                        </div>
                        {loadingOrders && (
                          <div className="text-xs text-gray-500 mt-1">Loading orders...</div>
                        )}
                        {!formData.clientid && (
                          <div className="text-xs text-gray-400 mt-1">Select a client to see orders</div>
                        )}
                      </div>
                    </div>

                    {/* Bottom Row: Project (left) and Contract (right) */}
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Project (Optional)
                        </label>
                        <div className="relative">
                          <select
                            value={formData.projectid}
                            onChange={(e) => handleProjectChange(e.target.value)}
                            disabled={!formData.orderid || loadingProjects}
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white cursor-pointer hover:border-gray-400 transition-colors appearance-none pr-8 disabled:bg-gray-100 disabled:cursor-not-allowed disabled:text-gray-500"
                            style={{
                              backgroundImage: !(!formData.orderid || loadingProjects) ? `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")` : 'none',
                              backgroundPosition: 'right 0.5rem center',
                              backgroundRepeat: 'no-repeat',
                              backgroundSize: '1.5em 1.5em'
                            }}
                          >
                            <option value="" disabled>
                              {!formData.orderid ? 'Select Order First' : 'Select Project (Optional)'}
                            </option>
                            {projects.map((project: any) => (
                              <option key={project.id} value={project.id}>
                                {project.name}
                              </option>
                            ))}
                          </select>
                        </div>
                        {loadingProjects && (
                          <div className="text-xs text-gray-500 mt-1">Loading projects...</div>
                        )}
                        {!formData.orderid && (
                          <div className="text-xs text-gray-400 mt-1">Select an order to see projects</div>
                        )}
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Contract *
                        </label>
                        <div className="relative">
                          <select
                            required
                            value={formData.contid}
                            onChange={(e) => setFormData({ ...formData, contid: e.target.value })}
                            disabled={!formData.projectid || loadingContracts}
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white cursor-pointer hover:border-gray-400 transition-colors appearance-none pr-8 disabled:bg-gray-100 disabled:cursor-not-allowed disabled:text-gray-500"
                            style={{
                              backgroundImage: !(!formData.projectid || loadingContracts) ? `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")` : 'none',
                              backgroundPosition: 'right 0.5rem center',
                              backgroundRepeat: 'no-repeat',
                              backgroundSize: '1.5em 1.5em'
                            }}
                          >
                            <option value="" disabled>
                              {!formData.projectid ? 'Select Project First' : 'Select Contract'}
                            </option>
                            {contracts.map((contract: any) => (
                              <option key={contract.id} value={contract.id}>
                                {contract.contname}
                              </option>
                            ))}
                          </select>
                        </div>
                        {loadingContracts && (
                          <div className="text-xs text-gray-500 mt-1">Loading contracts...</div>
                        )}
                        {!formData.projectid && (
                          <div className="text-xs text-gray-400 mt-1">Select a project to see contracts</div>
                        )}
                      </div>
                    </div>

                    {/* Selection Summary */}
                    {(formData.clientid || formData.orderid || formData.projectid || formData.contid) && (
                      <div className="mt-3 p-2 bg-white rounded border border-green-200">
                        <div className="text-xs font-medium text-gray-700 mb-1">Selection Summary:</div>
                        <div className="text-xs text-gray-600 space-y-1">
                          {formData.clientid && (
                            <div>✓ Client: {clients.find(c => c.id == formData.clientid)?.companyName}</div>
                          )}
                          {formData.orderid && (
                            <div>✓ Order: {orders.find(o => o.id == formData.orderid)?.ordertitle}</div>
                          )}
                          {formData.projectid && (
                            <div>✓ Project: {projects.find(p => p.id == formData.projectid)?.name}</div>
                          )}
                          {formData.contid && (
                            <div>✓ Contract: {contracts.find(c => c.id == formData.contid)?.contname}</div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Financial Details Card */}
                  <div className="bg-purple-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                      Financial Details
                    </h3>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Subtotal *
                        </label>
                        <input
                          type="number"
                          required
                          min="0"
                          step="0.01"
                          value={formData.subtotal}
                          onChange={(e) => setFormData({ ...formData, subtotal: Number(e.target.value) || 0 })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="0.00"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Tax Rate (%)
                        </label>
                        <input
                          type="number"
                          min="0"
                          max="100"
                          step="0.01"
                          value={formData.taxrate}
                          onChange={(e) => setFormData({ ...formData, taxrate: Number(e.target.value) || 0 })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="0.00"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3 mt-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Tax Amount (Auto-calculated)
                        </label>
                        <input
                          type="number"
                          value={formData.taxamount.toFixed(2)}
                          readOnly
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Total Amount (Auto-calculated)
                        </label>
                        <input
                          type="number"
                          value={formData.totalamount.toFixed(2)}
                          readOnly
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-gray-50 text-gray-600 font-medium"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right Column - Payment & Status */}
                <div className="col-span-4 space-y-4">
                  {/* Payment Information Card */}
                  <div className="bg-orange-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                      Payment Information
                    </h3>

                    <div className="space-y-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Payment Date (Optional)
                        </label>
                        <input
                          type="date"
                          value={formData.paidat}
                          onChange={(e) => setFormData({ ...formData, paidat: e.target.value })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      {/* Payment Status Indicator */}
                      <div className="bg-white rounded-md p-3 border border-orange-200">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium text-gray-700">Payment Status:</span>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            formData.paidat
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {formData.paidat ? 'Paid' : 'Unpaid'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Invoice Summary Card */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                      Invoice Summary
                    </h3>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Subtotal:</span>
                        <span className="font-medium">${formData.subtotal.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tax ({formData.taxrate}%):</span>
                        <span className="font-medium">${formData.taxamount.toFixed(2)}</span>
                      </div>
                      <div className="border-t border-blue-200 pt-2 flex justify-between">
                        <span className="font-semibold text-gray-900">Total:</span>
                        <span className="font-bold text-blue-600">${formData.totalamount.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Status Information Card */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Status Information
                    </h3>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-600">Current Status:</span>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          formData.status === 'PAID' ? 'bg-green-100 text-green-800' :
                          formData.status === 'SENT' ? 'bg-blue-100 text-blue-800' :
                          formData.status === 'OVERDUE' ? 'bg-red-100 text-red-800' :
                          formData.status === 'CANCELLED' ? 'bg-gray-100 text-gray-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {formData.status}
                        </span>
                      </div>

                      {formData.duedate && (
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600">Due Date:</span>
                          <span className="text-xs font-medium">
                            {new Date(formData.duedate).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                <div className="text-xs text-gray-500">
                  {initialData ? 'Last updated: ' + new Date(initialData.updatedat || Date.now()).toLocaleDateString() : 'Creating new invoice'}
                </div>
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    disabled={loading}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-6 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-md hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 transition-all duration-200 shadow-sm"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                        Processing...
                      </>
                    ) : (
                      <>
                        {initialData ? (
                          <>
                            <svg className="h-4 w-4 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Update Invoice
                          </>
                        ) : (
                          <>
                            <svg className="h-4 w-4 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            Create Invoice
                          </>
                        )}
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}
