'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { XMarkIcon, ArrowDownTrayIcon, PaperAirplaneIcon, CheckIcon } from '@heroicons/react/24/outline'

interface Invoice {
  id: string
  invoiceNumber: string
  client: {
    companyName: string
    contactName: string
    contactEmail: string
  }
  project?: {
    name: string
    status: string
  }
  totalAmount: number
  subtotal: number
  taxAmount: number
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED'
  issueDate: string
  dueDate: string
  paidAt?: string
  description?: string
  items: Array<{
    id: string
    description: string
    quantity: number
    unitPrice: number
    totalPrice: number
  }>
  payments: Array<{
    id: string
    amount: number
    status: string
    paidAt?: string
  }>
}

interface InvoiceViewModalProps {
  isOpen: boolean
  onClose: () => void
  invoice: Invoice
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'DRAFT':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'SENT':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'PAID':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'OVERDUE':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'CANCELLED':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getDaysUntilDue = (dueDate: string) => {
  const today = new Date()
  const due = new Date(dueDate)
  const diffTime = due.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

export default function InvoiceViewModal({
  isOpen,
  onClose,
  invoice
}: InvoiceViewModalProps) {
  if (!isOpen) return null

  const daysUntilDue = getDaysUntilDue(invoice.dueDate)
  const isOverdue = daysUntilDue < 0 && invoice.status !== 'PAID'
  const totalPaid = invoice.payments.reduce((sum, payment) => 
    payment.status === 'COMPLETED' ? sum + Number(payment.amount) : sum, 0
  )

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen px-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-bold text-white">Invoice Details</h2>
                  <p className="text-blue-100">{invoice.invoiceNumber}</p>
                </div>
                <div className="flex items-center space-x-3">
                  <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(invoice.status)}`}>
                    {invoice.status}
                  </div>
                  <button
                    type="button"
                    onClick={onClose}
                    className="text-white hover:text-gray-200 transition-colors"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
              {/* Invoice Header */}
              <div className="grid grid-cols-2 gap-8 mb-8">
                {/* Client Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Bill To</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="text-lg font-medium text-gray-900">
                      {invoice.client.companyName}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {invoice.client.contactName}
                    </div>
                    <div className="text-sm text-gray-600">
                      {invoice.client.contactEmail}
                    </div>
                  </div>
                </div>

                {/* Invoice Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Invoice Information</h3>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Issue Date:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {formatDate(invoice.issueDate)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Due Date:</span>
                      <span className={`text-sm font-medium ${isOverdue ? 'text-red-600' : 'text-gray-900'}`}>
                        {formatDate(invoice.dueDate)}
                        {isOverdue && (
                          <span className="ml-1 text-red-500">
                            ({Math.abs(daysUntilDue)} days overdue)
                          </span>
                        )}
                      </span>
                    </div>
                    {invoice.project && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Project:</span>
                        <span className="text-sm font-medium text-gray-900">
                          {invoice.project.name}
                        </span>
                      </div>
                    )}
                    {invoice.status === 'PAID' && invoice.paidAt && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Paid Date:</span>
                        <span className="text-sm font-medium text-green-600">
                          {formatDate(invoice.paidAt)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Description */}
              {invoice.description && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-700">{invoice.description}</p>
                  </div>
                </div>
              )}

              {/* Invoice Items */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Invoice Items</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Description
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Quantity
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Unit Price
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {invoice.items.map((item, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {item.description}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {item.quantity}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrency(item.unitPrice)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {formatCurrency(item.totalPrice)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Totals */}
              <div className="bg-gray-50 rounded-lg p-6 mb-6">
                <div className="max-w-sm ml-auto space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-700">Subtotal:</span>
                    <span className="text-sm font-medium text-gray-900">
                      {formatCurrency(invoice.subtotal)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-700">Tax:</span>
                    <span className="text-sm font-medium text-gray-900">
                      {formatCurrency(invoice.taxAmount)}
                    </span>
                  </div>
                  <div className="flex justify-between border-t pt-3">
                    <span className="text-lg font-bold text-gray-900">Total:</span>
                    <span className="text-lg font-bold text-gray-900">
                      {formatCurrency(invoice.totalAmount)}
                    </span>
                  </div>
                  {totalPaid > 0 && (
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-green-700">Paid:</span>
                      <span className="text-sm font-medium text-green-700">
                        {formatCurrency(totalPaid)}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Payments */}
              {invoice.payments.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment History</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="space-y-2">
                      {invoice.payments.map((payment, index) => (
                        <div key={index} className="flex justify-between items-center">
                          <div>
                            <span className="text-sm font-medium text-gray-900">
                              {formatCurrency(Number(payment.amount))}
                            </span>
                            <span className="ml-2 text-xs text-gray-500">
                              {payment.paidAt && formatDate(payment.paidAt)}
                            </span>
                          </div>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                            payment.status === 'COMPLETED' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {payment.status}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-between items-center pt-6 border-t">
                <div className="text-sm text-gray-500">
                  Last updated: {formatDate(invoice.issueDate)}
                </div>
                <div className="flex space-x-3">
                  <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                    Download PDF
                  </button>
                  {invoice.status === 'DRAFT' && (
                    <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                      <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                      Send Invoice
                    </button>
                  )}
                  {(invoice.status === 'SENT' || invoice.status === 'OVERDUE') && (
                    <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                      <CheckIcon className="h-4 w-4 mr-2" />
                      Mark as Paid
                    </button>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}
