'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import InvoiceCard from './invoice-card'
import { CheckIcon, TrashIcon, PencilIcon, EyeIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline'

interface Invoice {
  id: string
  invoiceNumber: string
  client: {
    companyName: string
    contactName: string
    contactEmail: string
  }
  project?: {
    name: string
    status: string
  }
  totalAmount: number
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED'
  issueDate: string
  dueDate: string
  paidAt?: string
  items: Array<{
    id: string
    description: string
    quantity: number
    unitPrice: number
    totalPrice: number
  }>
  payments: Array<{
    id: string
    amount: number
    status: string
    paidAt?: string
  }>
}

interface InvoiceGridProps {
  invoices: Invoice[]
  loading?: boolean
  onView: (invoice: Invoice) => void
  onEdit: (invoice: Invoice) => void
  onDelete: (id: string) => void
  onDownload: (invoice: Invoice) => void
  onSend: (invoice: Invoice) => void
  onMarkPaid: (invoice: Invoice) => void
}

export default function InvoiceGrid({
  invoices,
  loading = false,
  onView,
  onEdit,
  onDelete,
  onDownload,
  onSend,
  onMarkPaid,
  selectedInvoices = [],
  onSelectInvoice = () => {},
}: InvoiceGridProps & { selectedInvoices?: string[], onSelectInvoice?: (id: string) => void }) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 px-2">
        {[...Array(6)].map((_, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 animate-pulse h-40" />
        ))}
      </div>
    )
  }

  if (invoices.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No invoices found</h3>
        <p className="text-gray-500">Get started by creating your first invoice.</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 px-2">
      {invoices.map((invoice) => (
        <div
          key={invoice.id}
          className={`rounded-lg p-4 hover:bg-gray-100 transition-colors border-2 h-full flex flex-col justify-between ${selectedInvoices.includes(String(invoice.id)) ? 'bg-blue-50 border-blue-500' : 'bg-gray-50 border-gray-200'}`}
        >
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-medium text-lg">
              {invoice.client.companyName.charAt(0)}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-semibold text-gray-900 truncate">{invoice.invoiceNumber}</h3>
              <p className="text-xs text-gray-600 truncate">{invoice.client.companyName}</p>
            </div>
            <input
              type="checkbox"
              checked={selectedInvoices.includes(String(invoice.id))}
              onChange={() => onSelectInvoice(String(invoice.id))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
          </div>
          <div className="space-y-2 text-xs text-gray-600 mb-2">
            <div className="flex items-center justify-between">
              <span>Amount:</span>
              <span className="truncate ml-2 font-semibold text-gray-900">{invoice.totalAmount.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Status:</span>
              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${invoice.status === 'PAID' ? 'bg-green-100 text-green-800' : invoice.status === 'SENT' ? 'bg-blue-100 text-blue-800' : invoice.status === 'OVERDUE' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}`}>{invoice.status}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Due:</span>
              <span className={invoice.status === 'OVERDUE' ? 'text-red-600 font-semibold' : 'font-medium'}>{invoice.dueDate}</span>
            </div>
          </div>
          <div className="flex items-center justify-center space-x-1 mt-3 pt-3 border-t border-gray-200">
            <button onClick={() => onView(invoice)} className="p-1.5 rounded-md text-blue-600 hover:text-blue-800 hover:bg-blue-50 transition-all duration-200" title="View"><EyeIcon className="w-4 h-4" /></button>
            <button onClick={() => onEdit(invoice)} className="p-1.5 rounded-md text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50 transition-all duration-200" title="Edit"><PencilIcon className="w-4 h-4" /></button>
            <button onClick={() => onDownload(invoice)} className="p-1.5 rounded-md text-green-600 hover:text-green-800 hover:bg-green-50 transition-all duration-200" title="Download"><ArrowDownTrayIcon className="w-4 h-4" /></button>
            <button onClick={() => onDelete(invoice.id)} className="p-1.5 rounded-md text-red-600 hover:text-red-800 hover:bg-red-50 transition-all duration-200" title="Delete"><TrashIcon className="w-4 h-4" /></button>
            {(invoice.status === 'SENT' || invoice.status === 'OVERDUE') && (
              <button onClick={() => onMarkPaid(invoice)} className="p-1.5 rounded-md text-green-700 hover:text-green-900 hover:bg-green-50 transition-all duration-200" title="Mark Paid"><CheckIcon className="w-4 h-4" /></button>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}
