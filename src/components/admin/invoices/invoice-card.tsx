'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  PaperAirplaneIcon,
  CheckIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import { ClientAvatar } from '../clients/client-avatar'

interface Invoice {
  id: string
  invoiceNumber: string
  client: {
    companyName: string
    contactName: string
    contactEmail: string
    logoUrl?: string
  }
  project?: {
    name: string
    status: string
  }
  totalAmount: number
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED'
  issueDate: string
  dueDate: string
  paidAt?: string
  items: Array<{
    id: string
    description: string
    quantity: number
    unitPrice: number
    totalPrice: number
  }>
  payments: Array<{
    id: string
    amount: number
    status: string
    paidAt?: string
  }>
}

interface InvoiceCardProps {
  invoice: Invoice
  onView: (invoice: Invoice) => void
  onEdit: (invoice: Invoice) => void
  onDelete: (id: string) => void
  onDownload: (invoice: Invoice) => void
  onSend: (invoice: Invoice) => void
  onMarkPaid: (invoice: Invoice) => void
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'DRAFT':
      return DocumentTextIcon
    case 'SENT':
      return PaperAirplaneIcon
    case 'PAID':
      return CheckIcon
    case 'OVERDUE':
      return ExclamationTriangleIcon
    case 'CANCELLED':
      return TrashIcon
    default:
      return ClockIcon
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'DRAFT':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'SENT':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'PAID':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'OVERDUE':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'CANCELLED':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getDaysUntilDue = (dueDate: string) => {
  const today = new Date()
  const due = new Date(dueDate)
  const diffTime = due.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

export default function InvoiceCard({
  invoice,
  onView,
  onEdit,
  onDelete,
  onDownload,
  onSend,
  onMarkPaid
}: InvoiceCardProps) {
  const [isLoading, setIsLoading] = useState(false)
  const StatusIcon = getStatusIcon(invoice.status)
  const daysUntilDue = getDaysUntilDue(invoice.dueDate)
  const isOverdue = daysUntilDue < 0 && invoice.status !== 'PAID'
  const totalPaid = invoice.payments.reduce((sum, payment) =>
    payment.status === 'COMPLETED' ? sum + Number(payment.amount) : sum, 0
  )

  const handleAction = async (action: () => void) => {
    setIsLoading(true)
    try {
      await action()
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200"
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center border ${getStatusColor(invoice.status)}`}>
              <StatusIcon className="w-5 h-5" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {invoice.invoiceNumber}
              </h3>
              <p className="text-sm text-gray-600">
                {invoice.client.companyName}
              </p>
              {invoice.project && (
                <p className="text-xs text-gray-500">
                  Project: {invoice.project.name}
                </p>
              )}
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-xl font-bold text-gray-900">
              {formatCurrency(invoice.totalAmount)}
            </div>
            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
              {invoice.status}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Issue Date:</span>
            <div className="font-medium">{formatDate(invoice.issueDate)}</div>
          </div>
          <div>
            <span className="text-gray-500">Due Date:</span>
            <div className={`font-medium ${isOverdue ? 'text-red-600' : ''}`}>
              {formatDate(invoice.dueDate)}
              {isOverdue && (
                <span className="ml-1 text-red-500">
                  ({Math.abs(daysUntilDue)} days overdue)
                </span>
              )}
            </div>
          </div>
          <div>
            <span className="text-gray-500">Items:</span>
            <div className="font-medium">{invoice.items.length}</div>
          </div>
          <div>
            <span className="text-gray-500">Payments:</span>
            <div className="font-medium">
              {totalPaid > 0 ? formatCurrency(totalPaid) : 'None'}
            </div>
          </div>
        </div>

        {invoice.status === 'PAID' && invoice.paidAt && (
          <div className="mt-3 p-2 bg-green-50 rounded-md">
            <div className="text-sm text-green-800">
              Paid on {formatDate(invoice.paidAt)}
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="px-4 py-3 bg-gray-50 rounded-b-lg">
        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            <button
              onClick={() => handleAction(() => onView(invoice))}
              disabled={isLoading}
              className="p-2 text-gray-400 hover:text-blue-600 transition-colors disabled:opacity-50"
              title="View Details"
            >
              <EyeIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => handleAction(() => onEdit(invoice))}
              disabled={isLoading}
              className="p-2 text-gray-400 hover:text-blue-600 transition-colors disabled:opacity-50"
              title="Edit"
            >
              <PencilIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => handleAction(() => onDownload(invoice))}
              disabled={isLoading}
              className="p-2 text-gray-400 hover:text-green-600 transition-colors disabled:opacity-50"
              title="Download PDF"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
            </button>
          </div>

          <div className="flex space-x-2">
            {invoice.status === 'DRAFT' && (
              <button
                onClick={() => handleAction(() => onSend(invoice))}
                disabled={isLoading}
                className="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 transition-colors disabled:opacity-50"
              >
                Send
              </button>
            )}
            {(invoice.status === 'SENT' || invoice.status === 'OVERDUE') && (
              <button
                onClick={() => handleAction(() => onMarkPaid(invoice))}
                disabled={isLoading}
                className="px-3 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-md hover:bg-green-200 transition-colors disabled:opacity-50"
              >
                Mark Paid
              </button>
            )}
            <button
              onClick={() => handleAction(() => onDelete(invoice.id))}
              disabled={isLoading}
              className="p-2 text-gray-400 hover:text-red-600 transition-colors disabled:opacity-50"
              title="Delete"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export function InvoiceCardFull({
  invoice,
  selected,
  onSelect,
  onView,
  onEdit,
  onDelete,
  onDownload,
  onSend,
  onMarkPaid
}: InvoiceCardProps & { selected?: boolean, onSelect?: () => void }) {
  const StatusIcon = getStatusIcon(invoice.status)
  const daysUntilDue = getDaysUntilDue(invoice.dueDate)
  const isOverdue = daysUntilDue < 0 && invoice.status !== 'PAID'
  const totalPaid = invoice.payments.reduce((sum, payment) => payment.status === 'COMPLETED' ? sum + Number(payment.amount) : sum, 0)

  // Prepare client object for avatar
  const clientLogoUrl = (invoice.client as any).logoUrl || undefined
  const clientName = invoice.client.companyName

  return (
    <div className={`rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300 border-2 min-h-[320px] flex ${selected ? 'bg-blue-50 border-blue-500' : 'bg-white border-gray-100 hover:border-blue-200'}`}>
      {/* Sidebar */}
      <div className="flex-shrink-0 w-56 relative bg-gradient-to-br from-blue-500 to-purple-600 flex flex-col items-center justify-center py-8 gap-4">
        {clientLogoUrl ? (
          <img
            src={clientLogoUrl}
            alt={clientName + ' logo'}
            className="w-28 h-28 object-contain rounded-2xl bg-white shadow border-2 border-gray-200 mb-2"
          />
        ) : (
          <div className="w-28 h-28 flex items-center justify-center rounded-2xl bg-blue-100 mb-2">
            <svg className="w-16 h-16 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a4 4 0 004 4h10a4 4 0 004-4V7a4 4 0 00-4-4H7a4 4 0 00-4 4z" />
            </svg>
          </div>
        )}
        <div className="text-white text-3xl font-bold mb-1 mt-2">{invoice.invoiceNumber}</div>
        <div className="text-white text-lg font-semibold text-center px-2 break-words">{invoice.client.companyName}</div>
        {/* Checkbox Overlay */}
        {onSelect && (
          <div className="absolute top-4 left-4">
            <input
              type="checkbox"
              checked={selected}
              onChange={onSelect}
              className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded shadow-lg"
            />
          </div>
        )}
        {/* Status Badge Overlay */}
        <div className="absolute top-4 right-4">
          <span className={`inline-flex px-3 py-1.5 text-xs font-bold rounded-full shadow-lg ${invoice.status === 'PAID' ? 'bg-green-500 text-white' : invoice.status === 'SENT' ? 'bg-blue-500 text-white' : invoice.status === 'OVERDUE' ? 'bg-red-500 text-white' : 'bg-gray-500 text-white'}`}>{invoice.status}</span>
        </div>
      </div>
      {/* Content */}
      <div className="flex-1 p-8 flex flex-col justify-between min-w-0">
        {/* Header */}
        <div>
          <div className="mb-6">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">{invoice.client.companyName}</h3>
            <p className="text-xl text-blue-600 font-semibold">{invoice.totalAmount.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}</p>
          </div>
          {/* Details */}
          <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <div className="flex items-center"><span className="font-bold text-gray-800 w-24">Due:</span><span className={isOverdue ? 'text-red-600 font-semibold ml-2' : 'font-medium ml-2'}>{invoice.dueDate}</span></div>
              <div className="flex items-center"><span className="font-bold text-gray-800 w-24">Issued:</span><span className="ml-2">{invoice.issueDate}</span></div>
              <div className="flex items-center"><span className="font-bold text-gray-800 w-24">Status:</span><span className={`ml-2 inline-flex px-2 py-1 text-xs font-medium rounded-full ${invoice.status === 'PAID' ? 'bg-green-100 text-green-800' : invoice.status === 'SENT' ? 'bg-blue-100 text-blue-800' : invoice.status === 'OVERDUE' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}`}>{invoice.status}</span></div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center"><span className="font-bold text-gray-800 w-24">Items:</span><span className="ml-2">{invoice.items.length}</span></div>
              <div className="flex items-center"><span className="font-bold text-gray-800 w-24">Payments:</span><span className="ml-2">{totalPaid > 0 ? totalPaid.toLocaleString('en-US', { style: 'currency', currency: 'USD' }) : 'None'}</span></div>
              {invoice.status === 'PAID' && invoice.paidAt && <div className="flex items-center"><span className="font-bold text-gray-800 w-24">Paid:</span><span className="ml-2 text-green-700">{invoice.paidAt}</span></div>}
            </div>
          </div>
        </div>
        {/* Actions */}
        <div className="flex items-center gap-2 mt-4">
          <button onClick={() => onView(invoice)} className="p-2 rounded-md text-blue-600 hover:text-blue-800 hover:bg-blue-50 transition-all duration-200" title="View"><EyeIcon className="w-5 h-5" /></button>
          <button onClick={() => onEdit(invoice)} className="p-2 rounded-md text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50 transition-all duration-200" title="Edit"><PencilIcon className="w-5 h-5" /></button>
          <button onClick={() => onDownload(invoice)} className="p-2 rounded-md text-green-600 hover:text-green-800 hover:bg-green-50 transition-all duration-200" title="Download"><ArrowDownTrayIcon className="w-5 h-5" /></button>
          <button onClick={() => onDelete(invoice.id)} className="p-2 rounded-md text-red-600 hover:text-red-800 hover:bg-red-50 transition-all duration-200" title="Delete"><TrashIcon className="w-5 h-5" /></button>
          {(invoice.status === 'SENT' || invoice.status === 'OVERDUE') && (
            <button onClick={() => onMarkPaid(invoice)} className="p-2 rounded-md text-green-700 hover:text-green-900 hover:bg-green-50 transition-all duration-200" title="Mark Paid"><CheckIcon className="w-5 h-5" /></button>
          )}
        </div>
      </div>
    </div>
  )
}
