'use client'

import React, { useState, useRef, useEffect } from 'react'
import RichTextEditor from '@/components/ui/rich-text-editor'
import FileAttachment, { AttachmentFile } from '../contact-forms/file-attachment'

interface ChatInputProps {
  onSendMessage: (message: string, attachments: AttachmentFile[]) => Promise<void>
  placeholder?: string
  disabled?: boolean
  className?: string
  showRichText?: boolean
  maxFiles?: number
  onTypingStart?: () => void
  onTypingStop?: () => void
  replyingTo?: {
    id: string | number
    message: string
    sender: string
  } | null
  onCancelReply?: () => void
}

export function ChatInput({
  onSendMessage,
  placeholder = "Type your message...",
  disabled = false,
  className = '',
  showRichText = false,
  maxFiles = 5,
  onTypingStart,
  onTypingStop,
  replyingTo,
  onCancelReply
}: ChatInputProps) {
  const [message, setMessage] = useState('')
  const [attachments, setAttachments] = useState<AttachmentFile[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout>()

  // Handle typing indicators
  useEffect(() => {
    if (message.trim() && !isTyping) {
      setIsTyping(true)
      onTypingStart?.()
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false)
        onTypingStop?.()
      }
    }, 1000)

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }
    }
  }, [message, isTyping, onTypingStart, onTypingStop])

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current && !showRichText) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`
    }
  }, [message, showRichText])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!message.trim() && attachments.length === 0) return
    if (isSubmitting) return

    setIsSubmitting(true)
    
    try {
      await onSendMessage(message.trim(), attachments)
      setMessage('')
      setAttachments([])
      
      // Stop typing indicator
      if (isTyping) {
        setIsTyping(false)
        onTypingStop?.()
      }
    } catch (error) {
      console.error('Failed to send message:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !showRichText) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const canSend = (message.trim() || attachments.length > 0) && !isSubmitting

  return (
    <div className={`border-t border-gray-200 bg-white ${className}`}>
      {/* Reply Context */}
      {replyingTo && (
        <div className="px-4 py-2 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
              </svg>
              <div>
                <p className="text-xs text-gray-500">Replying to {replyingTo.sender}</p>
                <p className="text-sm text-gray-700 truncate max-w-md">{replyingTo.message}</p>
              </div>
            </div>
            <button
              onClick={onCancelReply}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="p-4">
        <div className="flex flex-col space-y-2">
          {/* Message Input */}
          <div className="relative">
            {showRichText ? (
              <RichTextEditor
                content={message}
                onChange={setMessage}
                placeholder={placeholder}
                disabled={disabled || isSubmitting}
                className="min-h-[80px]"
              />
            ) : (
              <textarea
                ref={textareaRef}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={placeholder}
                disabled={disabled || isSubmitting}
                rows={1}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed resize-none"
              />
            )}
          </div>

          {/* Attachments and Send Button */}
          <div className="flex items-end space-x-2">
            {/* File Attachment */}
            <div className="flex-1">
              <FileAttachment
                attachments={attachments}
                onAttachmentsChange={setAttachments}
                disabled={disabled || isSubmitting}
                maxFiles={maxFiles}
                className="border-0"
              />
            </div>

            {/* Send Button */}
            <button
              type="submit"
              disabled={!canSend || disabled}
              className="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex-shrink-0"
              title={isSubmitting ? 'Sending...' : 'Send Message'}
            >
              {isSubmitting ? (
                <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              )}
            </button>
          </div>

          {/* Typing indicator for current user */}
          {isTyping && (
            <div className="text-xs text-gray-500 italic">
              Typing...
            </div>
          )}
        </div>
      </form>
    </div>
  )
}
