'use client'

import React from 'react'
import { <PERSON>rudProvider } from './crud-context'
import { CrudHeader } from './crud-header'
import { CrudDataView } from './crud-data-view'
import { CrudPagination } from './crud-pagination'
import { CrudFormModal } from './crud-form-modal'
import { CrudConfig } from './types'

interface CrudManagerProps<T> {
  config: CrudConfig<T>
}

export function CrudManager<T>({ config }: CrudManagerProps<T>) {
  return (
    <CrudProvider config={config}>
      <div className="space-y-6">
        <CrudHeader config={config} />
        <CrudDataView config={config} />
        <CrudPagination config={config} />
        <CrudFormModal config={config} />
      </div>
    </CrudProvider>
  )
}
