import { CrudConfig } from '../types'

interface Project {
  id: string
  name: string
  description: string
  status: 'PLANNING' | 'IN_PROGRESS' | 'COMPLETED' | 'ON_HOLD' | 'CANCELLED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  startDate: string
  completionDate?: string
  budget?: number
  actualCost?: number
  clientId: string
  client?: {
    companyName: string
  }
  technologies: string[]
  teamMembers: string[]
  progress: number
  isPublic: boolean
  repositoryUrl?: string
  liveUrl?: string
  createdAt: string
  updatedAt: string
}

export const projectsConfig: CrudConfig<Project> = {
  title: 'Projects',
  description: 'Manage client projects and track progress',
  endpoint: 'projects',
  
  columns: [
    {
      key: 'name',
      label: 'Project',
      sortable: true,
      searchable: true,
      renderType: 'project'
    },
    {
      key: 'client',
      label: 'Client',
      sortable: true,
      renderType: 'text'
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => {
        const statusColors: Record<string, string> = {
          PLANNING: 'bg-blue-100 text-blue-800',
          IN_PROGRESS: 'bg-yellow-100 text-yellow-800',
          COMPLETED: 'bg-green-100 text-green-800',
          ON_HOLD: 'bg-gray-100 text-gray-800',
          CANCELLED: 'bg-red-100 text-red-800'
        }
        return (
          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${statusColors[value] || 'bg-gray-100 text-gray-800'}`}>
            {value.replace('_', ' ')}
          </span>
        )
      }
    },
    {
      key: 'priority',
      label: 'Priority',
      sortable: true,
      render: (value) => {
        const priorityColors: Record<string, string> = {
          LOW: 'bg-gray-100 text-gray-800',
          MEDIUM: 'bg-blue-100 text-blue-800',
          HIGH: 'bg-orange-100 text-orange-800',
          URGENT: 'bg-red-100 text-red-800'
        }
        return (
          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${priorityColors[value] || 'bg-gray-100 text-gray-800'}`}>
            {value}
          </span>
        )
      }
    },
    {
      key: 'progress',
      label: 'Progress',
      renderType: 'progress'
    },
    {
      key: 'budget',
      label: 'Budget',
      sortable: true,
      renderType: 'currency'
    },
    {
      key: 'completionDate',
      label: 'Due Date',
      sortable: true,
      renderType: 'overdue'
    }
  ],

  fields: [
    {
      name: 'name',
      label: 'Project Name',
      type: 'text',
      required: true,
      placeholder: 'Enter project name',
      validation: {
        min: 3,
        max: 100
      }
    },
    {
      name: 'slug',
      label: 'URL Slug',
      type: 'text',
      placeholder: 'Auto-generated from name if empty',
      description: 'SEO-friendly URL identifier'
    },
    {
      name: 'description',
      label: 'Description',
      type: 'textarea',
      required: true,
      placeholder: 'Describe the project...',
      validation: {
        min: 10,
        max: 1000
      }
    },
    {
      name: 'excerpt',
      label: 'Short Excerpt',
      type: 'textarea',
      placeholder: 'Brief summary for cards and previews',
      validation: {
        max: 200
      }
    },
    {
      name: 'clientId',
      label: 'Client',
      type: 'select',
      required: true,
      options: [] // This would be populated dynamically from the clients API
    },
    {
      name: 'status',
      label: 'Status',
      type: 'select',
      required: true,
      defaultValue: 'PLANNING',
      options: [
        { value: 'PLANNING', label: 'Planning' },
        { value: 'IN_PROGRESS', label: 'In Progress' },
        { value: 'COMPLETED', label: 'Completed' },
        { value: 'ON_HOLD', label: 'On Hold' },
        { value: 'CANCELLED', label: 'Cancelled' }
      ]
    },
    {
      name: 'priority',
      label: 'Priority',
      type: 'select',
      required: true,
      defaultValue: 'MEDIUM',
      options: [
        { value: 'LOW', label: 'Low' },
        { value: 'MEDIUM', label: 'Medium' },
        { value: 'HIGH', label: 'High' },
        { value: 'URGENT', label: 'Urgent' }
      ]
    },
    {
      name: 'startDate',
      label: 'Start Date',
      type: 'date',
      required: true
    },
    {
      name: 'completionDate',
      label: 'Completion Date',
      type: 'date',
      description: 'Expected or actual completion date'
    },
    {
      name: 'budget',
      label: 'Budget',
      type: 'number',
      placeholder: '50000',
      description: 'Project budget in USD'
    },
    {
      name: 'actualCost',
      label: 'Actual Cost',
      type: 'number',
      placeholder: '45000',
      description: 'Actual project cost in USD'
    },
    {
      name: 'progress',
      label: 'Progress (%)',
      type: 'number',
      defaultValue: 0,
      validation: {
        min: 0,
        max: 100
      }
    },
    {
      name: 'imageUrl',
      label: 'Project Image URL',
      type: 'text',
      placeholder: 'https://example.com/project-image.jpg',
      description: 'Main project image for portfolio display'
    },
    {
      name: 'projectUrl',
      label: 'Live Project URL',
      type: 'text',
      placeholder: 'https://project.com'
    },
    {
      name: 'githubUrl',
      label: 'GitHub Repository URL',
      type: 'text',
      placeholder: 'https://github.com/company/project'
    },
    {
      name: 'tags',
      label: 'Project Tags',
      type: 'text',
      placeholder: 'web, mobile, api (comma-separated)',
      description: 'Comma-separated tags for categorization'
    },
    {
      name: 'isPublic',
      label: 'Public Project',
      type: 'checkbox',
      defaultValue: false,
      description: 'Show this project in the public portfolio'
    },
    {
      name: 'isFeatured',
      label: 'Featured Project',
      type: 'checkbox',
      defaultValue: false,
      description: 'Feature this project on the homepage'
    },
    {
      name: 'displayOrder',
      label: 'Display Order',
      type: 'number',
      defaultValue: 0,
      description: 'Order for displaying projects (lower numbers first)'
    }
  ],

  actions: [
    {
      label: 'View',
      icon: 'eye',
      action: 'view',
      variant: 'secondary'
    },
    {
      label: 'Edit',
      icon: 'edit',
      action: 'edit',
      variant: 'primary'
    },
    {
      label: 'Delete',
      icon: 'delete',
      action: 'delete',
      variant: 'danger',
      requiresConfirmation: true
    }
  ],

  bulkActions: [
    {
      label: 'Mark as Completed',
      action: 'complete',
      variant: 'success'
    },
    {
      label: 'Put on Hold',
      action: 'hold',
      variant: 'warning'
    }
  ],

  filters: [
    {
      name: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'PLANNING', label: 'Planning' },
        { value: 'IN_PROGRESS', label: 'In Progress' },
        { value: 'COMPLETED', label: 'Completed' },
        { value: 'ON_HOLD', label: 'On Hold' },
        { value: 'CANCELLED', label: 'Cancelled' }
      ]
    },
    {
      name: 'priority',
      label: 'Priority',
      type: 'select',
      options: [
        { value: 'LOW', label: 'Low' },
        { value: 'MEDIUM', label: 'Medium' },
        { value: 'HIGH', label: 'High' },
        { value: 'URGENT', label: 'Urgent' }
      ]
    },
    {
      name: 'isPublic',
      label: 'Visibility',
      type: 'select',
      options: [
        { value: 'true', label: 'Public' },
        { value: 'false', label: 'Private' }
      ]
    },
    {
      name: 'completionDate',
      label: 'Due Date',
      type: 'date-range'
    }
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search projects by name, description, or client...',
  defaultSort: { field: 'createdAt', order: 'desc' },
  pageSize: 12,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true
}
