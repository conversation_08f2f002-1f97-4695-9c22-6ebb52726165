'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { <PERSON>rudField } from './types'
import { LoadingSpinner } from '../ui/loading-spinner'

interface CrudFormProps {
  fields: CrudField[]
  initialData?: any
  onSubmit: (data: any) => Promise<void>
  onCancel: () => void
  title: string
  submitLabel?: string
  isLoading?: boolean
}

export function CrudForm({
  fields,
  initialData,
  onSubmit,
  onCancel,
  title,
  submitLabel = 'Save',
  isLoading = false
}: CrudFormProps) {
  const [formData, setFormData] = useState<any>({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Initialize form data
  useEffect(() => {
    const initialFormData: any = {}
    fields.forEach(field => {
      if (initialData && initialData[field.name] !== undefined) {
        initialFormData[field.name] = initialData[field.name]
      } else if (field.defaultValue !== undefined) {
        initialFormData[field.name] = field.defaultValue
      } else {
        // Set default values based on field type
        switch (field.type) {
          case 'checkbox':
            initialFormData[field.name] = false
            break
          case 'number':
            initialFormData[field.name] = 0
            break
          default:
            initialFormData[field.name] = ''
        }
      }
    })
    setFormData(initialFormData)
  }, [fields, initialData])

  const validateField = (field: CrudField, value: any): string | null => {
    // Required validation
    if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      return `${field.label} is required`
    }

    // Skip other validations if field is empty and not required
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return null
    }

    // Type-specific validations
    if (field.type === 'email' && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(value)) {
        return 'Please enter a valid email address'
      }
    }

    if (field.type === 'number' && value !== undefined) {
      const numValue = Number(value)
      if (isNaN(numValue)) {
        return 'Please enter a valid number'
      }
      if (field.validation?.min !== undefined && numValue < field.validation.min) {
        return `Value must be at least ${field.validation.min}`
      }
      if (field.validation?.max !== undefined && numValue > field.validation.max) {
        return `Value must be at most ${field.validation.max}`
      }
    }

    // Length validations for text fields
    if (typeof value === 'string') {
      if (field.validation?.min !== undefined && value.length < field.validation.min) {
        return `${field.label} must be at least ${field.validation.min} characters`
      }
      if (field.validation?.max !== undefined && value.length > field.validation.max) {
        return `${field.label} must be at most ${field.validation.max} characters`
      }
    }

    // Pattern validation
    if (field.validation?.pattern && typeof value === 'string') {
      const regex = new RegExp(field.validation.pattern)
      if (!regex.test(value)) {
        return `${field.label} format is invalid`
      }
    }

    // Custom validation
    if (field.validation?.custom) {
      return field.validation.custom(value)
    }

    return null
  }

  const handleInputChange = (field: CrudField, value: any) => {
    setFormData((prev: any) => ({ ...prev, [field.name]: value }))
    
    // Clear error when user starts typing
    if (errors[field.name]) {
      setErrors(prev => ({ ...prev, [field.name]: '' }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate all fields
    const newErrors: Record<string, string> = {}
    fields.forEach(field => {
      if (field.hidden || (field.showWhen && !field.showWhen(formData))) return
      
      const error = validateField(field, formData[field.name])
      if (error) {
        newErrors[field.name] = error
      }
    })

    setErrors(newErrors)

    if (Object.keys(newErrors).length > 0) {
      return
    }

    try {
      setIsSubmitting(true)
      await onSubmit(formData)
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderField = (field: CrudField) => {
    if (field.hidden || (field.showWhen && !field.showWhen(formData))) {
      return null
    }

    const value = formData[field.name] || ''
    const error = errors[field.name]
    const isDisabled = field.disabled || isSubmitting

    const baseInputClasses = `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
      error 
        ? 'border-red-300 bg-red-50' 
        : 'border-gray-300 focus:border-blue-500'
    } ${isDisabled ? 'bg-gray-100 cursor-not-allowed' : ''}`

    switch (field.type) {
      case 'textarea':
        return (
          <div key={field.name} className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <textarea
              value={value}
              onChange={(e) => handleInputChange(field, e.target.value)}
              placeholder={field.placeholder}
              disabled={isDisabled}
              rows={4}
              className={baseInputClasses}
            />
            {field.description && (
              <p className="text-sm text-gray-500">{field.description}</p>
            )}
            {error && <p className="text-sm text-red-600">{error}</p>}
          </div>
        )

      case 'select':
        return (
          <div key={field.name} className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <select
              value={value}
              onChange={(e) => handleInputChange(field, e.target.value)}
              disabled={isDisabled}
              className={baseInputClasses}
            >
              <option value="">Select {field.label}</option>
              {field.options?.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {field.description && (
              <p className="text-sm text-gray-500">{field.description}</p>
            )}
            {error && <p className="text-sm text-red-600">{error}</p>}
          </div>
        )

      case 'checkbox':
        return (
          <div key={field.name} className="space-y-2">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={value}
                onChange={(e) => handleInputChange(field, e.target.checked)}
                disabled={isDisabled}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="text-sm font-medium text-gray-700">
                {field.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </label>
            </div>
            {field.description && (
              <p className="text-sm text-gray-500">{field.description}</p>
            )}
            {error && <p className="text-sm text-red-600">{error}</p>}
          </div>
        )

      case 'date':
        return (
          <div key={field.name} className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <input
              type="date"
              value={value ? new Date(value).toISOString().split('T')[0] : ''}
              onChange={(e) => handleInputChange(field, e.target.value)}
              disabled={isDisabled}
              className={baseInputClasses}
            />
            {field.description && (
              <p className="text-sm text-gray-500">{field.description}</p>
            )}
            {error && <p className="text-sm text-red-600">{error}</p>}
          </div>
        )

      case 'file':
        return (
          <div key={field.name} className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <input
              type="file"
              onChange={(e) => {
                const file = e.target.files?.[0]
                if (file) {
                  handleInputChange(field, file)
                }
              }}
              disabled={isDisabled}
              className={baseInputClasses}
            />
            {field.description && (
              <p className="text-sm text-gray-500">{field.description}</p>
            )}
            {error && <p className="text-sm text-red-600">{error}</p>}
          </div>
        )

      default:
        return (
          <div key={field.name} className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <input
              type={field.type}
              value={value}
              onChange={(e) => handleInputChange(field, e.target.value)}
              placeholder={field.placeholder}
              disabled={isDisabled}
              className={baseInputClasses}
            />
            {field.description && (
              <p className="text-sm text-gray-500">{field.description}</p>
            )}
            {error && <p className="text-sm text-red-600">{error}</p>}
          </div>
        )
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden"
      >
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          <button
            onClick={onCancel}
            disabled={isSubmitting}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:cursor-not-allowed"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="flex flex-col h-full">
          <div className="flex-1 overflow-y-auto p-6 space-y-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <LoadingSpinner size="lg" />
              </div>
            ) : (
              fields.map(renderField)
            )}
          </div>

          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
            <button
              type="button"
              onClick={onCancel}
              disabled={isSubmitting}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isSubmitting && <LoadingSpinner size="sm" />}
              <span>{submitLabel}</span>
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  )
}
