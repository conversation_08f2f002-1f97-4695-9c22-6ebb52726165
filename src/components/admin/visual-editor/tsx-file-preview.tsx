'use client'

import React, { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'

interface HighlightedText {
  id: string
  text: string
  startLine: number
  endLine: number
  startColumn: number
  endColumn: number
  filePath: string
}

interface TsxFilePreviewProps {
  content: string
  filePath: string
  onTextSelect: (selectedText: string, startLine: number, endLine: number, startColumn: number, endColumn: number) => void
  highlightedText: HighlightedText | null
  onInlineEdit?: (lineNumber: number, newText: string) => void
}

export default function TsxFilePreview({
  content,
  filePath,
  onTextSelect,
  highlightedText,
  onInlineEdit
}: TsxFilePreviewProps) {
  const [selectedRange, setSelectedRange] = useState<{
    startLine: number
    endLine: number
    startColumn: number
    endColumn: number
    text: string
  } | null>(null)
  const [editingLine, setEditingLine] = useState<number | null>(null)
  const [editingText, setEditingText] = useState('')
  const previewRef = useRef<HTMLDivElement>(null)

  const lines = content.split('\n')

  const handleLineDoubleClick = (lineNumber: number, lineText: string) => {
    if (onInlineEdit) {
      setEditingLine(lineNumber)
      setEditingText(lineText)
    }
  }

  const handleInlineEditSave = () => {
    if (editingLine !== null && onInlineEdit) {
      onInlineEdit(editingLine, editingText)
      setEditingLine(null)
      setEditingText('')
    }
  }

  const handleInlineEditCancel = () => {
    setEditingLine(null)
    setEditingText('')
  }

  const handleTextSelection = () => {
    const selection = window.getSelection()
    if (!selection || selection.isCollapsed || !previewRef.current) return

    const range = selection.getRangeAt(0)
    const selectedText = selection.toString().trim()
    
    if (!selectedText) return

    // Find the line numbers and column positions
    const preElement = previewRef.current.querySelector('pre')
    if (!preElement) return

    const textContent = preElement.textContent || ''
    const startOffset = textContent.indexOf(selectedText)
    const endOffset = startOffset + selectedText.length

    if (startOffset === -1) return

    // Calculate line and column positions
    const beforeStart = textContent.substring(0, startOffset)
    const beforeEnd = textContent.substring(0, endOffset)
    
    const startLine = beforeStart.split('\n').length
    const endLine = beforeEnd.split('\n').length
    
    const startLineContent = beforeStart.split('\n').pop() || ''
    const endLineContent = beforeEnd.split('\n').pop() || ''
    
    const startColumn = startLineContent.length + 1
    const endColumn = endLineContent.length + 1

    setSelectedRange({
      startLine,
      endLine,
      startColumn,
      endColumn,
      text: selectedText
    })

    onTextSelect(selectedText, startLine, endLine, startColumn, endColumn)
  }

  const renderLineWithHighlight = (line: string, lineNumber: number) => {
    if (!highlightedText) return line

    const isHighlightedLine = lineNumber >= highlightedText.startLine && lineNumber <= highlightedText.endLine
    
    if (!isHighlightedLine) return line

    // If it's a single line highlight
    if (highlightedText.startLine === highlightedText.endLine) {
      const before = line.substring(0, highlightedText.startColumn - 1)
      const highlighted = line.substring(highlightedText.startColumn - 1, highlightedText.endColumn - 1)
      const after = line.substring(highlightedText.endColumn - 1)
      
      return (
        <>
          {before}
          <span className="bg-yellow-400 text-yellow-900 px-1 rounded font-semibold">{highlighted}</span>
          {after}
        </>
      )
    }
    
    // Multi-line highlight
    if (lineNumber === highlightedText.startLine) {
      const before = line.substring(0, highlightedText.startColumn - 1)
      const highlighted = line.substring(highlightedText.startColumn - 1)
      
      return (
        <>
          {before}
          <span className="bg-yellow-400 text-yellow-900 px-1 rounded font-semibold">{highlighted}</span>
        </>
      )
    }
    
    if (lineNumber === highlightedText.endLine) {
      const highlighted = line.substring(0, highlightedText.endColumn - 1)
      const after = line.substring(highlightedText.endColumn - 1)
      
      return (
        <>
          <span className="bg-yellow-400 text-yellow-900 px-1 rounded font-semibold">{highlighted}</span>
          {after}
        </>
      )
    }
    
    // Middle lines of multi-line highlight
    return <span className="bg-yellow-400 text-yellow-900 px-1 rounded font-semibold">{line}</span>
  }

  return (
    <div ref={previewRef} className="h-full flex flex-col">
      <div className="flex-1 overflow-auto bg-gray-900">
        <pre className="text-sm font-mono p-4 leading-relaxed select-text text-gray-100">
          <code>
            {lines.map((line, index) => (
              <div
                key={index}
                className="flex hover:bg-gray-800 transition-colors group"
                onMouseUp={handleTextSelection}
                onDoubleClick={() => handleLineDoubleClick(index + 1, line)}
              >
                <span className="text-gray-500 select-none w-12 text-right pr-4 flex-shrink-0 group-hover:text-gray-400">
                  {index + 1}
                </span>
                {editingLine === index + 1 ? (
                  <div className="flex-1 flex items-center">
                    <input
                      type="text"
                      value={editingText}
                      onChange={(e) => setEditingText(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleInlineEditSave()
                        } else if (e.key === 'Escape') {
                          handleInlineEditCancel()
                        }
                      }}
                      className="flex-1 bg-gray-800 text-gray-100 border border-blue-500 rounded px-2 py-1 text-sm font-mono focus:outline-none focus:ring-2 focus:ring-blue-500"
                      autoFocus
                    />
                    <div className="ml-2 flex gap-1">
                      <button
                        onClick={handleInlineEditSave}
                        className="px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700"
                      >
                        ✓
                      </button>
                      <button
                        onClick={handleInlineEditCancel}
                        className="px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700"
                      >
                        ✕
                      </button>
                    </div>
                  </div>
                ) : (
                  <span className="flex-1 whitespace-pre-wrap cursor-pointer">
                    {renderLineWithHighlight(line, index + 1)}
                  </span>
                )}
              </div>
            ))}
          </code>
        </pre>
      </div>
      
      {selectedRange && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-blue-900 border-t border-blue-700"
        >
          <div className="text-sm text-blue-100">
            <strong>Selected:</strong> Lines {selectedRange.startLine}-{selectedRange.endLine}, 
            Columns {selectedRange.startColumn}-{selectedRange.endColumn}
          </div>
          <div className="text-xs text-blue-200 mt-1 font-mono bg-gray-800 p-2 rounded border border-blue-600 max-h-20 overflow-y-auto">
            {selectedRange.text}
          </div>
        </motion.div>
      )}
    </div>
  )
}
