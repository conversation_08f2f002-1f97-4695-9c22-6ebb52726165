'use client'

import { motion, AnimatePresence } from 'framer-motion'
import {
  ClockIcon,
  DocumentTextIcon,
  PaintBrushIcon,
  ArrowsPointingOutIcon,
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  TrashIcon,
} from '@heroicons/react/24/outline'

interface ChangeHistory {
  id: string
  type: 'text' | 'style' | 'layout'
  description: string
  timestamp: Date
  elementId: string
  oldValue: string
  newValue: string
}

interface HistoryPanelProps {
  isVisible: boolean
  changeHistory: ChangeHistory[]
  onUndo: (changeId: string) => void
  onRedo: (changeId: string) => void
  onClearHistory: () => void
}

export default function HistoryPanel({
  isVisible,
  changeHistory,
  onUndo,
  onRedo,
  onClearHistory
}: HistoryPanelProps) {
  const getChangeIcon = (type: string) => {
    switch (type) {
      case 'text':
        return <DocumentTextIcon className="h-4 w-4" />
      case 'style':
        return <PaintBrushIcon className="h-4 w-4" />
      case 'layout':
        return <ArrowsPointingOutIcon className="h-4 w-4" />
      default:
        return <DocumentTextIcon className="h-4 w-4" />
    }
  }

  const getChangeColor = (type: string) => {
    switch (type) {
      case 'text':
        return 'text-blue-600 bg-blue-50'
      case 'style':
        return 'text-purple-600 bg-purple-50'
      case 'layout':
        return 'text-green-600 bg-green-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: 320 }}
          exit={{ width: 0 }}
          className="bg-white border-l border-gray-200 overflow-hidden"
        >
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <ClockIcon className="h-5 w-5 text-gray-500" />
                <h3 className="text-lg font-semibold text-gray-900">Change History</h3>
              </div>
              {changeHistory.length > 0 && (
                <button
                  onClick={onClearHistory}
                  className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                  title="Clear History"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              )}
            </div>
            <p className="text-sm text-gray-500 mt-1">
              {changeHistory.length} change{changeHistory.length !== 1 ? 's' : ''}
            </p>
          </div>

          <div className="flex-1 overflow-y-auto">
            {changeHistory.length === 0 ? (
              <div className="text-center text-gray-500 py-12">
                <ClockIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="font-medium">No changes yet</p>
                <p className="text-sm mt-1">Start editing to see your changes here</p>
              </div>
            ) : (
              <div className="p-4 space-y-3">
                {changeHistory.map((change, index) => (
                  <motion.div
                    key={change.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="group relative p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1">
                        <div className={`p-2 rounded-md ${getChangeColor(change.type)}`}>
                          {getChangeIcon(change.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">
                            {change.description}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            {change.timestamp.toLocaleTimeString()} • {change.timestamp.toLocaleDateString()}
                          </p>
                          <div className="mt-2 text-xs text-gray-600">
                            <div className="flex items-center space-x-2">
                              <span className="font-medium">Old:</span>
                              <span className="truncate">{change.oldValue}</span>
                            </div>
                            <div className="flex items-center space-x-2 mt-1">
                              <span className="font-medium">New:</span>
                              <span className="truncate">{change.newValue}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={() => onUndo(change.id)}
                          className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                          title="Undo this change"
                        >
                          <ArrowUturnLeftIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => onRedo(change.id)}
                          className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                          title="Redo this change"
                        >
                          <ArrowUturnRightIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>

          {changeHistory.length > 0 && (
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <div className="text-xs text-gray-500 text-center">
                Changes are automatically saved as drafts
              </div>
            </div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  )
} 