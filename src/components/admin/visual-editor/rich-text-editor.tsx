'use client'

import React, { useState, useRef, useEffect } from 'react'
import {
  BoldIcon,
  ItalicIcon,
  UnderlineIcon,
  StrikethroughIcon,
  CodeBracketIcon,
  ChatBubbleLeftRightIcon,
  ListBulletIcon,
  ArrowLeftIcon,
  ArrowUpIcon,
  ArrowRightIcon,
  LinkIcon,
  PhotoIcon,
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon
} from '@heroicons/react/24/outline'

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  rows?: number
  className?: string
}

export default function RichTextEditor({
  value,
  onChange,
  placeholder = "Enter your content here...",
  rows = 4,
  className = ""
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null)
  const [isFocused, setIsFocused] = useState(false)
  const [canUndo, setCanUndo] = useState(false)
  const [canRedo, setCanRedo] = useState(false)

  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.innerHTML = value
    }
  }, [value])

  const execCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value)
    editorRef.current?.focus()
    updateContent()
  }

  const updateContent = () => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML
      onChange(content)
      
      // Update undo/redo state
      setCanUndo(document.queryCommandEnabled('undo'))
      setCanRedo(document.queryCommandEnabled('redo'))
    }
  }

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault()
    const text = e.clipboardData.getData('text/plain')
    document.execCommand('insertText', false, text)
    updateContent()
  }

  const insertLink = () => {
    const url = prompt('Enter URL:')
    if (url) {
      execCommand('createLink', url)
    }
  }

  const insertImage = () => {
    const url = prompt('Enter image URL:')
    if (url) {
      execCommand('insertImage', url)
    }
  }

  const toolbarButtons = [
    {
      icon: BoldIcon,
      command: 'bold',
      title: 'Bold (Ctrl+B)',
      shortcut: 'Ctrl+B'
    },
    {
      icon: ItalicIcon,
      command: 'italic',
      title: 'Italic (Ctrl+I)',
      shortcut: 'Ctrl+I'
    },
    {
      icon: UnderlineIcon,
      command: 'underline',
      title: 'Underline (Ctrl+U)',
      shortcut: 'Ctrl+U'
    },
    {
      icon: StrikethroughIcon,
      command: 'strikeThrough',
      title: 'Strikethrough',
      shortcut: ''
    },
    { icon: CodeBracketIcon, command: 'formatBlock', value: '<pre>', title: 'Code Block' },
    { icon: ChatBubbleLeftRightIcon, command: 'formatBlock', value: '<blockquote>', title: 'Quote' },
    { icon: ListBulletIcon, command: 'insertUnorderedList', title: 'Bullet List' },
    { icon: ListBulletIcon, command: 'insertOrderedList', title: 'Numbered List' },
    { icon: ArrowLeftIcon, command: 'justifyLeft', title: 'Align Left' },
    { icon: ArrowUpIcon, command: 'justifyCenter', title: 'Align Center' },
    { icon: ArrowRightIcon, command: 'justifyRight', title: 'Align Right' },
    { icon: LinkIcon, command: 'custom', action: insertLink, title: 'Insert Link' },
    { icon: PhotoIcon, command: 'custom', action: insertImage, title: 'Insert Image' }
  ]

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-200 p-2 flex flex-wrap gap-1">
        {/* Undo/Redo */}
        <div className="flex gap-1 mr-2">
          <button
            onClick={() => execCommand('undo')}
            disabled={!canUndo}
            className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed"
            title="Undo (Ctrl+Z)"
          >
            <ArrowUturnLeftIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => execCommand('redo')}
            disabled={!canRedo}
            className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed"
            title="Redo (Ctrl+Y)"
          >
            <ArrowUturnRightIcon className="h-4 w-4" />
          </button>
        </div>

        {/* Divider */}
        <div className="w-px h-6 bg-gray-300 mx-1" />

        {/* Formatting buttons */}
        {toolbarButtons.map((button, index) => (
          <button
            key={index}
            onClick={() => button.action ? button.action() : execCommand(button.command, button.value)}
            className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded"
            title={button.title}
          >
            <button.icon className="h-4 w-4" />
          </button>
        ))}
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable
        onInput={updateContent}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        onPaste={handlePaste}
        className={`p-3 min-h-[${rows * 1.5}rem] text-sm focus:outline-none ${
          isFocused ? 'ring-2 ring-blue-500 ring-opacity-50' : ''
        }`}
        style={{ minHeight: `${rows * 1.5}rem` }}
        data-placeholder={placeholder}
        suppressContentEditableWarning
      />

      {/* Character/Word Count */}
      <div className="bg-gray-50 border-t border-gray-200 px-3 py-2 text-xs text-gray-500 flex justify-between">
        <span>{value.replace(/<[^>]*>/g, '').length} characters</span>
        <span>{value.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length} words</span>
      </div>
    </div>
  )
} 