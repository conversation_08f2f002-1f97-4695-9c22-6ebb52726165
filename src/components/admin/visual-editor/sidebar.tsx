'use client'

import React from 'react'
import { motion } from 'framer-motion'
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  ClockIcon,
  CloudArrowUpIcon,
  PlusIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
} from '@heroicons/react/24/outline'
import PageIcon from './page-icon'

interface PageData {
  id: string
  name: string
  path: string
  description: string
  lastModified: string
  status: 'draft' | 'published'
}

interface ChangeHistory {
  id: string
  type: 'text' | 'style' | 'layout'
  description: string
  timestamp: Date
  elementId: string
  oldValue: string
  newValue: string
}

interface SidebarProps {
  pages: PageData[]
  selectedPage: string
  onPageSelect: (pageId: string) => void
  collapsed: boolean
  onToggleCollapse: () => void
  showHistory: boolean
  onToggleHistory: () => void
  changeHistory: ChangeHistory[]
  onPublish: () => void
  isSaving: boolean
  draftMode: boolean
  onToggleDraftMode: () => void
}

export default function Sidebar({
  pages,
  selectedPage,
  onPageSelect,
  collapsed,
  onToggleCollapse,
  showHistory,
  onToggleHistory,
  changeHistory,
  onPublish,
  isSaving,
  draftMode,
  onToggleDraftMode
}: SidebarProps) {
  return (
    <motion.div
      initial={{ width: 280 }}
      animate={{ width: collapsed ? 80 : 280 }}
      className="bg-white border-r border-gray-200 flex flex-col"
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          {!collapsed && (
            <div>
              <h1 className="text-lg font-semibold text-gray-900">Visual Editor</h1>
              <p className="text-sm text-gray-500">Edit content visually</p>
            </div>
          )}
          <button
            onClick={onToggleCollapse}
            className="p-2 rounded-md hover:bg-gray-100 transition-colors"
          >
            {collapsed ? (
              <ChevronRightIcon className="h-5 w-5 text-gray-500" />
            ) : (
              <ChevronLeftIcon className="h-5 w-5 text-gray-500" />
            )}
          </button>
        </div>
      </div>

      {/* Pages List */}
      <div className="flex-1 overflow-y-auto p-4">
        {!collapsed && (
          <div className="mb-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-700">Pages</h3>
              <button className="p-1 text-gray-400 hover:text-gray-600">
                <PlusIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        )}
        <div className="space-y-2">
          {pages.map((page) => {
            console.log('Rendering page:', page.id)
            return (
              <button
                key={page.id}
                onClick={() => onPageSelect(page.id)}
                className={`w-full flex items-center p-3 rounded-lg text-left transition-colors ${
                  selectedPage === page.id
                    ? 'bg-blue-50 border border-blue-200 text-blue-700'
                    : 'hover:bg-gray-50 text-gray-700'
                }`}
              >
                              <PageIcon pageId={page.id} className="h-5 w-5 flex-shrink-0" />
                {!collapsed && (
                  <div className="ml-3 flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div className="font-medium text-sm truncate">{page.name}</div>
                      <div className={`w-2 h-2 rounded-full ${
                        page.status === 'published' ? 'bg-green-500' : 'bg-yellow-500'
                      }`} />
                    </div>
                    <div className="text-xs text-gray-500 truncate">{page.description}</div>
                    <div className="text-xs text-gray-400 mt-1">
                      {new Date(page.lastModified).toLocaleDateString()}
                    </div>
                  </div>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Footer Actions */}
      <div className="p-4 border-t border-gray-200">
        {!collapsed && (
          <div className="space-y-3">
            {/* Draft Mode Toggle */}
            <button
              onClick={onToggleDraftMode}
              className={`w-full flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                draftMode
                  ? 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {draftMode ? (
                <EyeSlashIcon className="h-4 w-4 mr-2" />
              ) : (
                <EyeIcon className="h-4 w-4 mr-2" />
              )}
              {draftMode ? 'Draft Mode' : 'Live Mode'}
            </button>

            {/* History Button */}
            <button
              onClick={onToggleHistory}
              className={`w-full flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                showHistory
                  ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <ClockIcon className="h-4 w-4 mr-2" />
              History ({changeHistory.length})
            </button>

            {/* Publish Button */}
            <button
              onClick={onPublish}
              disabled={isSaving || changeHistory.length === 0}
              className="w-full flex items-center justify-center px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <CloudArrowUpIcon className="h-4 w-4 mr-2" />
              {isSaving ? 'Publishing...' : 'Publish Changes'}
            </button>
          </div>
        )}
      </div>
    </motion.div>
  )
} 