'use client'

import React, { useState, useEffect, useRef } from 'react'
import { ContentField } from '@/app/api/content/content-manager'

interface ContentAwareIframeProps {
  pagePath: string
  onElementSelect: (element: any) => void
  onContentLoad: (content: Record<string, string>) => void
  interactiveMode: boolean
}

export default function ContentAwareIframe({
  pagePath,
  onElementSelect,
  onContentLoad,
  interactiveMode
}: ContentAwareIframeProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const [iframeLoaded, setIframeLoaded] = useState(false)
  const [contentFields, setContentFields] = useState<ContentField[]>([])
  const [contentMap, setContentMap] = useState<Record<string, string>>({})

  // Load content for the page
  useEffect(() => {
    const loadPageContent = async () => {
      try {
        console.log('Loading page content for:', pagePath)
        const response = await fetch(`/api/content/page-content?pagePath=${encodeURIComponent(pagePath)}`, {
          credentials: 'include'
        })

        console.log('Page content response status:', response.status)
        if (response.ok) {
          const data = await response.json()
          console.log('Page content data:', data)
          if (data.success) {
            setContentFields(data.data.content)
            setContentMap(data.data.contentMap)
            onContentLoad(data.data.contentMap)
          } else {
            console.error('Page content API returned error:', data.error)
          }
        } else {
          console.error('Page content API failed with status:', response.status)
        }
      } catch (error) {
        console.error('Error loading page content:', error)
      }
    }

    if (pagePath) {
      loadPageContent()
    }
  }, [pagePath, onContentLoad])

  // Inject content and interactive script when iframe loads
  useEffect(() => {
    if (iframeLoaded && contentFields.length > 0) {
      injectContentAndScript()
    }
  }, [iframeLoaded, contentFields])

  // Send interactive mode updates to iframe
  useEffect(() => {
    if (iframeRef.current && iframeRef.current.contentWindow && iframeLoaded) {
      console.log('Sending interactive mode update to iframe:', interactiveMode);
      iframeRef.current.contentWindow.postMessage({
        type: 'UPDATE_INTERACTIVE_MODE',
        interactiveMode: interactiveMode
      }, '*');
    }
  }, [interactiveMode, iframeLoaded])

  const injectContentAndScript = () => {
    const iframe = iframeRef.current
    if (!iframe || !iframe.contentWindow) {
      console.log('Iframe not ready for script injection')
      return
    }
    
    console.log('Injecting content and script into iframe...')
    console.log('Content map:', contentMap)
    console.log('Interactive mode:', interactiveMode)

    const script = `
      (function() {
        let interactiveMode = ${interactiveMode};
        let selectedElement = null;
        let hoveredElement = null;
        let contentMap = ${JSON.stringify(contentMap)};
        
        console.log('Iframe script initialized with interactiveMode:', interactiveMode);
        console.log('Iframe script initialized with contentMap:', contentMap);

        // Function to update content in the page
        function updatePageContent() {
          console.log('Updating page content with:', contentMap);
          
          // Update content based on the content map
          Object.keys(contentMap).forEach(fieldId => {
            const [section, key] = fieldId.split('-');
            const newValue = contentMap[fieldId];
            
            // Try to find elements that might contain this content
            const selectors = [
              \`[data-content-section="\${section}"][data-content-key="\${key}"]\`,
              \`[data-edit-id="\${fieldId}"]\`,
              \`.\${section}-content .\${key}\`,
              \`#\${fieldId}\`
            ];
            
            for (const selector of selectors) {
              const element = document.querySelector(selector);
              if (element && element.textContent !== newValue) {
                console.log('Updating element:', selector, 'with:', newValue);
                element.textContent = newValue;
                element.setAttribute('data-visual-editor-id', fieldId);
                break;
              }
            }
          });
        }

        // Function to enable interactive mode
        function enableInteractiveMode() {
          console.log('Enabling interactive mode...');
          interactiveMode = true;
          document.body.style.cursor = 'crosshair';
          document.addEventListener('click', handleElementClick, true);
          document.addEventListener('mouseover', handleElementHover);
          document.addEventListener('mouseout', handleElementMouseOut);
          
          // Add data attributes to editable elements
          addEditableAttributes();
          console.log('Interactive mode enabled');
        }

        // Function to disable interactive mode
        function disableInteractiveMode() {
          interactiveMode = false;
          document.body.style.cursor = 'default';
          document.removeEventListener('click', handleElementClick, true);
          document.removeEventListener('mouseover', handleElementHover);
          document.removeEventListener('mouseout', handleElementMouseOut);
          removeHoverEffects();
        }

        // Add data attributes to make elements identifiable
        function addEditableAttributes() {
          console.log('Adding editable attributes to elements...');
          console.log('Content map:', contentMap);
          
          // First, try to find elements with existing data attributes
          Object.keys(contentMap).forEach(fieldId => {
            const [section, key] = fieldId.split('-');
            const selectors = [
              \`[data-content-section="\${section}"][data-content-key="\${key}"]\`,
              \`[data-edit-id="\${fieldId}"]\`,
              \`.\${section}-content .\${key}\`,
              \`#\${fieldId}\`
            ];
            
            console.log('Looking for fieldId:', fieldId, 'with selectors:', selectors);
            
            for (const selector of selectors) {
              const element = document.querySelector(selector);
              if (element) {
                console.log('Found element for', fieldId, 'with selector:', selector);
                element.setAttribute('data-visual-editor-id', fieldId);
                element.setAttribute('data-editable', 'true');
                break;
              }
            }
          });
          
          // If no elements found with data attributes, try to find text elements that match the content
          Object.keys(contentMap).forEach(fieldId => {
            const content = contentMap[fieldId];
            if (!content) return;
            
            // Look for elements that contain this text
            const textElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div');
            textElements.forEach(element => {
              if (element.textContent && element.textContent.trim() === content.trim()) {
                console.log('Found text match for', fieldId, ':', element.textContent);
                element.setAttribute('data-visual-editor-id', fieldId);
                element.setAttribute('data-editable', 'true');
              }
            });
          });
          
          // Make ALL text elements editable for now (for testing)
          const allTextElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div');
          allTextElements.forEach((element, index) => {
            if (element.textContent && element.textContent.trim().length > 0) {
              const tempId = \`temp-\${index}\`;
              element.setAttribute('data-visual-editor-id', tempId);
              element.setAttribute('data-editable', 'true');
              console.log('Made element editable:', tempId, element.textContent.trim());
            }
          });
          
          console.log('Editable elements found:', document.querySelectorAll('[data-editable]').length);
        }

        // Handle element click for editing
        function handleElementClick(e) {
          console.log('Element clicked:', e.target);
          console.log('Interactive mode:', interactiveMode);
          
          if (!interactiveMode) {
            console.log('Interactive mode not enabled');
            return;
          }
          
          const element = e.target;
          console.log('Element has data-visual-editor-id:', element.hasAttribute('data-visual-editor-id'));
          console.log('Element has data-editable:', element.hasAttribute('data-editable'));
          
          if (element && element.hasAttribute('data-editable')) {
            e.preventDefault();
            e.stopPropagation();

            const fieldId = element.getAttribute('data-visual-editor-id');
            const rect = element.getBoundingClientRect();
            
            console.log('Sending element data to parent:', fieldId);
            
            const elementData = {
              id: fieldId,
              text: element.textContent.trim(),
              description: getElementDescription(element),
              filePath: window.location.pathname,
              rect: {
                left: rect.left,
                top: rect.top,
                width: rect.width,
                height: rect.height
              },
              className: element.className,
              tagName: element.tagName,
              selector: generateSelector(element)
            };

            window.parent.postMessage({
              type: 'ELEMENT_SELECTED',
              element: elementData
            }, '*');
          } else {
            console.log('Element not editable or missing data-editable attribute');
          }
        }

        // Handle element hover
        function handleElementHover(e) {
          if (!interactiveMode) return;
          const element = e.target;
          if (element && element.hasAttribute('data-editable')) {
            hoveredElement = element;
            element.style.outline = '2px solid #3b82f6';
            element.style.outlineOffset = '2px';
            element.style.cursor = 'pointer';
            console.log('Hovering over editable element:', element.textContent?.trim());
          }
        }

        // Handle element mouse out
        function handleElementMouseOut(e) {
          if (!interactiveMode) return;
          const element = e.target;
          if (element === hoveredElement) {
            element.style.outline = '';
            element.style.outlineOffset = '';
            element.style.cursor = '';
            hoveredElement = null;
          }
        }

        // Remove hover effects
        function removeHoverEffects() {
          const elements = document.querySelectorAll('[data-editable]');
          elements.forEach(el => {
            el.style.outline = '';
            el.style.outlineOffset = '';
            el.style.cursor = '';
          });
        }

        // Generate element description
        function getElementDescription(element) {
          const tagName = element.tagName.toLowerCase();
          const fieldId = element.getAttribute('data-visual-editor-id');
          if (fieldId) {
            const [section, key] = fieldId.split('-');
            return \`\${section} \${key}\`;
          }
          return tagName;
        }

        // Generate CSS selector
        function generateSelector(element) {
          if (element.id) return '#' + element.id;
          if (element.className) return '.' + element.className.split(' ').join('.');
          return element.tagName.toLowerCase();
        }

        // Update element text
        function updateElementText(elementId, newText) {
          console.log('Updating element:', elementId, 'with text:', newText);
          
          const element = document.querySelector(\`[data-visual-editor-id="\${elementId}"]\`);
          if (element) {
            console.log('Found element, updating text');
            element.textContent = newText;
            contentMap[elementId] = newText;
            console.log('Element updated successfully');
          } else {
            console.log('Element not found with ID:', elementId);
            console.log('Available elements with data-visual-editor-id:');
            document.querySelectorAll('[data-visual-editor-id]').forEach(el => {
              console.log('-', el.getAttribute('data-visual-editor-id'), ':', el.textContent?.trim());
            });
          }
        }

        // Listen for messages from parent
        window.addEventListener('message', function(event) {
          console.log('Content-aware iframe received message:', event.data);
          
          if (event.data.type === 'UPDATE_ELEMENT_TEXT') {
            console.log('Processing UPDATE_ELEMENT_TEXT message');
            updateElementText(event.data.elementId, event.data.newText);
          } else if (event.data.type === 'ENABLE_INTERACTIVE_MODE') {
            console.log('Enabling interactive mode via message');
            enableInteractiveMode();
          } else if (event.data.type === 'DISABLE_INTERACTIVE_MODE') {
            console.log('Disabling interactive mode via message');
            disableInteractiveMode();
          } else if (event.data.type === 'UPDATE_CONTENT_MAP') {
            contentMap = event.data.contentMap;
            updatePageContent();
          } else if (event.data.type === 'UPDATE_INTERACTIVE_MODE') {
            console.log('Updating interactive mode to:', event.data.interactiveMode);
            interactiveMode = event.data.interactiveMode;
            if (interactiveMode) {
              enableInteractiveMode();
            } else {
              disableInteractiveMode();
            }
          }
        });

        // Send message to parent when element is selected
        window.addEventListener('message', function(event) {
          if (event.data.type === 'ELEMENT_SELECTED') {
            console.log('Element selected in iframe, forwarding to parent');
            window.parent.postMessage(event.data, '*');
          }
        });

        // Initialize content when DOM is ready
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', function() {
            updatePageContent();
            if (interactiveMode) {
              enableInteractiveMode();
            }
          });
        } else {
          updatePageContent();
          if (interactiveMode) {
            enableInteractiveMode();
          }
        }

        // Test message handler
        window.addEventListener('message', function(event) {
          if (event.data.type === 'TEST_MESSAGE') {
            console.log('Received test message:', event.data.message);
            window.parent.postMessage({
              type: 'TEST_RESPONSE',
              message: 'Hello from iframe!'
            }, '*');
          }
        });

      })();
    `

    try {
      const scriptElement = iframe.contentDocument?.createElement('script')
      if (scriptElement) {
        scriptElement.textContent = script
        iframe.contentDocument?.head.appendChild(scriptElement)
        console.log('Script injected successfully')
      } else {
        console.log('Could not create script element')
      }
    } catch (error) {
      console.error('Error injecting script:', error)
    }
  }

  const handleIframeLoad = () => {
    console.log('Iframe loaded for URL:', pagePath)
    console.log('Iframe document ready state:', iframeRef.current?.contentDocument?.readyState)
    setIframeLoaded(true)
    // Wait a bit for the iframe to fully load, then inject the script
    setTimeout(() => {
      console.log('Injecting script after timeout...')
      injectContentAndScript()
    }, 100)
  }

  return (
    <iframe
      ref={iframeRef}
      src={pagePath}
      className="w-full h-full min-h-screen border-0"
      onLoad={handleIframeLoad}
      sandbox="allow-scripts allow-same-origin allow-forms"
      style={{ minHeight: '100vh' }}
    />
  )
} 