'use client'

import {
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  CursorArrowRaysIcon,
  EyeIcon,
  EyeSlashIcon,
  DevicePhoneMobileIcon,
  DeviceTabletIcon,
  ComputerDesktopIcon,
  Cog6ToothIcon,
  GlobeAltIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline'

interface ToolbarProps {
  interactiveMode: boolean
  onToggleInteractiveMode: () => void
  previewMode: 'desktop' | 'tablet' | 'mobile'
  onPreviewModeChange: (mode: 'desktop' | 'tablet' | 'mobile') => void
  draftMode: boolean
  onToggleDraftMode: () => void
  onUndo: () => void
  onRedo: () => void
  canUndo: boolean
  canRedo: boolean
  selectedPage?: string
  onRefresh?: () => void
  showTsxEditor?: boolean
  onToggleTsxEditor?: () => void
}

export default function Toolbar({
  interactiveMode,
  onToggleInteractiveMode,
  previewMode,
  onPreviewModeChange,
  draftMode,
  onToggleDraftMode,
  onUndo,
  onRedo,
  canUndo,
  canRedo,
  selectedPage,
  onRefresh,
  showTsxEditor,
  onToggleTsxEditor
}: ToolbarProps) {
  return (
    <div className="bg-white border-b border-gray-200 px-6 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Page Info */}
          <div className="flex items-center space-x-2">
            <GlobeAltIcon className="h-5 w-5 text-gray-500" />
            <span className="font-medium text-gray-900">
              {selectedPage || 'Select Page'}
            </span>
          </div>
          
          {/* Preview Mode Selector */}
          <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => onPreviewModeChange('desktop')}
              className={`p-2 rounded-md transition-colors ${
                previewMode === 'desktop' 
                  ? 'bg-white shadow-sm text-blue-600' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              title="Desktop View"
            >
              <ComputerDesktopIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => onPreviewModeChange('tablet')}
              className={`p-2 rounded-md transition-colors ${
                previewMode === 'tablet' 
                  ? 'bg-white shadow-sm text-blue-600' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              title="Tablet View"
            >
              <DeviceTabletIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => onPreviewModeChange('mobile')}
              className={`p-2 rounded-md transition-colors ${
                previewMode === 'mobile' 
                  ? 'bg-white shadow-sm text-blue-600' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              title="Mobile View"
            >
              <DevicePhoneMobileIcon className="h-4 w-4" />
            </button>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {/* Undo/Redo */}
          <div className="flex items-center space-x-1">
            <button
              onClick={onUndo}
              disabled={!canUndo}
              className="p-2 text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Undo"
            >
              <ArrowUturnLeftIcon className="h-5 w-5" />
            </button>
            <button
              onClick={onRedo}
              disabled={!canRedo}
              className="p-2 text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Redo"
            >
              <ArrowUturnRightIcon className="h-5 w-5" />
            </button>
          </div>
          
          <div className="h-6 w-px bg-gray-300" />

          {/* TSX Editor Toggle */}
          {onToggleTsxEditor && (
            <button
              onClick={onToggleTsxEditor}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                showTsxEditor
                  ? 'bg-purple-100 text-purple-700 hover:bg-purple-200'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <DocumentTextIcon className="h-4 w-4 mr-2" />
              {showTsxEditor ? 'Exit TSX Editor' : 'TSX Editor'}
            </button>
          )}

          {/* Edit Mode Toggle */}
          {!showTsxEditor && (
            <button
              onClick={onToggleInteractiveMode}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                interactiveMode
                  ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <CursorArrowRaysIcon className="h-4 w-4 mr-2" />
              {interactiveMode ? 'Exit Edit Mode' : 'Edit Mode'}
            </button>
          )}
          
          {/* Draft Mode Toggle */}
          <button
            onClick={onToggleDraftMode}
            className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
              draftMode
                ? 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {draftMode ? (
              <EyeSlashIcon className="h-4 w-4 mr-2" />
            ) : (
              <EyeIcon className="h-4 w-4 mr-2" />
            )}
            {draftMode ? 'Draft Mode' : 'Live Mode'}
          </button>

          {/* Settings */}
          <button
            className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
            title="Settings"
          >
            <Cog6ToothIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  )
} 