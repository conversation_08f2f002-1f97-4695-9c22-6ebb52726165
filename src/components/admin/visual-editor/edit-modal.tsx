'use client'

import { motion, AnimatePresence } from 'framer-motion'
import {
  XMarkIcon,
  CheckIcon,
  DocumentTextIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline'

interface EditableElement {
  id: string
  text: string
  description: string
  filePath: string
  line?: number
  rect: DOMRect
  className?: string
  elementPath?: string
  selector?: string
  tagName?: string
  sourceLocation?: string
  lineNumber?: number
  columnStart?: number
}

interface EditModalProps {
  element: EditableElement | null
  value: string
  onChange: (value: string) => void
  onSave: () => void
  onCancel: () => void
  isSaving: boolean
  position: { x: number; y: number }
}

export default function EditModal({
  element,
  value,
  onChange,
  onSave,
  onCancel,
  isSaving,
  position
}: EditModalProps) {
  if (!element) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 10 }}
        className="fixed z-50 bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden"
        style={{
          left: position.x - 200,
          top: position.y - 120,
          width: 400,
          maxWidth: '90vw'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-2">
            <DocumentTextIcon className="h-5 w-5 text-blue-600" />
            <div>
              <h3 className="text-sm font-semibold text-gray-900">Edit Text</h3>
              <p className="text-xs text-gray-500">{element.description}</p>
            </div>
          </div>
          <button
            onClick={onCancel}
            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 space-y-4">
          {/* Element Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <InformationCircleIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-blue-800">
                <div className="font-medium mb-1">Element Details</div>
                <div className="space-y-1">
                  <div><span className="font-medium">Tag:</span> {element.tagName}</div>
                  {element.className && (
                    <div><span className="font-medium">Class:</span> {element.className}</div>
                  )}
                  {element.sourceLocation ? (
                    <div className="bg-green-100 border border-green-300 rounded p-2 mt-2">
                      <div className="font-medium text-green-800 mb-1">📍 Source Location Found</div>
                      <div className="text-green-700">{element.sourceLocation}</div>
                    </div>
                  ) : (
                    <div className="bg-yellow-100 border border-yellow-300 rounded p-2 mt-2">
                      <div className="font-medium text-yellow-800 mb-1">🔍 Searching Source Files...</div>
                      <div className="text-yellow-700">Looking for text in source files</div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Text Editor */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Content
            </label>
            <textarea
              value={value}
              onChange={(e) => onChange(e.target.value)}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={4}
              placeholder="Enter your content here..."
              autoFocus
            />
            <div className="flex items-center justify-between mt-2">
              <div className="text-xs text-gray-500">
                {value.length} characters
              </div>
              <div className="text-xs text-gray-500">
                {value.split(' ').length} words
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-2">
            <button
              onClick={onCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={onSave}
              disabled={isSaving || !value.trim()}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Saving...
                </>
              ) : (
                <>
                  <CheckIcon className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </div>

        {/* Arrow pointing to element */}
        <div
          className="absolute w-0 h-0 border-l-8 border-r-8 border-t-8 border-transparent border-t-white"
          style={{
            left: '50%',
            top: '100%',
            transform: 'translateX(-50%)',
            filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1))'
          }}
        />
      </motion.div>
    </AnimatePresence>
  )
} 