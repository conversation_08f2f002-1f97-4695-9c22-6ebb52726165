'use client'

import React, { useRef, useCallback, useEffect, useState } from 'react'
import { ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline'
import styles from './scroll-simulator.module.css'

interface SimpleScrollSimulatorProps {
  containerRef: React.RefObject<HTMLElement>
  className?: string
  forceShow?: boolean
  debug?: boolean
}

export function SimpleScrollSimulator({
  containerRef,
  className = '',
  forceShow = false,
  debug = true
}: SimpleScrollSimulatorProps) {
  const [isScrolling, setIsScrolling] = useState(false)
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(null)
  const [canScrollUp, setCanScrollUp] = useState(false)
  const [canScrollDown, setCanScrollDown] = useState(false)
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Check scroll availability
  const checkScrollAvailability = useCallback(() => {
    if (!containerRef.current) return

    const { scrollTop, scrollHeight, clientHeight } = containerRef.current
    const tolerance = 5 // Add small tolerance for floating point precision

    setCanScrollUp(scrollTop > tolerance)
    setCanScrollDown(scrollTop < scrollHeight - clientHeight - tolerance)

    // Debug logging
    if (debug) {
      console.log('Scroll check:', {
        scrollTop,
        scrollHeight,
        clientHeight,
        canScrollUp: scrollTop > tolerance,
        canScrollDown: scrollTop < scrollHeight - clientHeight - tolerance,
        forceShow
      })
    }
  }, [containerRef])

  // Quick navigation functions
  const scrollToTop = useCallback(() => {
    if (!containerRef.current) return
    containerRef.current.scrollTo({ top: 0, behavior: 'smooth' })
  }, [containerRef])

  const scrollToBottom = useCallback(() => {
    if (!containerRef.current) return
    containerRef.current.scrollTo({ 
      top: containerRef.current.scrollHeight, 
      behavior: 'smooth' 
    })
  }, [containerRef])

  // Simulate realistic mouse wheel scrolling
  const simulateWheelEvent = useCallback((direction: 'up' | 'down') => {
    if (!containerRef.current) return

    const container = containerRef.current
    const scrollAmount = direction === 'down' ? 150 : -150

    console.log(`Scrolling ${direction}, amount: ${scrollAmount}`)
    console.log('Before scroll:', {
      scrollTop: container.scrollTop,
      scrollHeight: container.scrollHeight,
      clientHeight: container.clientHeight
    })

    // Try multiple scroll methods for better compatibility
    try {
      // Method 1: scrollBy with smooth behavior
      container.scrollBy({
        top: scrollAmount,
        behavior: 'smooth'
      })

      // Method 2: Fallback with direct scrollTop manipulation if smooth doesn't work
      setTimeout(() => {
        if (container.scrollTop === container.scrollTop) { // Check if scroll actually happened
          const newScrollTop = Math.max(0, Math.min(
            container.scrollTop + scrollAmount,
            container.scrollHeight - container.clientHeight
          ))
          container.scrollTop = newScrollTop
        }
      }, 50)

    } catch (error) {
      console.error('Scroll error:', error)
      // Fallback: direct scrollTop manipulation
      const newScrollTop = Math.max(0, Math.min(
        container.scrollTop + scrollAmount,
        container.scrollHeight - container.clientHeight
      ))
      container.scrollTop = newScrollTop
    }

    // Visual feedback
    setIsScrolling(true)
    setScrollDirection(direction)

    // Clear visual feedback after a short delay
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false)
      setScrollDirection(null)
      checkScrollAvailability() // Recheck after scroll
    }, 300)
  }, [containerRef, checkScrollAvailability])

  // Handle button interactions
  const handleScrollUp = useCallback(() => {
    if (canScrollUp) {
      simulateWheelEvent('up')
    }
  }, [canScrollUp, simulateWheelEvent])

  const handleScrollDown = useCallback(() => {
    if (canScrollDown) {
      simulateWheelEvent('down')
    }
  }, [canScrollDown, simulateWheelEvent])

  // Setup event listeners
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    // Initial check
    checkScrollAvailability()

    // Check again after a short delay to ensure content is loaded
    const timeoutId = setTimeout(() => {
      checkScrollAvailability()
    }, 100)

    container.addEventListener('scroll', checkScrollAvailability)
    window.addEventListener('resize', checkScrollAvailability)

    // Also check when content changes
    const observer = new MutationObserver(() => {
      setTimeout(checkScrollAvailability, 50)
    })

    observer.observe(container, {
      childList: true,
      subtree: true,
      attributes: true
    })

    return () => {
      clearTimeout(timeoutId)
      container.removeEventListener('scroll', checkScrollAvailability)
      window.removeEventListener('resize', checkScrollAvailability)
      observer.disconnect()
    }
  }, [checkScrollAvailability])

  // Cleanup
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [])

  if (!forceShow && !canScrollUp && !canScrollDown) return null

  return (
    <div className={`${styles.scrollSimulator} ${className}`}>
      <div className="flex flex-col space-y-1 bg-white rounded-xl shadow-lg border-2 border-blue-200 p-3">
        {/* Debug Info */}
        {debug && (
          <div className="text-xs text-gray-500 text-center mb-2 p-1 bg-gray-50 rounded">
            <div>Debug Mode</div>
            <div>Up: {canScrollUp ? '✓' : '✗'}</div>
            <div>Down: {canScrollDown ? '✓' : '✗'}</div>
          </div>
        )}
        {/* Mouse Wheel Visual */}
        <div className="flex justify-center mb-2">
          <div className="relative">
            {/* Mouse Body */}
            <div className="w-8 h-12 bg-gray-100 rounded-xl border-2 border-gray-300 relative overflow-hidden">
              {/* Mouse Wheel */}
              <div className={`absolute left-1/2 top-3 transform -translate-x-1/2 w-2 h-6 bg-gray-400 rounded-full transition-all duration-200 ${
                isScrolling && scrollDirection === 'up' ? 'translate-y-1 bg-blue-500' :
                isScrolling && scrollDirection === 'down' ? '-translate-y-1 bg-blue-500' : ''
              }`}>
                {/* Wheel notches */}
                <div className="absolute inset-0 flex flex-col justify-center items-center space-y-0.5">
                  <div className="w-0.5 h-0.5 bg-white rounded-full opacity-60"></div>
                  <div className="w-0.5 h-0.5 bg-white rounded-full opacity-60"></div>
                  <div className="w-0.5 h-0.5 bg-white rounded-full opacity-60"></div>
                </div>
              </div>
              
              {/* Scroll direction indicator */}
              {isScrolling && (
                <div className={`absolute left-1/2 transform -translate-x-1/2 ${
                  scrollDirection === 'up' ? 'top-1' : 'bottom-1'
                }`}>
                  {scrollDirection === 'up' ? (
                    <ChevronUpIcon className="h-3 w-3 text-blue-500 animate-bounce" />
                  ) : (
                    <ChevronDownIcon className="h-3 w-3 text-blue-500 animate-bounce" />
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Scroll Up Button */}
        <button
          type="button"
          onClick={handleScrollUp}
          onDoubleClick={scrollToTop}
          disabled={!canScrollUp}
          className={`p-2 rounded-lg transition-all duration-200 ${
            canScrollUp
              ? 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 shadow-md hover:shadow-lg'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
          title="Scroll up (Double-click to go to top)"
        >
          <ChevronUpIcon className="h-4 w-4" />
        </button>

        {/* Scroll Down Button */}
        <button
          type="button"
          onClick={handleScrollDown}
          onDoubleClick={scrollToBottom}
          disabled={!canScrollDown}
          className={`p-2 rounded-lg transition-all duration-200 ${
            canScrollDown
              ? 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 shadow-md hover:shadow-lg'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
          title="Scroll down (Double-click to go to bottom)"
        >
          <ChevronDownIcon className="h-4 w-4" />
        </button>

        {/* Instructions */}
        <div className="text-xs text-gray-500 text-center mt-1 px-1 leading-tight">
          <div>Click to scroll</div>
          <div>Double-click for ends</div>
        </div>
      </div>
    </div>
  )
}
