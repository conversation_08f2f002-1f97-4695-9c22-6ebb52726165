'use client'

import React, { useRef, useCallback, useEffect, useState } from 'react'
import { ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline'

interface MouseWheelSimulatorProps {
  containerRef: React.RefObject<HTMLElement>
  className?: string
}

export function MouseWheelSimulator({ 
  containerRef, 
  className = ''
}: MouseWheelSimulatorProps) {
  const [isScrolling, setIsScrolling] = useState(false)
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(null)
  const [canScrollUp, setCanScrollUp] = useState(false)
  const [canScrollDown, setCanScrollDown] = useState(false)
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Check scroll availability
  const checkScrollAvailability = useCallback(() => {
    if (!containerRef.current) return

    const { scrollTop, scrollHeight, clientHeight } = containerRef.current
    setCanScrollUp(scrollTop > 0)
    setCanScrollDown(scrollTop < scrollHeight - clientHeight)
  }, [containerRef])

  // Quick navigation functions
  const scrollToTop = useCallback(() => {
    if (!containerRef.current) return
    containerRef.current.scrollTo({ top: 0, behavior: 'smooth' })
  }, [containerRef])

  const scrollToBottom = useCallback(() => {
    if (!containerRef.current) return
    containerRef.current.scrollTo({
      top: containerRef.current.scrollHeight,
      behavior: 'smooth'
    })
  }, [containerRef])

  // Simulate realistic mouse wheel scrolling
  const simulateWheelEvent = useCallback((direction: 'up' | 'down') => {
    if (!containerRef.current) return

    const container = containerRef.current
    
    // Create and dispatch a realistic wheel event
    const wheelEvent = new WheelEvent('wheel', {
      deltaY: direction === 'down' ? 100 : -100,
      deltaMode: WheelEvent.DOM_DELTA_PIXEL,
      bubbles: true,
      cancelable: true,
      view: window
    })

    // Dispatch the event to trigger natural scroll behavior
    container.dispatchEvent(wheelEvent)

    // Visual feedback
    setIsScrolling(true)
    setScrollDirection(direction)

    // Clear visual feedback after a short delay
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }
    
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false)
      setScrollDirection(null)
    }, 150)
  }, [containerRef])

  // Smooth scroll with multiple wheel events for natural feel
  const performSmoothScroll = useCallback((direction: 'up' | 'down', intensity: number = 3) => {
    let count = 0
    const maxCount = intensity

    const scrollStep = () => {
      if (count < maxCount) {
        simulateWheelEvent(direction)
        count++
        setTimeout(scrollStep, 50) // 50ms between each wheel event
      }
    }

    scrollStep()
  }, [simulateWheelEvent])

  // Handle button interactions
  const handleScrollUp = useCallback(() => {
    if (canScrollUp) {
      performSmoothScroll('up', 2)
    }
  }, [canScrollUp, performSmoothScroll])

  const handleScrollDown = useCallback(() => {
    if (canScrollDown) {
      performSmoothScroll('down', 2)
    }
  }, [canScrollDown, performSmoothScroll])

  // Setup event listeners
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    checkScrollAvailability()
    container.addEventListener('scroll', checkScrollAvailability)
    window.addEventListener('resize', checkScrollAvailability)

    // Keyboard support
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle if the container is focused or if no input is focused
      const activeElement = document.activeElement
      const isInputFocused = activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.tagName === 'SELECT'
      )

      if (!isInputFocused) {
        switch (e.key) {
          case 'ArrowUp':
            e.preventDefault()
            handleScrollUp()
            break
          case 'ArrowDown':
            e.preventDefault()
            handleScrollDown()
            break
          case 'Home':
            e.preventDefault()
            scrollToTop()
            break
          case 'End':
            e.preventDefault()
            scrollToBottom()
            break
          case 'PageUp':
            e.preventDefault()
            performSmoothScroll('up', 5)
            break
          case 'PageDown':
            e.preventDefault()
            performSmoothScroll('down', 5)
            break
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)

    return () => {
      container.removeEventListener('scroll', checkScrollAvailability)
      window.removeEventListener('resize', checkScrollAvailability)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [checkScrollAvailability, handleScrollUp, handleScrollDown, scrollToTop, scrollToBottom, performSmoothScroll])

  // Cleanup
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [])

  if (!canScrollUp && !canScrollDown) return null

  return (
    <div className={`fixed right-6 top-1/2 transform -translate-y-1/2 z-50 ${className}`}>
      <div className="flex flex-col space-y-1 bg-white rounded-xl shadow-lg border border-gray-200 p-3">
        {/* Mouse Wheel Visual */}
        <div className="flex justify-center mb-2">
          <div className="relative">
            {/* Mouse Body */}
            <div className="w-8 h-12 bg-gray-100 rounded-xl border-2 border-gray-300 relative overflow-hidden">
              {/* Mouse Wheel */}
              <div className={`absolute left-1/2 top-3 transform -translate-x-1/2 w-2 h-6 bg-gray-400 rounded-full transition-all duration-150 ${
                isScrolling && scrollDirection === 'up' ? 'translate-y-1 bg-blue-500' :
                isScrolling && scrollDirection === 'down' ? '-translate-y-1 bg-blue-500' : ''
              }`}>
                {/* Wheel notches */}
                <div className="absolute inset-0 flex flex-col justify-center items-center space-y-0.5">
                  <div className="w-0.5 h-0.5 bg-white rounded-full opacity-60"></div>
                  <div className="w-0.5 h-0.5 bg-white rounded-full opacity-60"></div>
                  <div className="w-0.5 h-0.5 bg-white rounded-full opacity-60"></div>
                </div>
              </div>
              
              {/* Scroll direction indicator */}
              {isScrolling && (
                <div className={`absolute left-1/2 transform -translate-x-1/2 ${
                  scrollDirection === 'up' ? 'top-1' : 'bottom-1'
                }`}>
                  {scrollDirection === 'up' ? (
                    <ChevronUpIcon className="h-3 w-3 text-blue-500 animate-bounce" />
                  ) : (
                    <ChevronDownIcon className="h-3 w-3 text-blue-500 animate-bounce" />
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Scroll Up Button */}
        <button
          type="button"
          onClick={handleScrollUp}
          onDoubleClick={scrollToTop}
          disabled={!canScrollUp}
          className={`p-2 rounded-lg transition-all duration-200 ${
            canScrollUp
              ? 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 shadow-md hover:shadow-lg'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
          title="Scroll up (Double-click to go to top)"
        >
          <ChevronUpIcon className="h-4 w-4" />
        </button>

        {/* Scroll Down Button */}
        <button
          type="button"
          onClick={handleScrollDown}
          onDoubleClick={scrollToBottom}
          disabled={!canScrollDown}
          className={`p-2 rounded-lg transition-all duration-200 ${
            canScrollDown
              ? 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 shadow-md hover:shadow-lg'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
          title="Scroll down (Double-click to go to bottom)"
        >
          <ChevronDownIcon className="h-4 w-4" />
        </button>

        {/* Instructions */}
        <div className="text-xs text-gray-500 text-center mt-1 px-1 leading-tight">
          <div>Click to scroll</div>
          <div>Double-click for ends</div>
          <div className="mt-1 text-gray-400">
            <div>↑↓ Arrow keys</div>
            <div>PgUp/PgDn</div>
            <div>Home/End</div>
          </div>
        </div>
      </div>
    </div>
  )
}
