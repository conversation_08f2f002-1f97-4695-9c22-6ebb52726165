'use client'

import React, { useState, useRef } from 'react'
import { CloudArrowUpIcon, DocumentIcon, PhotoIcon, XMarkIcon, EyeIcon } from '@heroicons/react/24/outline'

interface SimpleFileUploadProps {
  value?: string
  onChange: (path: string) => void
  accept?: string
  uploadType: 'image' | 'document'
  placeholder?: string
  disabled?: boolean
  className?: string
}

export function SimpleFileUpload({
  value,
  onChange,
  accept,
  uploadType,
  placeholder,
  disabled = false,
  className = ''
}: SimpleFileUploadProps) {
  const [uploading, setUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const defaultAccept = uploadType === 'image' 
    ? 'image/jpeg,image/png,image/gif,image/webp,image/jpg'
    : 'application/pdf,.pdf,.doc,.docx,.txt'

  const handleFileSelect = async (file: File) => {
    setUploading(true)
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('category', uploadType === 'image' ? 'profile' : 'document')

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }

      const response_data = await response.json()
      console.log('Upload response:', response_data)

      // Get the file path from the response - handle successResponse wrapper
      let filePath = ''
      if (response_data.success && response_data.data) {
        // Response is wrapped in successResponse format
        if (response_data.data.files && response_data.data.files.length > 0) {
          filePath = response_data.data.files[0].url
        } else if (response_data.data.url) {
          filePath = response_data.data.url
        }
      } else if (response_data.files && response_data.files.length > 0) {
        // Direct response format
        filePath = response_data.files[0].url
      } else if (response_data.url) {
        // Direct URL format
        filePath = response_data.url
      }

      if (!filePath) {
        console.error('No file path found in response:', response_data)
        throw new Error('No file path returned from upload')
      }

      console.log('Setting file path:', filePath)
      onChange(filePath)
    } catch (error) {
      console.error('Upload error:', error)
      alert(error instanceof Error ? error.message : 'Upload failed. Please try again.')
    } finally {
      setUploading(false)
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  const clearFile = () => {
    onChange('')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const getFileName = (path: string) => {
    return path.split('/').pop() || 'Unknown file'
  }

  const isImage = (path: string) => {
    return /\.(jpg|jpeg|png|gif|webp)$/i.test(path)
  }

  const openDocument = () => {
    if (value) {
      // Open the document in a new tab/window
      window.open(value, '_blank')
    }
  }

  return (
    <div className={className}>
      {uploadType === 'image' && value && isImage(value) ? (
        // Side-by-side layout for images with preview
        <div className="flex items-start space-x-4">
          {/* Left side - Input and controls */}
          <div className="flex-1 space-y-2">
            <div className="flex items-center space-x-2">
              {/* File path input */}
              <input
                type="text"
                value={value || ''}
                onChange={(e) => onChange(e.target.value)}
                placeholder={placeholder}
                disabled={disabled}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm bg-white"
              />

              {/* Upload button */}
              <button
                type="button"
                onClick={openFileDialog}
                disabled={uploading || disabled}
                className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1 text-sm"
              >
                {uploading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <>
                    <CloudArrowUpIcon className="w-4 h-4" />
                    <span>Upload</span>
                  </>
                )}
              </button>
            </div>

            {/* File info */}
            <div className="flex items-center space-x-2 text-xs text-gray-600">
              <PhotoIcon className="w-4 h-4" />
              <span>File: {getFileName(value)}</span>
            </div>
          </div>

          {/* Right side - Photo preview */}
          <div className="flex-shrink-0">
            <div className="text-xs text-gray-500 mb-2 text-center">Preview</div>
            <div className="relative inline-block">
              <img
                src={value}
                alt="Selected photo"
                className="w-32 h-32 object-cover rounded-lg border-2 border-gray-200 shadow-sm"
                onError={(e) => {
                  console.error('Image failed to load:', value)
                  const target = e.currentTarget as HTMLImageElement
                  target.style.display = 'none'
                  // Show error message
                  const errorDiv = document.createElement('div')
                  errorDiv.className = 'w-32 h-32 bg-gray-100 border-2 border-gray-200 rounded-lg flex items-center justify-center text-xs text-gray-500'
                  errorDiv.textContent = 'Image not found'
                  target.parentNode?.appendChild(errorDiv)
                }}
                onLoad={() => {
                  console.log('Image loaded successfully:', value)
                }}
              />
              {/* X button to remove photo */}
              <button
                type="button"
                onClick={clearFile}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1.5 hover:bg-red-600 transition-colors shadow-lg"
                title="Remove photo"
              >
                <XMarkIcon className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      ) : (
        // Standard layout for documents or when no image
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            {/* File path input */}
            <input
              type="text"
              value={value || ''}
              onChange={(e) => onChange(e.target.value)}
              placeholder={placeholder}
              disabled={disabled}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm bg-white"
            />

            {/* Upload button */}
            <button
              type="button"
              onClick={openFileDialog}
              disabled={uploading || disabled}
              className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1 text-sm"
            >
              {uploading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <>
                  <CloudArrowUpIcon className="w-4 h-4" />
                  <span>Upload</span>
                </>
              )}
            </button>
          </div>

          {/* File info for documents */}
          {value && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs text-gray-600">
                <div className="flex items-center space-x-2">
                  {uploadType === 'image' ? (
                    <PhotoIcon className="w-4 h-4" />
                  ) : (
                    <DocumentIcon className="w-4 h-4" />
                  )}
                  <span>File: {getFileName(value)}</span>
                </div>
                {/* X button for documents */}
                {uploadType === 'document' && (
                  <button
                    type="button"
                    onClick={clearFile}
                    className="text-red-500 hover:text-red-700 transition-colors p-1"
                    title="Remove file"
                  >
                    <XMarkIcon className="w-4 h-4" />
                  </button>
                )}
              </div>

              {/* Open document button for documents */}
              {uploadType === 'document' && (
                <button
                  type="button"
                  onClick={openDocument}
                  className="flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                  title="Open document"
                >
                  <EyeIcon className="w-4 h-4" />
                  <span>Open Document</span>
                </button>
              )}
            </div>
          )}
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept || defaultAccept}
        onChange={handleFileChange}
        className="hidden"
        disabled={uploading || disabled}
      />
    </div>
  )
}
