'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  BuildingOfficeIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  CreditCardIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'
import { ClientManagement } from './client-management'
import { EnhancedProjectManagement } from './enhanced-project-management'
import { EnhancedInvoiceManagement } from './enhanced-invoice-management'
import { EnhancedPaymentManagement } from './enhanced-payment-management'

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  country?: string
  zipCode?: string
  logoUrl?: string
  isActive: boolean
  notes?: string
  createdAt: string
  updatedAt: string
  _count?: {
    projects: number
    invoices: number
    payments: number
  }
}

interface Project {
  id: string | number
  clientId: string | number
  name: string
  description: string
  status?: string
  projStartDate?: string
  projCompletionDate?: string
  estimateCost?: number
  estimateTime?: string
  imageUrl?: string
  projectUrl?: string
  githubUrl?: string
  tags?: string
  createdAt: string
  updatedAt?: string
  _count?: {
    contracts: number
    invoices: number
    messages: number
  }
}



interface Invoice {
  id: string | number
  clientId: string | number
  projectId?: string | number
  totalAmount: number
  subtotal?: number
  taxRate: number
  taxAmount: number
  status: string
  dueDate: string
  description?: string
  paidAt?: string
  createdAt: string
  updatedAt?: string
  _count?: {
    payments: number
  }
}

interface Payment {
  id: string | number
  amount: number
  paymentDate: string
  paymentMethod: string
  status: string
  notes?: string
  invoiceId: string | number
  createdAt: string
  updatedAt?: string
}

type ActiveSection = 'clients' | 'projects' | 'invoices' | 'payments'

export function ClientsManagement() {
  const [activeSection, setActiveSection] = useState<ActiveSection>('clients')
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null)
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null)

  const sections = [
    {
      id: 'clients' as const,
      title: 'Clients',
      description: 'Manage client information and contacts',
      color: 'bg-blue-500',
      isActive: activeSection === 'clients'
    },
    {
      id: 'projects' as const,
      title: 'Projects',
      description: 'Manage client projects and deliverables',
      color: 'bg-green-500',
      isActive: activeSection === 'projects',
      disabled: !selectedClient
    },
    {
      id: 'invoices' as const,
      title: 'Invoices',
      description: 'Manage project invoices and billing',
      color: 'bg-yellow-500',
      isActive: activeSection === 'invoices',
      disabled: !selectedProject
    },
    {
      id: 'payments' as const,
      title: 'Payments',
      description: 'Manage invoice payments and transactions',
      color: 'bg-purple-500',
      isActive: activeSection === 'payments',
      disabled: !selectedInvoice
    }
  ]

  const handleSectionChange = (sectionId: ActiveSection) => {
    if (sections.find(s => s.id === sectionId)?.disabled) return
    setActiveSection(sectionId)
  }

  const handleClientSelect = (client: Client | null) => {
    console.log('ClientsManagement: Client selected:', client)

    // Ensure the client object is properly serialized
    const cleanClient = client ? {
      id: String(client.id),
      companyName: String(client.companyName || ''),
      contactName: String(client.contactName || ''),
      contactEmail: String(client.contactEmail || ''),
      contactPhone: client.contactPhone ? String(client.contactPhone) : undefined,
      website: client.website ? String(client.website) : undefined,
      address: client.address ? String(client.address) : undefined,
      city: client.city ? String(client.city) : undefined,
      state: client.state ? String(client.state) : undefined,
      country: client.country ? String(client.country) : undefined,
      zipCode: client.zipCode ? String(client.zipCode) : undefined,
      logoUrl: client.logoUrl ? String(client.logoUrl) : undefined,
      isActive: Boolean(client.isActive),
      notes: client.notes ? String(client.notes) : undefined,
      createdAt: client.createdAt ? String(client.createdAt) : '',
      updatedAt: client.updatedAt ? String(client.updatedAt) : '',
      _count: client._count || undefined
    } : null

    console.log('ClientsManagement: Clean client:', cleanClient)

    setSelectedClient(cleanClient)
    setSelectedProject(null)
    setSelectedInvoice(null)
    if (cleanClient && activeSection === 'clients') {
      setActiveSection('projects')
    }
  }

  const handleProjectSelect = (project: Project | null) => {
    const cleanProject = project ? {
      id: String(project.id),
      clientId: String(project.clientId),
      name: String(project.name || ''),
      description: String(project.description || ''),
      status: project.status ? String(project.status) : undefined,
      projStartDate: project.projStartDate ? String(project.projStartDate) : undefined,
      projCompletionDate: project.projCompletionDate ? String(project.projCompletionDate) : undefined,
      estimateCost: project.estimateCost ? Number(project.estimateCost) : undefined,
      estimateTime: project.estimateTime ? String(project.estimateTime) : undefined,
      imageUrl: project.imageUrl ? String(project.imageUrl) : undefined,
      projectUrl: project.projectUrl ? String(project.projectUrl) : undefined,
      githubUrl: project.githubUrl ? String(project.githubUrl) : undefined,
      tags: project.tags ? String(project.tags) : undefined,
      createdAt: project.createdAt ? String(project.createdAt) : '',
      updatedAt: project.updatedAt ? String(project.updatedAt) : undefined,
      _count: project._count || undefined
    } : null

    setSelectedProject(cleanProject)
    setSelectedInvoice(null)
    if (cleanProject && activeSection === 'projects') {
      setActiveSection('invoices')
    }
  }



  const handleInvoiceSelect = (invoice: Invoice | null) => {
    const cleanInvoice = invoice ? {
      id: String(invoice.id),
      clientId: String(invoice.clientId),
      projectId: invoice.projectId ? String(invoice.projectId) : undefined,
      totalAmount: Number(invoice.totalAmount || 0),
      subtotal: invoice.subtotal ? Number(invoice.subtotal) : undefined,
      taxRate: Number(invoice.taxRate || 0),
      taxAmount: Number(invoice.taxAmount || 0),
      status: String(invoice.status || ''),
      dueDate: String(invoice.dueDate || ''),
      description: invoice.description ? String(invoice.description) : undefined,
      paidAt: invoice.paidAt ? String(invoice.paidAt) : undefined,
      createdAt: invoice.createdAt ? String(invoice.createdAt) : '',
      updatedAt: invoice.updatedAt ? String(invoice.updatedAt) : undefined,
      _count: invoice._count || undefined
    } : null

    setSelectedInvoice(cleanInvoice)
    setSelectedPayment(null)
    if (cleanInvoice && activeSection === 'invoices') {
      setActiveSection('payments')
    }
  }

  const handlePaymentSelect = (payment: Payment | null) => {
    const cleanPayment = payment ? {
      id: String(payment.id),
      amount: Number(payment.amount || 0),
      paymentDate: String(payment.paymentDate || ''),
      paymentMethod: String(payment.paymentMethod || ''),
      status: String(payment.status || ''),
      notes: payment.notes ? String(payment.notes) : undefined,
      invoiceId: String(payment.invoiceId),
      createdAt: payment.createdAt ? String(payment.createdAt) : '',
      updatedAt: payment.updatedAt ? String(payment.updatedAt) : undefined
    } : null

    setSelectedPayment(cleanPayment)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Clients Management</h1>
            <p className="text-gray-600 mt-1">
              Manage your client hierarchy: Clients → Projects → Invoices → Payments
            </p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <span>Hierarchical Management System</span>
          </div>
        </div>

        {/* Breadcrumb */}
        <div className="mt-4 flex items-center space-x-2 text-sm">
          <span className="text-gray-500">Current Path:</span>
          <div className="flex items-center space-x-1">
            <span className="font-medium text-blue-600">Clients</span>
            {selectedClient && selectedClient.companyName && (
              <>
                <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                <span className="font-medium text-green-600">{selectedClient.companyName}</span>
              </>
            )}
            {selectedProject && selectedProject.name && (
              <>
                <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                <span className="font-medium text-green-600">{selectedProject.name}</span>
              </>
            )}
            {selectedInvoice && (
              <>
                <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                <span className="font-medium text-yellow-600">Invoice #{selectedInvoice.id}</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Section Navigation */}
      <div className="overflow-x-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 min-w-[600px] md:min-w-0">
          {sections.map((section) => (
          <motion.button
            key={section.id}
            onClick={() => handleSectionChange(section.id)}
            disabled={section.disabled}
            className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
              section.isActive
                ? 'border-blue-500 bg-blue-50 shadow-md'
                : section.disabled
                ? 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-50'
                : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
            }`}
            whileHover={!section.disabled ? { scale: 1.02 } : undefined}
            whileTap={!section.disabled ? { scale: 0.98 } : undefined}
          >
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${section.color} ${section.disabled ? 'opacity-50' : ''}`}>
                {section.id === 'clients' && <BuildingOfficeIcon className="h-5 w-5 text-white" />}
                {section.id === 'projects' && <DocumentTextIcon className="h-5 w-5 text-white" />}
                {section.id === 'invoices' && <CurrencyDollarIcon className="h-5 w-5 text-white" />}
                {section.id === 'payments' && <CreditCardIcon className="h-5 w-5 text-white" />}
              </div>
              <div className="flex-1">
                <h3 className={`font-semibold ${
                  section.isActive ? 'text-blue-900' : section.disabled ? 'text-gray-400' : 'text-gray-900'
                }`}>
                  {String(section.title)}
                </h3>
                <p className={`text-sm ${
                  section.isActive ? 'text-blue-600' : section.disabled ? 'text-gray-400' : 'text-gray-500'
                }`}>
                  {String(section.description)}
                </p>
              </div>
            </div>
            </motion.button>
          ))}
        </div>
      </div>

      {/* Content Area */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto">
        {activeSection === 'clients' && (
          <ClientManagement
            selectedClient={selectedClient}
            onClientSelect={handleClientSelect}
          />
        )}

        {activeSection === 'projects' && selectedClient && (
          <EnhancedProjectManagement
            client={selectedClient}
            selectedProject={selectedProject}
            onProjectSelect={handleProjectSelect}
          />
        )}

        {activeSection === 'invoices' && selectedProject && (
          <EnhancedInvoiceManagement
            client={selectedClient}
            project={selectedProject}
            selectedInvoice={selectedInvoice}
            onInvoiceSelect={handleInvoiceSelect}
          />
        )}

        {activeSection === 'payments' && selectedInvoice && (
          <EnhancedPaymentManagement
            client={selectedClient}
            project={selectedProject}
            invoice={selectedInvoice}
            selectedPayment={selectedPayment}
            onPaymentSelect={handlePaymentSelect}
          />
        )}
      </div>
    </div>
  )
}
