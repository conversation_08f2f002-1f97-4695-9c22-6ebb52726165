'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Elements } from '@stripe/react-stripe-js'
import axios from 'axios'
import {
  XMarkIcon,
  CreditCardIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  BanknotesIcon,
  BuildingLibraryIcon,
  DevicePhoneMobileIcon,
  GlobeAltIcon,
  ChevronDownIcon,
  ShieldCheckIcon,
  LockClosedIcon,
  EnvelopeIcon,
  TagIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import { stripePromise, CURRENCIES, DEFAULT_CURRENCY } from '@/lib/stripe'
import { paymentFormSchema, PaymentFormData, PROMO_CODES } from '@/lib/payment-schema'

interface PaymentFormModalProps {
  isOpen: boolean
  onClose: () => void
  invoice: {
    id: string | number
    totalAmount: number
    description?: string
    status?: string
    dueDate?: string
  }
  project?: {
    id: string | number
    name: string
  }
  client?: {
    id: string | number
    companyName: string
    contactEmail?: string
  }
  onSubmit: (paymentData: any) => void
}

interface PaymentMethod {
  id: string
  name: string
  icon: React.ComponentType<{ className?: string }>
  description: string
  processingFee?: string
  processingTime: string
  requiresStripe?: boolean
  fields: string[]
}

const paymentMethods: PaymentMethod[] = [
  {
    id: 'stripe_card',
    name: 'Credit/Debit Card',
    icon: CreditCardIcon,
    description: 'Visa, Mastercard, American Express',
    processingFee: '2.9% + $0.30',
    processingTime: 'Instant',
    requiresStripe: true,
    fields: ['cardNumber', 'expiryDate', 'cvv', 'cardholderName']
  },
  {
    id: 'paypal',
    name: 'PayPal',
    icon: GlobeAltIcon,
    description: 'PayPal account or guest checkout',
    processingFee: '2.9% + $0.30',
    processingTime: 'Instant',
    fields: ['paypalEmail']
  },
  {
    id: 'bank_transfer',
    name: 'Bank Transfer',
    icon: BuildingLibraryIcon,
    description: 'Direct bank account transfer',
    processingFee: '$0.80',
    processingTime: '1-3 business days',
    fields: ['bankName', 'accountNumber', 'routingNumber', 'accountHolderName']
  },
  {
    id: 'wire_transfer',
    name: 'Wire Transfer',
    icon: BuildingLibraryIcon,
    description: 'International wire transfer',
    processingFee: '$15-25',
    processingTime: '1-5 business days',
    fields: ['bankName', 'swiftCode', 'accountNumber', 'accountHolderName', 'bankAddress']
  },
  {
    id: 'ach_transfer',
    name: 'ACH Transfer',
    icon: BuildingLibraryIcon,
    description: 'Automated Clearing House',
    processingFee: '$0.80',
    processingTime: '3-5 business days',
    fields: ['bankName', 'accountNumber', 'routingNumber', 'accountHolderName']
  },
  {
    id: 'apple_pay',
    name: 'Apple Pay',
    icon: DevicePhoneMobileIcon,
    description: 'Apple Pay digital wallet',
    processingFee: '2.9% + $0.30',
    processingTime: 'Instant',
    requiresStripe: true,
    fields: []
  },
  {
    id: 'google_pay',
    name: 'Google Pay',
    icon: DevicePhoneMobileIcon,
    description: 'Google Pay digital wallet',
    processingFee: '2.9% + $0.30',
    processingTime: 'Instant',
    requiresStripe: true,
    fields: []
  },
  {
    id: 'check',
    name: 'Check',
    icon: BanknotesIcon,
    description: 'Physical or electronic check',
    processingTime: '3-7 business days',
    fields: ['checkNumber', 'bankName']
  },
  {
    id: 'cash',
    name: 'Cash',
    icon: BanknotesIcon,
    description: 'Cash payment in person',
    processingTime: 'Instant',
    fields: []
  }
]

export function PaymentFormModal({ isOpen, onClose, invoice, project, client, onSubmit }: PaymentFormModalProps) {
  const [formData, setFormData] = useState({
    amount: '',
    paymentMethod: 'credit_card',
    paymentDate: new Date().toISOString().split('T')[0],
    notes: '',
    status: 'completed',
    reference: '',
    transactionId: '',
    processingFee: 0
  })

  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [remainingAmount, setRemainingAmount] = useState(0)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (isDropdownOpen && !target.closest('.payment-method-dropdown')) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [isDropdownOpen])

  // Calculate remaining amount and processing fee
  useEffect(() => {
    const fetchRemainingAmount = async () => {
      try {
        const response = await fetch(`/api/admin/invoices-new/${invoice.id}/payments`)

        if (response.ok) {
          const data = await response.json()
          const totalPaid = data.data?.reduce((sum: number, payment: any) => sum + Number(payment.amount), 0) || 0
          setRemainingAmount(Number(invoice.totalAmount) - totalPaid)
        } else {
          console.error('Failed to fetch payments:', response.status, response.statusText)
          setRemainingAmount(Number(invoice.totalAmount))
        }
      } catch (error) {
        console.error('Error fetching payments:', error)
        setRemainingAmount(Number(invoice.totalAmount))
      }
    }

    if (isOpen) {
      fetchRemainingAmount()
    }
  }, [isOpen, invoice.id, invoice.totalAmount])

  // Calculate processing fee based on payment method and amount
  useEffect(() => {
    const selectedMethod = paymentMethods.find(method => method.id === formData.paymentMethod)
    if (selectedMethod?.processingFee && formData.amount) {
      const amount = parseFloat(formData.amount)
      if (!isNaN(amount)) {
        if (selectedMethod.processingFee.includes('%')) {
          const [percentage, fixed] = selectedMethod.processingFee.split(' + $')
          const percentageFee = (amount * parseFloat(percentage.replace('%', '')) / 100)
          const fixedFee = parseFloat(fixed || '0')
          setFormData(prev => ({ ...prev, processingFee: percentageFee + fixedFee }))
        } else {
          const fee = parseFloat(selectedMethod.processingFee.replace('$', '').split('-')[0])
          setFormData(prev => ({ ...prev, processingFee: fee }))
        }
      }
    } else {
      setFormData(prev => ({ ...prev, processingFee: 0 }))
    }
  }, [formData.paymentMethod, formData.amount])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Please enter a valid payment amount'
    } else if (parseFloat(formData.amount) > remainingAmount) {
      newErrors.amount = `Payment amount cannot exceed remaining balance of $${remainingAmount.toFixed(2)}`
    }

    if (!formData.paymentMethod) {
      newErrors.paymentMethod = 'Please select a payment method'
    }

    if (!formData.paymentDate) {
      newErrors.paymentDate = 'Please select a payment date'
    }

    if (formData.paymentMethod === 'bank_transfer' && !formData.reference) {
      newErrors.reference = 'Reference number is required for bank transfers'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setLoading(true)
    setErrorMessage(null)
    setSuccessMessage(null)

    try {
      const paymentData = {
        ...formData,
        amount: parseFloat(formData.amount),
        processingFee: formData.processingFee,
        invoiceId: invoice.id,
        projectId: project?.id,
        clientId: client?.id
      }

      await onSubmit(paymentData)

      setSuccessMessage('Payment created successfully!')

      // Reset form
      setFormData({
        amount: '',
        paymentMethod: 'credit_card',
        paymentDate: new Date().toISOString().split('T')[0],
        notes: '',
        status: 'completed',
        reference: '',
        transactionId: '',
        processingFee: 0
      })
      setErrors({})

      setTimeout(() => {
        onClose()
      }, 2000)
    } catch (error) {
      console.log('Error creating payment:', error)
      setErrorMessage(error instanceof Error ? error.message : 'Failed to create payment. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-4 text-center">
            {/* Background overlay */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
              onClick={onClose}
            />

            {/* Modal panel */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="relative inline-block bg-white rounded-xl text-left shadow-2xl transform transition-all w-full max-w-4xl max-h-[95vh] overflow-hidden"
            >
              {/* Scrollable content */}
              <div className="overflow-y-auto max-h-[95vh]">
              <div className="bg-white px-6 pt-6 pb-6 space-y-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 shadow-lg">
                      <CreditCardIcon className="h-6 w-6 text-white" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-xl font-semibold text-gray-900">
                        Record Payment
                      </h3>
                      <div className="mt-1 space-y-1">
                        <p className="text-sm text-gray-600">
                          Invoice #{invoice.id} • {invoice.description || 'Payment Processing'}
                        </p>
                        {project && client && (
                          <p className="text-xs text-gray-500">
                            {client.companyName} • {project.name}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={onClose}
                    className="bg-gray-100 rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                {/* Invoice Summary */}
                <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 mb-4 border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-700">Invoice Total</p>
                      <p className="text-2xl font-bold text-gray-900">${invoice.totalAmount.toFixed(2)}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-700">Remaining Balance</p>
                      <p className="text-2xl font-bold text-purple-600">${remainingAmount.toFixed(2)}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-700">Status</p>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        invoice.status === 'paid' ? 'bg-green-100 text-green-800' :
                        invoice.status === 'overdue' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {invoice.status || 'pending'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Success Message */}
                {successMessage && (
                  <div className="mb-4 bg-green-50 border border-green-200 rounded-md p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-green-800">{successMessage}</p>
                      </div>
                      <div className="ml-auto pl-3">
                        <div className="-mx-1.5 -my-1.5">
                          <button
                            type="button"
                            onClick={() => setSuccessMessage(null)}
                            className="inline-flex bg-green-50 rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-50 focus:ring-green-600"
                          >
                            <XMarkIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Error Message */}
                {errorMessage && (
                  <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-red-800">{errorMessage}</p>
                      </div>
                      <div className="ml-auto pl-3">
                        <div className="-mx-1.5 -my-1.5">
                          <button
                            type="button"
                            onClick={() => setErrorMessage(null)}
                            className="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600"
                          >
                            <XMarkIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Legacy Error Message (keeping for field validation errors) */}
                {errors.submit && (
                  <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex">
                      <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                      <div className="ml-3">
                        <p className="text-sm text-red-800">{errors.submit}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Payment Amount */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="amount" className="block text-sm font-semibold text-gray-700 mb-2">
                        Payment Amount *
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <CurrencyDollarIcon className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="number"
                          name="amount"
                          id="amount"
                          step="0.01"
                          min="0"
                          max={remainingAmount}
                          required
                          value={formData.amount}
                          onChange={handleChange}
                          className={`block w-full pl-10 pr-3 py-3 text-lg font-medium border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 ${
                            errors.amount ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white'
                          }`}
                          placeholder="0.00"
                        />
                      </div>
                      {errors.amount && (
                        <p className="mt-1 text-sm text-red-600">{errors.amount}</p>
                      )}
                      <p className="mt-1 text-xs text-gray-500">
                        Maximum: ${remainingAmount.toFixed(2)}
                      </p>
                    </div>

                    {/* Payment Date */}
                    <div>
                      <label htmlFor="paymentDate" className="block text-sm font-semibold text-gray-700 mb-2">
                        Payment Date *
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <CalendarIcon className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="date"
                          name="paymentDate"
                          id="paymentDate"
                          required
                          value={formData.paymentDate}
                          onChange={handleChange}
                          className={`block w-full pl-10 pr-3 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 ${
                            errors.paymentDate ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white'
                          }`}
                        />
                      </div>
                      {errors.paymentDate && (
                        <p className="mt-1 text-sm text-red-600">{errors.paymentDate}</p>
                      )}
                    </div>
                  </div>

                  {/* Payment Method Selection */}
                  <div className="relative payment-method-dropdown">
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Payment Method *
                    </label>
                    <div className="relative">
                      <button
                        type="button"
                        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                        className="relative w-full cursor-pointer rounded-lg border border-gray-300 bg-white py-3 pl-4 pr-10 text-left shadow-sm focus:border-purple-500 focus:outline-none focus:ring-1 focus:ring-purple-500"
                      >
                        {(() => {
                          const selectedMethod = paymentMethods.find(method => method.id === formData.paymentMethod)
                          const IconComponent = selectedMethod?.icon || CreditCardIcon
                          return (
                            <div className="flex items-center">
                              <IconComponent className="h-5 w-5 text-gray-400 mr-3" />
                              <div className="flex-1">
                                <div className="flex items-center justify-between">
                                  <span className="block truncate font-medium text-gray-900">
                                    {selectedMethod?.name || 'Select payment method'}
                                  </span>
                                  {selectedMethod?.processingFee && (
                                    <span className="text-sm text-gray-500 ml-2">
                                      {selectedMethod.processingFee}
                                    </span>
                                  )}
                                </div>
                                {selectedMethod && (
                                  <div className="flex items-center justify-between mt-1">
                                    <span className="text-sm text-gray-500">
                                      {selectedMethod.description}
                                    </span>
                                    <span className="text-xs text-gray-400">
                                      {selectedMethod.processingTime}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          )
                        })()}
                        <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                          <ChevronDownIcon className={`h-5 w-5 text-gray-400 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
                        </span>
                      </button>

                      {isDropdownOpen && (
                        <div className="absolute z-10 mt-1 w-full rounded-lg bg-white shadow-lg border border-gray-200 max-h-60 overflow-auto">
                          {paymentMethods.map((method) => {
                            const IconComponent = method.icon
                            const isSelected = formData.paymentMethod === method.id
                            return (
                              <div
                                key={method.id}
                                onClick={() => {
                                  setFormData(prev => ({ ...prev, paymentMethod: method.id }))
                                  setIsDropdownOpen(false)
                                }}
                                className={`cursor-pointer select-none relative py-3 px-4 hover:bg-gray-50 ${
                                  isSelected ? 'bg-purple-50 text-purple-900' : 'text-gray-900'
                                }`}
                              >
                                <div className="flex items-center">
                                  <IconComponent className={`h-5 w-5 mr-3 ${isSelected ? 'text-purple-600' : 'text-gray-400'}`} />
                                  <div className="flex-1">
                                    <div className="flex items-center justify-between">
                                      <span className={`block font-medium ${isSelected ? 'text-purple-900' : 'text-gray-900'}`}>
                                        {method.name}
                                      </span>
                                      {method.processingFee && (
                                        <span className={`text-sm ${isSelected ? 'text-purple-600' : 'text-gray-500'}`}>
                                          {method.processingFee}
                                        </span>
                                      )}
                                      {isSelected && (
                                        <CheckCircleIcon className="h-4 w-4 text-purple-600 ml-2" />
                                      )}
                                    </div>
                                    <div className="flex items-center justify-between mt-1">
                                      <span className={`text-sm ${isSelected ? 'text-purple-700' : 'text-gray-500'}`}>
                                        {method.description}
                                      </span>
                                      <span className={`text-xs ${isSelected ? 'text-purple-600' : 'text-gray-400'}`}>
                                        {method.processingTime}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      )}
                    </div>
                    {errors.paymentMethod && (
                      <p className="mt-2 text-sm text-red-600">{errors.paymentMethod}</p>
                    )}
                  </div>

                  {/* Additional Fields */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Reference Number */}
                    <div>
                      <label htmlFor="reference" className="block text-sm font-semibold text-gray-700 mb-2">
                        Reference Number {formData.paymentMethod === 'bank_transfer' && '*'}
                      </label>
                      <input
                        type="text"
                        name="reference"
                        id="reference"
                        value={formData.reference}
                        onChange={handleChange}
                        className={`block w-full px-3 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 ${
                          errors.reference ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white'
                        }`}
                        placeholder="REF-12345"
                      />
                      {errors.reference && (
                        <p className="mt-1 text-sm text-red-600">{errors.reference}</p>
                      )}
                    </div>

                    {/* Transaction ID */}
                    <div>
                      <label htmlFor="transactionId" className="block text-sm font-semibold text-gray-700 mb-2">
                        Transaction ID
                      </label>
                      <input
                        type="text"
                        name="transactionId"
                        id="transactionId"
                        value={formData.transactionId}
                        onChange={handleChange}
                        className="block w-full px-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white"
                        placeholder="TXN-67890"
                      />
                    </div>
                  </div>

                  {/* Status and Processing Fee */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="status" className="block text-sm font-semibold text-gray-700 mb-2">
                        Payment Status *
                      </label>
                      <select
                        name="status"
                        id="status"
                        value={formData.status}
                        onChange={handleChange}
                        className="block w-full px-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white"
                      >
                        <option value="completed">✅ Completed</option>
                        <option value="pending">⏳ Pending</option>
                        <option value="failed">❌ Failed</option>
                        <option value="cancelled">🚫 Cancelled</option>
                        <option value="refunded">↩️ Refunded</option>
                      </select>
                    </div>

                    {/* Processing Fee Display */}
                    {formData.processingFee > 0 && (
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Processing Fee
                        </label>
                        <div className="px-3 py-3 bg-gray-50 border border-gray-300 rounded-lg">
                          <p className="text-lg font-medium text-gray-900">
                            ${formData.processingFee.toFixed(2)}
                          </p>
                          <p className="text-xs text-gray-500">
                            Automatically calculated
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Notes */}
                  <div>
                    <label htmlFor="notes" className="block text-sm font-semibold text-gray-700 mb-2">
                      Payment Notes
                    </label>
                    <textarea
                      name="notes"
                      id="notes"
                      rows={4}
                      value={formData.notes}
                      onChange={handleChange}
                      className="block w-full px-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white resize-none"
                      placeholder="Add any additional notes about this payment, such as payment confirmation details, special instructions, or internal references..."
                    />
                  </div>

                  {/* Payment Summary */}
                  {formData.amount && (
                    <div className="bg-purple-50 border border-purple-200 rounded-xl p-4">
                      <h4 className="text-sm font-semibold text-purple-900 mb-3">Payment Summary</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-purple-700">Payment Amount:</span>
                          <span className="font-medium text-purple-900">${parseFloat(formData.amount || '0').toFixed(2)}</span>
                        </div>
                        {formData.processingFee > 0 && (
                          <div className="flex justify-between">
                            <span className="text-purple-700">Processing Fee:</span>
                            <span className="font-medium text-purple-900">${formData.processingFee.toFixed(2)}</span>
                          </div>
                        )}
                        <div className="flex justify-between border-t border-purple-200 pt-2">
                          <span className="text-purple-700">Remaining Balance:</span>
                          <span className="font-bold text-purple-900">
                            ${Math.max(0, remainingAmount - parseFloat(formData.amount || '0')).toFixed(2)}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                    <div className="flex items-center text-xs text-gray-500">
                      <InformationCircleIcon className="h-4 w-4 mr-1" />
                      All fields marked with * are required
                    </div>
                    <div className="flex items-center space-x-3">
                      <button
                        type="button"
                        onClick={onClose}
                        className="px-6 py-3 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={loading || !formData.amount || parseFloat(formData.amount) <= 0}
                        className="inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                      >
                        {loading ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                          </>
                        ) : (
                          <>
                            <CreditCardIcon className="h-4 w-4 mr-2" />
                            Record Payment
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </form>
              </div>
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  )
}
