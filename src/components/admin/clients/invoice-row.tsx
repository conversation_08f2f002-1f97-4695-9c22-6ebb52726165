'use client'

import React from 'react'
import { motion } from 'framer-motion'
import {
  CurrencyDollarIcon,
  CalendarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  DocumentTextIcon,
  PencilIcon,
  ArrowDownTrayIcon,
  TrashIcon
} from '@heroicons/react/24/outline'

interface Invoice {
  id: string | number
  clientId: string | number
  projectId?: string | number
  totalAmount: number
  subtotal?: number
  taxRate: number
  taxAmount: number
  status: string
  dueDate: string
  description?: string
  paidAt?: string
  createdAt: string
  updatedAt?: string
  _count?: {
    payments: number
  }
  contract?: {
    id: string | number
    contName: string
  }
  project?: {
    id: string | number
    name: string
  }
  order?: {
    id: string | number
    orderTitle: string
  }
}

interface InvoiceRowProps {
  invoice: Invoice
  isSelected: boolean
  onSelect: () => void
  onAction: (action: string) => void
  onInvoiceSelect: (invoice: Invoice | null) => void
  selectedInvoice: Invoice | null
  visibleColumns: string[]
  displayDensity: string
  viewMode: string
  actionLoading: string | null
  formatDate: (date: string) => string
  formatCurrency: (amount: number) => string
  getStatusColor: (status: string) => string
  getStatusIcon: (status: string) => React.ReactNode
  isOverdue: (dueDate: string, status: string) => boolean
  isDueSoon: (dueDate: string, status: string) => boolean
}

export function InvoiceRow({
  invoice,
  isSelected,
  onSelect,
  onAction,
  onInvoiceSelect,
  selectedInvoice,
  visibleColumns,
  displayDensity,
  viewMode,
  actionLoading,
  formatDate,
  formatCurrency,
  getStatusColor,
  getStatusIcon,
  isOverdue,
  isDueSoon
}: InvoiceRowProps) {
  const isCompact = displayDensity === 'compact'
  const isCurrentlySelected = selectedInvoice?.id === invoice.id

  // List View (Table Row)
  if (viewMode === 'list') {
    return (
      <tr
        className={`cursor-pointer transition-colors ${
          isCurrentlySelected
            ? 'bg-yellow-50 border-yellow-200'
            : 'hover:bg-gray-50'
        }`}
        onClick={() => onInvoiceSelect(invoice)}
      >
        <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
          <input
            type="checkbox"
            checked={isSelected}
            onChange={(e) => {
              e.stopPropagation()
              onSelect()
            }}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
        </td>
        
        {visibleColumns.includes('amount') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm font-medium text-gray-900">
              {formatCurrency(invoice.totalAmount)}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('status') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="flex items-center space-x-2">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                {getStatusIcon(invoice.status)}
                <span className="ml-1">{invoice.status}</span>
              </span>
              {isOverdue(invoice.dueDate, invoice.status) && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                  Overdue
                </span>
              )}
              {isDueSoon(invoice.dueDate, invoice.status) && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                  <ClockIcon className="h-3 w-3 mr-1" />
                  Due Soon
                </span>
              )}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('dueDate') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm text-gray-900">{formatDate(invoice.dueDate)}</div>
          </td>
        )}
        
        {visibleColumns.includes('description') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'}`}>
            <div className="text-sm text-gray-900 max-w-xs truncate">
              {invoice.description || '-'}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('subtotal') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm text-gray-900">
              {invoice.subtotal ? formatCurrency(invoice.subtotal) : '-'}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('taxAmount') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm text-gray-900">
              {invoice.taxAmount > 0 ? formatCurrency(invoice.taxAmount) : '-'}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('paidAt') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm text-gray-900">
              {invoice.paidAt ? formatDate(invoice.paidAt) : '-'}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('createdAt') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm text-gray-500">{formatDate(invoice.createdAt)}</div>
          </td>
        )}
        
        {visibleColumns.includes('updatedAt') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm text-gray-500">
              {invoice.updatedAt ? formatDate(invoice.updatedAt) : '-'}
            </div>
          </td>
        )}
        
        <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap text-right text-sm font-medium`}>
          <div className="flex items-center justify-end space-x-2">
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('edit')
              }}
              disabled={actionLoading === `edit-${invoice.id}`}
              className="p-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50"
              title="Edit Invoice"
            >
              <PencilIcon className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('download')
              }}
              className="p-1.5 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-md transition-colors"
              title="Download Invoice"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('delete')
              }}
              disabled={actionLoading === `delete-${invoice.id}`}
              className="p-1.5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors disabled:opacity-50"
              title="Delete Invoice"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </td>
      </tr>
    )
  }

  // Grid View (Compact Card)
  if (viewMode === 'grid') {
    return (
      <motion.div
        onClick={() => onInvoiceSelect(invoice)}
        className={`border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-md ${
          isCurrentlySelected
            ? 'border-yellow-500 bg-yellow-50 shadow-md'
            : 'border-gray-200 hover:border-gray-300 bg-white'
        }`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => {
                e.stopPropagation()
                onSelect()
              }}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <div className="text-lg font-semibold text-gray-900">
              {formatCurrency(invoice.totalAmount)}
            </div>
          </div>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
            {getStatusIcon(invoice.status)}
            <span className="ml-1">{invoice.status}</span>
          </span>
        </div>
        
        <div className="space-y-2 text-sm">
          <div className="flex items-center text-gray-600">
            <CalendarIcon className="h-4 w-4 mr-2" />
            <span>Due {formatDate(invoice.dueDate)}</span>
          </div>
          
          {invoice.description && (
            <div className="text-gray-600 line-clamp-2">
              {invoice.description}
            </div>
          )}
          
          {(isOverdue(invoice.dueDate, invoice.status) || isDueSoon(invoice.dueDate, invoice.status)) && (
            <div className="flex flex-wrap gap-1">
              {isOverdue(invoice.dueDate, invoice.status) && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                  Overdue
                </span>
              )}
              {isDueSoon(invoice.dueDate, invoice.status) && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                  <ClockIcon className="h-3 w-3 mr-1" />
                  Due Soon
                </span>
              )}
            </div>
          )}
        </div>
        
        <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-200">
          <div className="text-xs text-gray-500">
            Created {formatDate(invoice.createdAt)}
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('edit')
              }}
              disabled={actionLoading === `edit-${invoice.id}`}
              className="p-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50"
              title="Edit"
            >
              <PencilIcon className="h-3 w-3" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('download')
              }}
              className="p-1.5 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-md transition-colors"
              title="Download"
            >
              <ArrowDownTrayIcon className="h-3 w-3" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('delete')
              }}
              disabled={actionLoading === `delete-${invoice.id}`}
              className="p-1.5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors disabled:opacity-50"
              title="Delete"
            >
              <TrashIcon className="h-3 w-3" />
            </button>
          </div>
        </div>
      </motion.div>
    )
  }

  // Card View (Detailed Card)
  return (
    <motion.div
      onClick={() => onInvoiceSelect(invoice)}
      className={`border-2 rounded-lg p-6 cursor-pointer transition-all duration-200 hover:shadow-lg ${
        isCurrentlySelected
          ? 'border-yellow-500 bg-yellow-50 shadow-md'
          : 'border-gray-200 hover:border-gray-300 bg-white'
      }`}
      whileHover={{ scale: 1.01 }}
      whileTap={{ scale: 0.99 }}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-3 mb-4">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => {
                e.stopPropagation()
                onSelect()
              }}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <h3 className="text-lg font-medium text-gray-900">
              {formatCurrency(invoice.totalAmount)}
            </h3>

            {/* Status Badge */}
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
              {getStatusIcon(invoice.status)}
              <span className="ml-1">{invoice.status}</span>
            </span>

            {/* Warning Badges */}
            {isOverdue(invoice.dueDate, invoice.status) && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                Overdue
              </span>
            )}

            {isDueSoon(invoice.dueDate, invoice.status) && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                <ClockIcon className="h-3 w-3 mr-1" />
                Due Soon
              </span>
            )}
          </div>

          {/* Invoice Description */}
          {invoice.description && (
            <p className="mt-2 text-sm text-gray-600 line-clamp-2">
              {invoice.description}
            </p>
          )}

          {/* Invoice Details Grid */}
          <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div className="flex items-center text-gray-600">
              <CalendarIcon className="h-4 w-4 mr-2 text-blue-600" />
              <span>Due {formatDate(invoice.dueDate)}</span>
            </div>

            {invoice.subtotal && (
              <div className="flex items-center text-gray-600">
                <CurrencyDollarIcon className="h-4 w-4 mr-2 text-green-600" />
                <span>Subtotal: {formatCurrency(invoice.subtotal)}</span>
              </div>
            )}

            {invoice.taxAmount > 0 && (
              <div className="flex items-center text-gray-600">
                <DocumentTextIcon className="h-4 w-4 mr-2 text-purple-600" />
                <span>Tax: {formatCurrency(invoice.taxAmount)} ({invoice.taxRate}%)</span>
              </div>
            )}

            {invoice.paidAt && (
              <div className="flex items-center text-gray-600">
                <CheckCircleIcon className="h-4 w-4 mr-2 text-green-600" />
                <span>Paid {formatDate(invoice.paidAt)}</span>
              </div>
            )}
          </div>

          {/* Related Information */}
          {(invoice.contract || invoice.project || invoice.order) && (
            <div className="mt-4 flex flex-wrap items-center gap-4 text-sm text-gray-500">
              {invoice.contract && (
                <div>
                  <span className="font-medium">Contract:</span> {invoice.contract.contName}
                </div>
              )}
              {invoice.project && (
                <div>
                  <span className="font-medium">Project:</span> {invoice.project.name}
                </div>
              )}
              {invoice.order && (
                <div>
                  <span className="font-medium">Order:</span> {invoice.order.orderTitle}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Amount Breakdown */}
        <div className="ml-6 text-right">
          <div className="text-2xl font-bold text-gray-900">
            {formatCurrency(invoice.totalAmount)}
          </div>
          {invoice.subtotal && invoice.subtotal !== invoice.totalAmount && (
            <div className="text-sm text-gray-500">
              Subtotal: {formatCurrency(invoice.subtotal)}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-end space-x-2 mt-4">
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('edit')
              }}
              disabled={actionLoading === `edit-${invoice.id}`}
              className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50"
              title="Edit Invoice"
            >
              <PencilIcon className="h-4 w-4" />
            </button>

            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('download')
              }}
              className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-md transition-colors"
              title="Download Invoice"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
            </button>

            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('delete')
              }}
              disabled={actionLoading === `delete-${invoice.id}`}
              className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors disabled:opacity-50"
              title="Delete Invoice"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Footer with Dates */}
      <div className="mt-6 border-t border-gray-200 pt-4">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div>
            Created {formatDate(invoice.createdAt)}
          </div>
          {invoice.updatedAt && (
            <div>
              Updated {formatDate(invoice.updatedAt)}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
}
