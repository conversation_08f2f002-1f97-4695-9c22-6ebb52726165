'use client'

import React, { useState } from 'react'
import { BuildingOfficeIcon } from '@heroicons/react/24/outline'

interface Client {
  id: string
  companyName: string
  logoUrl?: string
  [key: string]: any
}

interface ClientAvatarProps {
  client: Client
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | 'full-height'
  className?: string
  style?: React.CSSProperties
}

const sizeClasses = {
  sm: 'w-8 h-8 text-xs',
  md: 'w-10 h-10 text-sm',
  lg: 'w-16 h-16 text-xl',
  xl: 'w-20 h-20 text-2xl',
  '2xl': 'w-24 h-24 text-3xl',
  '3xl': 'w-32 h-32 text-4xl',
  'full-height': 'w-40 h-full text-5xl'
}

// Helper to generate a consistent background color based on company name
function getBackgroundColor(name: string) {
  const colors = [
    'bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-pink-500', 'bg-indigo-500',
    'bg-yellow-500', 'bg-red-500', 'bg-teal-500', 'bg-orange-500', 'bg-cyan-500'
  ]
  let hash = 0
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash)
  }
  return colors[Math.abs(hash) % colors.length]
}

export function ClientAvatar({
  client,
  size = 'md',
  className = '',
  style = {}
}: ClientAvatarProps) {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  const initials = client.companyName
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)

  const handleImageLoad = () => {
    setImageLoading(false)
  }

  const handleImageError = () => {
    setImageError(true)
    setImageLoading(false)
  }

  // Handle full-height size differently
  const isFullHeight = size === 'full-height'
  const borderRadius = isFullHeight ? 'rounded-xl' : 'rounded-lg'

  // Show logo if available and not errored
  if (client.logoUrl && !imageError) {
    return (
      <div className={`relative ${sizeClasses[size]} ${className}`} style={style}>
        {imageLoading && (
          <div className={`absolute inset-0 bg-blue-600 ${borderRadius} flex items-center justify-center text-white font-semibold ${sizeClasses[size]}`}>
            {initials}
          </div>
        )}
        <img
          src={client.logoUrl}
          alt={`${client.companyName} logo`}
          className={`${sizeClasses[size]} ${borderRadius} object-cover border-2 border-gray-200 shadow-sm ${imageLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
          onLoad={handleImageLoad}
          onError={handleImageError}
          style={style}
        />
      </div>
    )
  }

  // Only logo in full-height mode, no avatar/fallback, no background
  if (isFullHeight) {
    if (client.logoUrl && !imageError) {
      return (
        <img
          src={client.logoUrl}
          alt={client.companyName}
          className={`w-full h-full object-cover rounded-lg ${className || ''}`}
          style={{ ...style, minHeight: '320px', minWidth: '100%' }}
        />
      )
    }
    // No logo: render nothing
    return null
  }

  // Fallback to initials (non-full-height)
  return (
    <div className={`${sizeClasses[size]} ${getBackgroundColor(client.companyName)} ${borderRadius} flex items-center justify-center text-white font-semibold ${className}`} style={style}>
      {initials || <BuildingOfficeIcon className="w-1/2 h-1/2" />}
    </div>
  )
}
