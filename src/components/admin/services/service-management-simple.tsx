'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CogIcon,
  EyeIcon,
  CurrencyDollarIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeSlashIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline'

interface Category {
  id: string
  name: string
  description?: string
  parentId?: string
  isActive: boolean
  displayOrder: number
  children?: Category[]
  _count?: {
    services: number
    children: number
  }
}

interface Service {
  id: string
  categoryId: string
  name: string
  description: string
  iconClass?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  category?: {
    id: string
    name: string
  }
  _count?: {
    serviceOptions: number
    orderDetails: number
  }
}

interface ServiceManagementProps {
  category: Category
  selectedService: Service | null
  onServiceSelect: (service: Service | null) => void
}

interface ServiceFormData {
  name: string
  description: string
  iconClass: string
  price: number
  discountRate: number
  totalDiscount: number
  manager: string
  isActive: boolean
  displayOrder: number
}

interface ColumnConfig {
  key: string
  label: string
  hideable?: boolean
}

export function ServiceManagement({ category, selectedService, onServiceSelect }: ServiceManagementProps) {
  console.log('ServiceManagement: Received category:', category)

  const [services, setServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingService, setEditingService] = useState<Service | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'cards'>('grid')
  const [displayDensity, setDisplayDensity] = useState<'compact' | 'comfortable'>('comfortable')
  const [visibleColumns, setVisibleColumns] = useState<string[]>(['name', 'description', 'price', 'manager', 'isActive', 'updatedAt'])
  const [sortBy, setSortBy] = useState('updatedAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [showDensityMenu, setShowDensityMenu] = useState(false)
  const [showColumnMenu, setShowColumnMenu] = useState(false)
  const [showSortMenu, setShowSortMenu] = useState(false)
  const [filters, setFilters] = useState<Record<string, string>>({})
  
  const searchInputRef = useRef<HTMLInputElement>(null)
  
  const [formData, setFormData] = useState<ServiceFormData>({
    name: '',
    description: '',
    iconClass: '',
    price: 0,
    discountRate: 0,
    totalDiscount: 0,
    manager: '',
    isActive: true,
    displayOrder: 0
  })

  // Column configuration
  const availableColumns: ColumnConfig[] = [
    { key: 'name', label: 'Service Name', hideable: false },
    { key: 'description', label: 'Description', hideable: true },
    { key: 'price', label: 'Price', hideable: true },
    { key: 'discountRate', label: 'Discount', hideable: true },
    { key: 'manager', label: 'Manager', hideable: true },
    { key: 'serviceOptions', label: 'Options', hideable: true },
    { key: 'orderDetails', label: 'Orders', hideable: true },
    { key: 'updatedAt', label: 'Last Updated', hideable: true },
    { key: 'isActive', label: 'Status', hideable: true },
  ]

  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    cards: RectangleStackIcon,
  }

  const densityLabels = {
    compact: 'Compact',
    comfortable: 'Comfortable',
  }

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchTerm])

  useEffect(() => {
    if (category && category.id) {
      fetchServices()
    }
  }, [category.id, debouncedSearchTerm, sortBy, sortOrder, filters])

  const fetchServices = async () => {
    try {
      setLoading(true)
      console.log('Frontend: Fetching services for category:', category.id, category.name)
      
      const params = new URLSearchParams({
        categoryId: category.id,
        limit: '100',
        sortBy: sortBy,
        sortOrder: sortOrder,
      })

      if (debouncedSearchTerm) {
        params.append('search', debouncedSearchTerm)
      }

      // Add filters as JSON string if any filters are set
      const activeFilters = Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value && value !== '')
      )
      if (Object.keys(activeFilters).length > 0) {
        params.append('filter', JSON.stringify(activeFilters))
      }
      
      const url = `/api/admin/services?${params.toString()}`
      console.log('Frontend: API URL:', url)
      const response = await fetch(url)
      console.log('Frontend: Response status:', response.status)
      if (response.ok) {
        const data = await response.json()
        console.log('Frontend: Received services:', data.data?.length, 'services')
        console.log('Frontend: Services data:', data.data)
        setServices(data.data || [])
      } else {
        console.error('Frontend: API error:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('Error fetching services:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const url = editingService 
        ? `/api/admin/services/${editingService.id}`
        : '/api/admin/services'
      
      const method = editingService ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          categoryId: category.id,
          name: formData.name,
          description: formData.description,
          iconClass: formData.iconClass,
          price: formData.price,
          discountRate: formData.discountRate,
          totalDiscount: formData.totalDiscount,
          manager: formData.manager,
          isActive: formData.isActive,
          displayOrder: formData.displayOrder
        }),
      })

      if (response.ok) {
        await fetchServices()
        setIsFormOpen(false)
        setEditingService(null)
        resetForm()
      }
    } catch (error) {
      console.error('Error saving service:', error)
    }
  }

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  const handleColumnToggle = (columnKey: string) => {
    setVisibleColumns(prev => 
      prev.includes(columnKey)
        ? prev.filter(col => col !== columnKey)
        : [...prev, columnKey]
    )
  }

  const resetViewSettings = () => {
    setVisibleColumns(['name', 'description', 'price', 'manager', 'isActive', 'updatedAt'])
    setDisplayDensity('comfortable')
    setViewMode('grid')
    setSortBy('updatedAt')
    setSortOrder('desc')
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      iconClass: '',
      price: 0,
      discountRate: 0,
      totalDiscount: 0,
      manager: '',
      isActive: true,
      displayOrder: 0
    })
  }

  const handleEdit = (service: Service) => {
    setEditingService(service)
    setFormData({
      name: service.name,
      description: service.description,
      iconClass: service.iconClass || '',
      price: service.price,
      discountRate: service.discountRate || 0,
      totalDiscount: service.totalDiscount || 0,
      manager: service.manager || '',
      isActive: service.isActive,
      displayOrder: service.displayOrder
    })
    setIsFormOpen(true)
  }

  const handleDelete = async (service: Service) => {
    if (!confirm(`Are you sure you want to delete "${service.name}"?`)) return

    try {
      const response = await fetch(`/api/admin/services/${service.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchServices()
        if (selectedService?.id === service.id) {
          onServiceSelect(null)
        }
      } else {
        const errorData = await response.json()
        alert(errorData.message || 'Failed to delete service')
      }
    } catch (error) {
      console.error('Error deleting service:', error)
      alert('An error occurred while deleting the service')
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-200 rounded-lg" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Services in "{category.name}"
          </h2>
          <p className="text-gray-600">Manage services under this category</p>
        </div>
        <button
          onClick={() => {
            setEditingService(null)
            resetForm()
            setIsFormOpen(true)
          }}
          className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Service</span>
        </button>
      </div>

      {/* Simple Search Bar */}
      <div className="mb-6">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Search services by name, description, manager..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
            />
            {searchTerm && (
              <button
                onClick={() => {
                  setSearchTerm('')
                  searchInputRef.current?.focus()
                }}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Services Grid */}
      <div className="overflow-x-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 min-w-[640px] md:min-w-0">
        {services.length === 0 ? (
          <div className="col-span-full text-center py-12 text-gray-500">
            <CogIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No services found in this category. Create your first service to get started.</p>
          </div>
        ) : (
          services.map(service => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              onClick={() => onServiceSelect(service)}
              className={`bg-white rounded-lg shadow-sm border-2 p-6 cursor-pointer transition-all duration-200 hover:shadow-md ${
                selectedService?.id === service.id
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 hover:border-green-300'
              }`}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <CogIcon className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {service.name}
                    </h3>
                    <p className="text-sm text-gray-500 truncate">
                      {service.description}
                    </p>
                  </div>
                </div>
                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                  service.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {service.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Price:</span>
                  <span className="font-medium text-gray-900">
                    ${Number(service.price || 0).toFixed(2)}
                  </span>
                </div>
                {service.discountRate && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Discount:</span>
                    <span className="font-medium text-green-600">
                      {service.discountRate}%
                    </span>
                  </div>
                )}
                {service.manager && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Manager:</span>
                    <span className="font-medium text-gray-900">
                      {service.manager}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                <span>{service._count?.serviceOptions || 0} Options</span>
                <span>{service._count?.orderDetails || 0} Orders</span>
                <span>{formatDate(service.updatedAt)}</span>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleEdit(service)
                  }}
                  className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  <PencilIcon className="h-4 w-4 mr-1" />
                  Edit
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleDelete(service)
                  }}
                  className="inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              </div>
            </motion.div>
          ))
        )}
        </div>
      </div>

      {/* Service Form Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
            onClick={() => setIsFormOpen(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="mt-3">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingService ? 'Edit Service' : 'Add New Service'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Name</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500"
                      rows={3}
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Price</label>
                    <input
                      type="number"
                      step="0.01"
                      value={formData.price}
                      onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Manager</label>
                    <input
                      type="text"
                      value={formData.manager}
                      onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500"
                    />
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.isActive}
                      onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-900">Active</label>
                  </div>
                  <div className="flex items-center space-x-3 pt-4">
                    <button
                      type="submit"
                      className="flex-1 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      {editingService ? 'Update' : 'Create'}
                    </button>
                    <button
                      type="button"
                      onClick={() => setIsFormOpen(false)}
                      className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
