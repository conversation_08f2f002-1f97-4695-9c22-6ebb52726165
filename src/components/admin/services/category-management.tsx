'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  FolderIcon,
  FolderOpenIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline'

interface Category {
  id: string
  name: string
  description?: string
  parentId?: string
  isActive: boolean
  displayOrder: number
  children?: Category[]
  _count?: {
    services: number
    children: number
  }
}

interface CategoryManagementProps {
  selectedCategory: Category | null
  onCategorySelect: (category: Category | null) => void
}

interface CategoryFormData {
  name: string
  description: string
  parentId: string
  isActive: boolean
  displayOrder: number
}

export function CategoryManagement({ selectedCategory, onCategorySelect }: CategoryManagementProps) {
  const [categories, setCategories] = useState<Category[]>([])
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'cards'>('list')
  const [displayDensity, setDisplayDensity] = useState<'compact' | 'comfortable'>('comfortable')
  const [showDensityMenu, setShowDensityMenu] = useState(false)
  const [sortBy, setSortBy] = useState('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [showSortMenu, setShowSortMenu] = useState(false)
  const [filters, setFilters] = useState<Record<string, string>>({})

  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    description: '',
    parentId: '',
    isActive: true,
    displayOrder: 0
  })

  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    cards: RectangleStackIcon,
  }

  const densityLabels = {
    compact: 'Compact',
    comfortable: 'Comfortable',
  }

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchTerm])

  useEffect(() => {
    fetchCategories()
  }, [])

  useEffect(() => {
    filterAndSortCategories()
  }, [categories, debouncedSearchTerm, sortBy, sortOrder, filters])

  const filterAndSortCategories = () => {
    let filtered = [...categories]

    // Apply search filter
    if (debouncedSearchTerm) {
      const searchLower = debouncedSearchTerm.toLowerCase()
      filtered = filtered.filter(category =>
        category.name.toLowerCase().includes(searchLower) ||
        (category.description && category.description.toLowerCase().includes(searchLower))
      )
    }

    // Apply other filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== '') {
        if (key === 'isActive') {
          filtered = filtered.filter(category =>
            category.isActive === (value === 'true')
          )
        }
      }
    })

    // Sort categories
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof Category]
      let bValue: any = b[sortBy as keyof Category]

      if (sortBy === 'services') {
        aValue = a._count?.services || 0
        bValue = b._count?.services || 0
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
      }
    })

    setFilteredCategories(buildCategoryTree(filtered))
  }

  const fetchCategories = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/categories?limit=100')

      if (response.ok) {
        const data = await response.json()
        setCategories(buildCategoryTree(data.data || []))
      } else {
        console.error('Failed to fetch categories:', response.status, response.statusText)
        setCategories([])
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      setCategories([])
    } finally {
      setLoading(false)
    }
  }

  const buildCategoryTree = (flatCategories: any[]): Category[] => {
    const categoryMap = new Map()
    const rootCategories: Category[] = []

    // Transform and create map
    flatCategories.forEach(cat => {
      const category: Category = {
        id: String(cat.id),
        name: cat.categname || cat.name,
        description: cat.categdesc || cat.description,
        parentId: cat.parentid ? String(cat.parentid) : undefined,
        isActive: cat.isactive,
        displayOrder: cat.displayorder || 0,
        children: [],
        _count: cat._count
      }
      categoryMap.set(category.id, category)
    })

    // Build tree structure
    categoryMap.forEach(category => {
      if (category.parentId && categoryMap.has(category.parentId)) {
        categoryMap.get(category.parentId).children.push(category)
      } else {
        rootCategories.push(category)
      }
    })

    // Sort by display order
    const sortCategories = (cats: Category[]) => {
      cats.sort((a, b) => a.displayOrder - b.displayOrder)
      cats.forEach(cat => {
        if (cat.children) {
          sortCategories(cat.children)
        }
      })
    }

    sortCategories(rootCategories)
    return rootCategories
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const url = editingCategory 
        ? `/api/admin/categories/${editingCategory.id}`
        : '/api/admin/categories'
      
      const method = editingCategory ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          categname: formData.name,
          categdesc: formData.description,
          parentid: formData.parentId ? Number(formData.parentId) : 0,
          isactive: formData.isActive,
          displayorder: formData.displayOrder
        }),
      })

      if (response.ok) {
        await fetchCategories()
        setIsFormOpen(false)
        setEditingCategory(null)
        setFormData({
          name: '',
          description: '',
          parentId: '',
          isActive: true,
          displayOrder: 0
        })
      }
    } catch (error) {
      console.error('Error saving category:', error)
    }
  }

  const handleEdit = (category: Category) => {
    setEditingCategory(category)
    setFormData({
      name: category.name,
      description: category.description || '',
      parentId: category.parentId || '',
      isActive: category.isActive,
      displayOrder: category.displayOrder
    })
    setIsFormOpen(true)
  }

  const handleDelete = async (category: Category) => {
    if (!confirm(`Are you sure you want to delete "${category.name}"?`)) return

    try {
      const response = await fetch(`/api/admin/categories/${category.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchCategories()
        if (selectedCategory?.id === category.id) {
          onCategorySelect(null)
        }
      } else {
        const errorData = await response.json()
        alert(errorData.message || 'Failed to delete category')
      }
    } catch (error) {
      console.error('Error deleting category:', error)
      alert('An error occurred while deleting the category')
    }
  }

  const handleToggleActive = async (category: Category) => {
    try {
      const response = await fetch(`/api/admin/categories/${category.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          categname: category.name,
          categdesc: category.description,
          parentid: category.parentId ? Number(category.parentId) : 0,
          isactive: !category.isActive,
          displayorder: category.displayOrder
        })
      })
      if (response.ok) {
        fetchCategories()
      }
    } catch (error) {
      console.error('Error toggling category status:', error)
    }
  }

  const toggleExpanded = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  const renderCategory = (category: Category, level: number = 0) => {
    const isExpanded = expandedCategories.has(category.id)
    const hasChildren = category.children && category.children.length > 0
    const isSelected = selectedCategory?.id === category.id

    return (
      <div key={category.id} className="select-none">
        <div
          className={`flex items-center space-x-2 ${displayDensity === 'compact' ? 'p-2' : 'p-3'} rounded-lg cursor-pointer transition-colors ${
            isSelected
              ? 'bg-blue-50 border border-blue-200'
              : 'hover:bg-gray-50'
          }`}
          style={{ marginLeft: `${level * 20}px` }}
          onClick={() => onCategorySelect(category)}
        >
          {hasChildren ? (
            <button
              onClick={(e) => {
                e.stopPropagation()
                toggleExpanded(category.id)
              }}
              className="p-1 hover:bg-gray-200 rounded"
            >
              {isExpanded ? (
                <ChevronDownIcon className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronRightIcon className="h-4 w-4 text-gray-500" />
              )}
            </button>
          ) : (
            <div className="w-6" />
          )}

          <div className="flex items-center space-x-2 flex-1">
            {hasChildren ? (
              isExpanded ? (
                <FolderOpenIcon className={`${displayDensity === 'compact' ? 'h-4 w-4' : 'h-5 w-5'} text-blue-500`} />
              ) : (
                <FolderIcon className={`${displayDensity === 'compact' ? 'h-4 w-4' : 'h-5 w-5'} text-blue-500`} />
              )
            ) : (
              <FolderIcon className={`${displayDensity === 'compact' ? 'h-4 w-4' : 'h-5 w-5'} text-gray-400`} />
            )}

            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className={`font-medium ${displayDensity === 'compact' ? 'text-sm' : 'text-base'} ${isSelected ? 'text-blue-900' : 'text-gray-900'}`}>
                    {category.name}
                  </h3>
                  {category.description && (
                    <p className={`${displayDensity === 'compact' ? 'text-xs' : 'text-sm'} ${isSelected ? 'text-blue-600' : 'text-gray-500'}`}>
                      {category.description}
                    </p>
                  )}
                </div>
                
                <div className="flex items-center space-x-1">
                  {category._count && typeof category._count.services === 'number' && (
                    <span className={`bg-gray-100 text-gray-600 px-2 py-1 rounded ${displayDensity === 'compact' ? 'text-xs' : 'text-xs'}`}>
                      {category._count.services} services
                    </span>
                  )}

                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleEdit(category)
                    }}
                    className={`text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded ${displayDensity === 'compact' ? 'p-0.5' : 'p-1'}`}
                    title="Edit category"
                  >
                    <PencilIcon className={displayDensity === 'compact' ? 'h-3 w-3' : 'h-4 w-4'} />
                  </button>

                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleToggleActive(category)
                    }}
                    className={`rounded transition-colors ${displayDensity === 'compact' ? 'p-0.5' : 'p-1'} ${
                      category.isActive
                        ? 'text-green-600 hover:text-green-700 hover:bg-green-50'
                        : 'text-gray-400 hover:text-green-600 hover:bg-green-50'
                    }`}
                    title={category.isActive ? 'Deactivate category' : 'Activate category'}
                  >
                    {category.isActive ? (
                      <EyeIcon className={displayDensity === 'compact' ? 'h-3 w-3' : 'h-4 w-4'} />
                    ) : (
                      <EyeSlashIcon className={displayDensity === 'compact' ? 'h-3 w-3' : 'h-4 w-4'} />
                    )}
                  </button>

                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDelete(category)
                    }}
                    className={`text-gray-400 hover:text-red-600 hover:bg-red-50 rounded ${displayDensity === 'compact' ? 'p-0.5' : 'p-1'}`}
                    title="Delete category"
                  >
                    <TrashIcon className={displayDensity === 'compact' ? 'h-3 w-3' : 'h-4 w-4'} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {hasChildren && isExpanded && (
          <div className="mt-1">
            {category.children!.map(child => renderCategory(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  const renderCategoryCard = (category: Category, isLargeCard: boolean = false) => {
    const isSelected = selectedCategory?.id === category.id
    const hasChildren = category.children && category.children.length > 0
    const serviceCount = category._count?.services || 0
    const childrenCount = category.children?.length || 0

    return (
      <motion.div
        key={category.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        onClick={() => onCategorySelect(category)}
        className={`bg-white rounded-lg shadow-sm border-2 cursor-pointer transition-all duration-200 hover:shadow-md ${
          isSelected
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-200 hover:border-blue-300'
        } ${displayDensity === 'compact' ? 'p-4' : 'p-6'}`}
      >
        <div className={`flex items-start justify-between ${displayDensity === 'compact' ? 'mb-3' : 'mb-4'}`}>
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            <div className="flex-shrink-0">
              <div className={`${isLargeCard ? 'w-12 h-12' : displayDensity === 'compact' ? 'w-8 h-8' : 'w-10 h-10'} bg-blue-100 rounded-lg flex items-center justify-center`}>
                {hasChildren ? (
                  <FolderOpenIcon className={`${isLargeCard ? 'w-7 h-7' : displayDensity === 'compact' ? 'w-5 h-5' : 'w-6 h-6'} text-blue-600`} />
                ) : (
                  <FolderIcon className={`${isLargeCard ? 'w-7 h-7' : displayDensity === 'compact' ? 'w-5 h-5' : 'w-6 h-6'} text-blue-600`} />
                )}
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <h3 className={`${isLargeCard ? 'text-lg' : displayDensity === 'compact' ? 'text-sm' : 'text-base'} font-semibold text-gray-900 truncate`} title={category.name}>
                {category.name}
              </h3>
              {category.description && (
                <p className={`${displayDensity === 'compact' ? 'text-xs' : 'text-sm'} text-gray-500 ${isLargeCard ? 'overflow-hidden' : 'truncate'}`}
                   style={isLargeCard ? {
                     display: '-webkit-box',
                     WebkitLineClamp: 2,
                     WebkitBoxOrient: 'vertical'
                   } : {}}
                   title={category.description}>
                  {category.description}
                </p>
              )}
            </div>
          </div>
          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full flex-shrink-0 ml-2 ${
            category.isActive
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}>
            {category.isActive ? 'Active' : 'Inactive'}
          </span>
        </div>

        <div className={`${displayDensity === 'compact' ? 'space-y-1 mb-3' : 'space-y-2 mb-4'}`}>
          <div className={`flex items-center justify-between ${displayDensity === 'compact' ? 'text-xs' : 'text-sm'}`}>
            <span className="text-gray-500">Services:</span>
            <span className="font-medium text-gray-900">{serviceCount}</span>
          </div>
          {hasChildren && (
            <div className={`flex items-center justify-between ${displayDensity === 'compact' ? 'text-xs' : 'text-sm'}`}>
              <span className="text-gray-500">Subcategories:</span>
              <span className="font-medium text-gray-900">{childrenCount}</span>
            </div>
          )}
          <div className={`flex items-center justify-between ${displayDensity === 'compact' ? 'text-xs' : 'text-sm'}`}>
            <span className="text-gray-500">Order:</span>
            <span className="font-medium text-gray-900">{category.displayOrder}</span>
          </div>
        </div>

        <div className="flex items-center justify-end space-x-1">
          <button
            onClick={(e) => {
              e.stopPropagation()
              handleEdit(category)
            }}
            className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="Edit category"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation()
              handleToggleActive(category)
            }}
            className={`p-1.5 rounded-md transition-colors focus:outline-none focus:ring-2 ${
              category.isActive
                ? 'text-green-600 hover:text-green-700 hover:bg-green-50 focus:ring-green-500'
                : 'text-gray-400 hover:text-green-600 hover:bg-green-50 focus:ring-green-500'
            }`}
            title={category.isActive ? 'Deactivate category' : 'Activate category'}
          >
            {category.isActive ? (
              <EyeIcon className="h-4 w-4" />
            ) : (
              <EyeSlashIcon className="h-4 w-4" />
            )}
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation()
              handleDelete(category)
            }}
            className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
            title="Delete category"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </motion.div>
    )
  }

  const getAllCategories = (cats: Category[]): Category[] => {
    const result: Category[] = []
    cats.forEach(cat => {
      result.push(cat)
      if (cat.children) {
        result.push(...getAllCategories(cat.children))
      }
    })
    return result
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-200 rounded-lg" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Category Management</h2>
          <p className="text-gray-600">Organize your services with categories and subcategories</p>
        </div>
        <button
          onClick={() => {
            setEditingCategory(null)
            setFormData({
              name: '',
              description: '',
              parentId: '',
              isActive: true,
              displayOrder: 0
            })
            setIsFormOpen(true)
          }}
          className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Category</span>
        </button>
      </div>

      {/* Search and Controls Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-4 mb-6">
        {/* Search and Primary Controls */}
        <div className="flex items-center justify-between space-x-4">
          <div className="flex items-center space-x-4 flex-1">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search categories by name or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
              {/* Search Loading Indicator */}
              {searchTerm !== debouncedSearchTerm && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
                </div>
              )}
              {/* Clear Search Button */}
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filters Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 border rounded-lg transition-colors flex items-center space-x-2 ${
                showFilters
                  ? 'bg-blue-50 border-blue-300 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </button>
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2 bg-gray-50 border border-gray-200 rounded-lg p-1">
            {/* View Mode Controls */}
            <div className="flex items-center bg-white rounded-md p-1">
              {Object.entries(viewModeIcons).map(([mode, Icon]) => (
                <button
                  key={mode}
                  onClick={() => setViewMode(mode as 'list' | 'grid' | 'cards')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === mode
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title={`${mode.charAt(0).toUpperCase() + mode.slice(1)} view`}
                >
                  <Icon className="h-4 w-4" />
                </button>
              ))}
            </div>

            {/* Display Density Controls */}
            <div className="relative">
              <button
                onClick={() => setShowDensityMenu(!showDensityMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Display density"
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                <span>{densityLabels[displayDensity]}</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showDensityMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[140px]"
                  >
                    {Object.entries(densityLabels).map(([density, label]) => (
                      <button
                        key={density}
                        onClick={() => {
                          setDisplayDensity(density as 'compact' | 'comfortable')
                          setShowDensityMenu(false)
                        }}
                        className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                          displayDensity === density
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-700'
                        }`}
                      >
                        {label}
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Filters Panel */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border-t border-gray-200 pt-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <select
                    value={filters.isActive || ''}
                    onChange={(e) => setFilters({ ...filters, isActive: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Categories</option>
                    <option value="true">Active Only</option>
                    <option value="false">Inactive Only</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sort By
                  </label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="name">Name</option>
                    <option value="displayOrder">Display Order</option>
                    <option value="services">Service Count</option>
                    <option value="updatedAt">Last Updated</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sort Order
                  </label>
                  <select
                    value={sortOrder}
                    onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="asc">Ascending</option>
                    <option value="desc">Descending</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                <div className="text-sm text-gray-500">
                  {filteredCategories.length} of {categories.length} categories
                </div>
                <button
                  onClick={() => {
                    setFilters({})
                    setSearchTerm('')
                    setSortBy('name')
                    setSortOrder('asc')
                  }}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  Clear all filters
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Categories Display */}
      {filteredCategories.length === 0 ? (
        <div className="text-center py-12 text-gray-500">
          <FolderIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>
            {searchTerm || Object.values(filters).some(v => v)
              ? 'No categories match your search criteria'
              : 'No categories found. Create your first category to get started.'}
          </p>
        </div>
      ) : (
        <>
          {viewMode === 'list' && (
            <div className="space-y-2">
              {filteredCategories.map(category => renderCategory(category))}
            </div>
          )}

          {viewMode === 'grid' && (
            <div className="overflow-x-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 min-w-[640px] md:min-w-0">
                {filteredCategories.map(category => renderCategoryCard(category))}
              </div>
            </div>
          )}

          {viewMode === 'cards' && (
            <div className="overflow-x-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 min-w-[640px] md:min-w-0">
                {filteredCategories.map(category => renderCategoryCard(category, true))}
              </div>
            </div>
          )}
        </>
      )}

      {/* Form Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setIsFormOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-lg p-6 w-full max-w-md mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">
                {editingCategory ? 'Edit Category' : 'Add New Category'}
              </h3>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category Name
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Parent Category
                  </label>
                  <select
                    value={formData.parentId}
                    onChange={(e) => setFormData({ ...formData, parentId: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">No Parent (Root Category)</option>
                    {getAllCategories(categories)
                      .filter(cat => cat.id !== editingCategory?.id)
                      .map(cat => (
                        <option key={cat.id} value={cat.id}>
                          {cat.name}
                        </option>
                      ))}
                  </select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Display Order
                    </label>
                    <input
                      type="number"
                      value={formData.displayOrder}
                      onChange={(e) => setFormData({ ...formData, displayOrder: Number(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div className="flex items-center">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.isActive}
                        onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium text-gray-700">Active</span>
                    </label>
                  </div>
                </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {editingCategory ? 'Update' : 'Create'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setIsFormOpen(false)}
                    className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
