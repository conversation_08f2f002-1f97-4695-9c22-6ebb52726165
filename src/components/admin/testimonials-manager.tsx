'use client'

import { useState } from 'react'
import { useAdminData } from '@/lib/hooks/use-admin-data'
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon, 
  StarIcon,
  UserCircleIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'

interface Testimonial {
  id: string
  clientName: string
  clientTitle: string
  clientCompany: string
  clientPhotoUrl?: string
  content: string
  rating: number
  isFeatured: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
}

interface TestimonialFormData {
  clientName: string
  clientTitle: string
  clientCompany: string
  clientPhotoUrl?: string
  content: string
  rating: number
  isFeatured: boolean
  displayOrder: number
}

export default function TestimonialsManager() {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingTestimonial, setEditingTestimonial] = useState<Testimonial | null>(null)
  const [formData, setFormData] = useState<TestimonialFormData>({
    clientName: '',
    clientTitle: '',
    clientCompany: '',
    clientPhotoUrl: '',
    content: '',
    rating: 5,
    isFeatured: false,
    displayOrder: 0
  })

  const {
    data: testimonials,
    loading,
    error,
    pagination,
    refetch,
    create,
    update,
    remove
  } = useAdminData<Testimonial>('testimonials')

  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault()
    const result = await create(formData)
    if (result) {
      setIsCreateModalOpen(false)
      resetForm()
    }
  }

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!editingTestimonial) return
    
    const result = await update(editingTestimonial.id, formData)
    if (result) {
      setEditingTestimonial(null)
      resetForm()
    }
  }

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this testimonial?')) {
      await remove(id)
    }
  }

  const openEditModal = (testimonial: Testimonial) => {
    setEditingTestimonial(testimonial)
    setFormData({
      clientName: testimonial.clientName,
      clientTitle: testimonial.clientTitle,
      clientCompany: testimonial.clientCompany,
      clientPhotoUrl: testimonial.clientPhotoUrl || '',
      content: testimonial.content,
      rating: testimonial.rating,
      isFeatured: testimonial.isFeatured,
      displayOrder: testimonial.displayOrder
    })
  }

  const resetForm = () => {
    setFormData({
      clientName: '',
      clientTitle: '',
      clientCompany: '',
      clientPhotoUrl: '',
      content: '',
      rating: 5,
      isFeatured: false,
      displayOrder: 0
    })
  }

  const renderStars = (rating: number, interactive: boolean = false, onChange?: (rating: number) => void) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => interactive && onChange && onChange(star)}
            className={`${interactive ? 'cursor-pointer hover:scale-110' : 'cursor-default'} transition-transform`}
            disabled={!interactive}
          >
            {star <= rating ? (
              <StarIconSolid className="h-5 w-5 text-yellow-400" />
            ) : (
              <StarIcon className="h-5 w-5 text-gray-300" />
            )}
          </button>
        ))}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white p-6 rounded-lg shadow">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-700">{error}</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Testimonials</h1>
          <p className="text-gray-600">Manage client testimonials and reviews</p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <PlusIcon className="h-5 w-5" />
          <span>Add Testimonial</span>
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-2xl font-bold text-gray-900">{testimonials.length}</div>
          <div className="text-sm text-gray-600">Total Testimonials</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-2xl font-bold text-green-600">
            {testimonials.filter(t => t.isFeatured).length}
          </div>
          <div className="text-sm text-gray-600">Featured</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-2xl font-bold text-yellow-600">
            {testimonials.length > 0 
              ? (testimonials.reduce((sum, t) => sum + t.rating, 0) / testimonials.length).toFixed(1)
              : '0.0'
            }
          </div>
          <div className="text-sm text-gray-600">Average Rating</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-2xl font-bold text-blue-600">
            {testimonials.filter(t => t.rating === 5).length}
          </div>
          <div className="text-sm text-gray-600">5-Star Reviews</div>
        </div>
      </div>

      {/* Testimonials Grid */}
      <div className="grid gap-6">
        {testimonials.map((testimonial) => (
          <div key={testimonial.id} className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center space-x-4">
                {testimonial.clientPhotoUrl ? (
                  <img
                    src={testimonial.clientPhotoUrl}
                    alt={testimonial.clientName}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                ) : (
                  <UserCircleIcon className="w-12 h-12 text-gray-400" />
                )}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{testimonial.clientName}</h3>
                  <p className="text-sm text-gray-600">{testimonial.clientTitle}</p>
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <BuildingOfficeIcon className="h-4 w-4" />
                    <span>{testimonial.clientCompany}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {testimonial.isFeatured && (
                  <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                    Featured
                  </span>
                )}
                <div className="flex space-x-1">
                  <button
                    onClick={() => openEditModal(testimonial)}
                    className="p-2 text-gray-400 hover:text-blue-600"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(testimonial.id)}
                    className="p-2 text-gray-400 hover:text-red-600"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>

            <div className="mb-4">
              {renderStars(testimonial.rating)}
            </div>

            <blockquote className="text-gray-700 italic mb-4">
              "{testimonial.content}"
            </blockquote>

            <div className="flex justify-between items-center text-sm text-gray-500">
              <span>Order: {testimonial.displayOrder}</span>
              <span>Created: {new Date(testimonial.createdAt).toLocaleDateString()}</span>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex justify-center">
          <nav className="flex space-x-2">
            {[...Array(pagination.totalPages)].map((_, i) => (
              <button
                key={i}
                className={`px-3 py-2 rounded ${
                  pagination.page === i + 1
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {i + 1}
              </button>
            ))}
          </nav>
        </div>
      )}

      {/* Create/Edit Modal */}
      {(isCreateModalOpen || editingTestimonial) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-lg font-semibold mb-4">
              {editingTestimonial ? 'Edit Testimonial' : 'Create Testimonial'}
            </h2>
            <form onSubmit={editingTestimonial ? handleUpdate : handleCreate}>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Client Name</label>
                    <input
                      type="text"
                      value={formData.clientName}
                      onChange={(e) => setFormData({ ...formData, clientName: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Client Title</label>
                    <input
                      type="text"
                      value={formData.clientTitle}
                      onChange={(e) => setFormData({ ...formData, clientTitle: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      required
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Company</label>
                  <input
                    type="text"
                    value={formData.clientCompany}
                    onChange={(e) => setFormData({ ...formData, clientCompany: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Photo URL</label>
                  <input
                    type="url"
                    value={formData.clientPhotoUrl}
                    onChange={(e) => setFormData({ ...formData, clientPhotoUrl: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Testimonial Content</label>
                  <textarea
                    value={formData.content}
                    onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    rows={4}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Rating</label>
                  {renderStars(formData.rating, true, (rating) => setFormData({ ...formData, rating }))}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Display Order</label>
                    <input
                      type="number"
                      value={formData.displayOrder}
                      onChange={(e) => setFormData({ ...formData, displayOrder: parseInt(e.target.value) })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    />
                  </div>
                  <div className="flex items-center mt-6">
                    <input
                      type="checkbox"
                      id="isFeatured"
                      checked={formData.isFeatured}
                      onChange={(e) => setFormData({ ...formData, isFeatured: e.target.checked })}
                      className="mr-2"
                    />
                    <label htmlFor="isFeatured" className="text-sm font-medium text-gray-700">
                      Featured Testimonial
                    </label>
                  </div>
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setIsCreateModalOpen(false)
                    setEditingTestimonial(null)
                    resetForm()
                  }}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  {editingTestimonial ? 'Update' : 'Create'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}
