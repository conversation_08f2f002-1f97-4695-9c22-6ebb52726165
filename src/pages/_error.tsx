import { NextPageContext } from 'next'

interface ErrorProps {
  statusCode?: number
  hasGetInitialProps?: boolean
  err?: Error
}

function Error({ statusCode, hasGetInitialProps, err }: ErrorProps) {
  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      justifyContent: 'center', 
      minHeight: '100vh',
      fontFamily: 'system-ui, sans-serif'
    }}>
      <h1>
        {statusCode
          ? `A ${statusCode} error occurred on server`
          : 'An error occurred on client'}
      </h1>
      <p>
        {statusCode === 404
          ? 'This page could not be found.'
          : 'Sorry, there was a problem.'}
      </p>
      {process.env.NODE_ENV === 'development' && err && (
        <details style={{ marginTop: '20px', maxWidth: '600px' }}>
          <summary>Error details (development only)</summary>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '10px', 
            overflow: 'auto',
            fontSize: '12px'
          }}>
            {err.stack}
          </pre>
        </details>
      )}
    </div>
  )
}

Error.getInitialProps = ({ res, err }: NextPageContext) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404
  return { statusCode }
}

export default Error
