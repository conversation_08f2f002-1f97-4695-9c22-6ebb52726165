'use client'

import { useState, useEffect, useCallback } from 'react'

interface UseProjectsOptions {
  featured?: boolean
  limit?: number
  search?: string
  filters?: {
    service?: string
    technology?: string
    featured?: boolean
  }
  autoFetch?: boolean
}

interface Project {
  id: string
  slug: string
  name: string
  description: string
  excerpt?: string
  imageUrl?: string
  projectUrl?: string
  githubUrl?: string
  startDate?: string
  completionDate?: string
  estimatedCost?: number
  isFeatured: boolean
  isPublic: boolean
  tagsArray?: string[]
  averageRating?: number
  client?: {
    companyName: string
  }
  services: Array<{
    id: string
    name: string
    description: string
  }>
  technologies: Array<{
    id: string
    name: string
    iconUrl?: string
  }>
  feedbacks: Array<{
    id: string
    rating: number
    comment: string
  }>
  _count: {
    feedbacks: number
  }
}

interface UseProjectsReturn {
  projects: Project[]
  loading: boolean
  error: string | null
  totalPages: number
  currentPage: number
  totalCount: number
  fetchProjects: () => Promise<void>
  refetch: () => Promise<void>
  setPage: (page: number) => void
  setSearch: (search: string) => void
  setFilters: (filters: any) => void
}

export function useProjects(options: UseProjectsOptions = {}): UseProjectsReturn {
  const {
    featured = false,
    limit = 12,
    search: initialSearch = '',
    filters: initialFilters = {},
    autoFetch = true
  } = options

  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [search, setSearch] = useState(initialSearch)
  const [filters, setFilters] = useState(initialFilters)

  const fetchProjects = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: limit.toString(),
      })

      if (search) {
        params.set('search', search)
      }

      if (featured) {
        params.set('featured', 'true')
      }

      if (Object.keys(filters).length > 0) {
        params.set('filter', JSON.stringify(filters))
      }

      const endpoint = featured ? '/api/projects/featured' : '/api/projects'
      const response = await fetch(`${endpoint}?${params.toString()}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch projects: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        setProjects(data.data || [])
        setTotalPages(data.pagination?.totalPages || 1)
        setTotalCount(data.pagination?.total || 0)
      } else {
        throw new Error(data.error || 'Failed to fetch projects')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      console.error('Error fetching projects:', err)
    } finally {
      setLoading(false)
    }
  }, [currentPage, limit, search, filters, featured])

  const refetch = useCallback(() => {
    return fetchProjects()
  }, [fetchProjects])

  const setPage = useCallback((page: number) => {
    setCurrentPage(page)
  }, [])

  // Auto-fetch on mount and when dependencies change
  useEffect(() => {
    if (autoFetch) {
      fetchProjects()
    }
  }, [fetchProjects, autoFetch])

  // Reset page when search or filters change
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1)
    }
  }, [search, filters])

  return {
    projects,
    loading,
    error,
    totalPages,
    currentPage,
    totalCount,
    fetchProjects,
    refetch,
    setPage,
    setSearch,
    setFilters
  }
}

// Hook for a single project
export function useProject(slug: string) {
  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchProject = useCallback(async () => {
    if (!slug) return

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/projects/${slug}`)

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Project not found')
        }
        throw new Error(`Failed to fetch project: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        setProject(data.data)
      } else {
        throw new Error(data.error || 'Failed to fetch project')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      console.error('Error fetching project:', err)
    } finally {
      setLoading(false)
    }
  }, [slug])

  useEffect(() => {
    fetchProject()
  }, [fetchProject])

  return {
    project,
    loading,
    error,
    refetch: fetchProject
  }
}

// Hook for featured projects (for landing page)
export function useFeaturedProjects(limit: number = 6) {
  return useProjects({
    featured: true,
    limit,
    autoFetch: true
  })
}
