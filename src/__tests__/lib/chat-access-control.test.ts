/**
 * @jest-environment node
 */

import { createMocks } from 'node-mocks-http'
import { NextRequest } from 'next/server'
import {
  getAuthenticatedUser,
  validateContactFormAccess,
  validateChatAccess,
  filterMessagesForUser,
  getAllowedRecipients,
  canUserMessage
} from '@/lib/chat-access-control'
import { getServerSession } from 'next-auth'
import { prisma } from '@/lib/prisma'

// Mock dependencies
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}))

jest.mock('@/lib/prisma', () => ({
  prisma: {
    contactforms: {
      findUnique: jest.fn(),
    },
    users: {
      findMany: jest.fn(),
    },
  },
}))

jest.mock('@/lib/auth', () => ({
  authOptions: {},
}))

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>
const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('Chat Access Control', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('getAuthenticatedUser', () => {
    it('should return user for valid session', async () => {
      const mockUser = { id: '1', email: '<EMAIL>', role: 'ADMIN' }
      mockGetServerSession.mockResolvedValue({ user: mockUser } as any)

      const { req } = createMocks({ method: 'GET' })
      const request = new NextRequest(req.url!, { method: req.method })

      const user = await getAuthenticatedUser(request)

      expect(user).toEqual(mockUser)
    })

    it('should throw error for invalid session', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const { req } = createMocks({ method: 'GET' })
      const request = new NextRequest(req.url!, { method: req.method })

      await expect(getAuthenticatedUser(request)).rejects.toThrow('Authentication required')
    })
  })

  describe('validateContactFormAccess', () => {
    const mockUser = { id: '1', email: '<EMAIL>', role: 'ADMIN' }
    const mockContactForm = {
      id: BigInt(1),
      senderid: BigInt(2),
      receiverid: BigInt(1),
      subject: 'Test',
    }

    it('should allow admin access to any contact form', async () => {
      mockPrisma.contactforms.findUnique.mockResolvedValue(mockContactForm as any)

      const result = await validateContactFormAccess(mockUser as any, 1)

      expect(result).toEqual(mockContactForm)
    })

    it('should allow user access to their own contact forms', async () => {
      const userMock = { id: '2', email: '<EMAIL>', role: 'USER' }
      mockPrisma.contactforms.findUnique.mockResolvedValue(mockContactForm as any)

      const result = await validateContactFormAccess(userMock as any, 1)

      expect(result).toEqual(mockContactForm)
    })

    it('should deny access to unauthorized users', async () => {
      const unauthorizedUser = { id: '3', email: '<EMAIL>', role: 'USER' }
      mockPrisma.contactforms.findUnique.mockResolvedValue(mockContactForm as any)

      await expect(validateContactFormAccess(unauthorizedUser as any, 1))
        .rejects.toThrow('Access denied to this contact form')
    })

    it('should throw error for non-existent contact form', async () => {
      mockPrisma.contactforms.findUnique.mockResolvedValue(null)

      await expect(validateContactFormAccess(mockUser as any, 999))
        .rejects.toThrow('Contact form not found')
    })
  })

  describe('canUserMessage', () => {
    it('should allow admin to message anyone', () => {
      const admin = { role: 'ADMIN' } as any
      const target = { role: 'USER' } as any

      expect(canUserMessage(admin, target)).toBe(true)
    })

    it('should allow user to message admin', () => {
      const user = { role: 'USER' } as any
      const admin = { role: 'ADMIN' } as any

      expect(canUserMessage(user, admin)).toBe(true)
    })

    it('should allow client to message admin', () => {
      const client = { role: 'CLIENT' } as any
      const admin = { role: 'ADMIN' } as any

      expect(canUserMessage(client, admin)).toBe(true)
    })

    it('should not allow user to message another user', () => {
      const user1 = { role: 'USER' } as any
      const user2 = { role: 'USER' } as any

      expect(canUserMessage(user1, user2)).toBe(false)
    })

    it('should not allow client to message another client', () => {
      const client1 = { role: 'CLIENT' } as any
      const client2 = { role: 'CLIENT' } as any

      expect(canUserMessage(client1, client2)).toBe(false)
    })
  })

  describe('filterMessagesForUser', () => {
    const messages = [
      {
        id: 1,
        senderid: 1,
        receiverid: 2,
        message: 'Admin to User',
        sender: { id: 1, role: 'ADMIN' },
        receiver: { id: 2, role: 'USER' },
      },
      {
        id: 2,
        senderid: 2,
        receiverid: 1,
        message: 'User to Admin',
        sender: { id: 2, role: 'USER' },
        receiver: { id: 1, role: 'ADMIN' },
      },
      {
        id: 3,
        senderid: 3,
        receiverid: 4,
        message: 'Other conversation',
        sender: { id: 3, role: 'USER' },
        receiver: { id: 4, role: 'ADMIN' },
      },
    ] as any

    it('should return all messages for admin', () => {
      const admin = { id: '1', role: 'ADMIN' } as any
      const filtered = filterMessagesForUser(messages, admin)

      expect(filtered).toHaveLength(3)
    })

    it('should return only relevant messages for user', () => {
      const user = { id: '2', role: 'USER' } as any
      const filtered = filterMessagesForUser(messages, user)

      expect(filtered).toHaveLength(2)
      expect(filtered.every(m => m.senderid === 2 || m.receiverid === 2)).toBe(true)
    })

    it('should return empty array for unrelated user', () => {
      const user = { id: '5', role: 'USER' } as any
      const filtered = filterMessagesForUser(messages, user)

      expect(filtered).toHaveLength(0)
    })
  })

  describe('getAllowedRecipients', () => {
    it('should return all users for admin', async () => {
      const admin = { id: '1', role: 'ADMIN' } as any
      const mockUsers = [
        { id: 1, role: 'ADMIN' },
        { id: 2, role: 'USER' },
        { id: 3, role: 'CLIENT' },
      ]

      mockPrisma.users.findMany.mockResolvedValue(mockUsers as any)

      const recipients = await getAllowedRecipients(admin)

      expect(recipients).toHaveLength(3)
      expect(mockPrisma.users.findMany).toHaveBeenCalledWith({
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          role: true,
        },
      })
    })

    it('should return only admins for regular user', async () => {
      const user = { id: '2', role: 'USER' } as any
      const mockAdmins = [
        { id: 1, role: 'ADMIN' },
      ]

      mockPrisma.users.findMany.mockResolvedValue(mockAdmins as any)

      const recipients = await getAllowedRecipients(user)

      expect(recipients).toHaveLength(1)
      expect(mockPrisma.users.findMany).toHaveBeenCalledWith({
        where: { role: 'ADMIN' },
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          role: true,
        },
      })
    })

    it('should return only admins for client', async () => {
      const client = { id: '3', role: 'CLIENT' } as any
      const mockAdmins = [
        { id: 1, role: 'ADMIN' },
      ]

      mockPrisma.users.findMany.mockResolvedValue(mockAdmins as any)

      const recipients = await getAllowedRecipients(client)

      expect(recipients).toHaveLength(1)
      expect(mockPrisma.users.findMany).toHaveBeenCalledWith({
        where: { role: 'ADMIN' },
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          role: true,
        },
      })
    })
  })

  describe('validateChatAccess', () => {
    it('should validate access and return context', async () => {
      const mockUser = { id: '1', email: '<EMAIL>', role: 'ADMIN' }
      const mockContactForm = {
        id: BigInt(1),
        senderid: BigInt(2),
        receiverid: BigInt(1),
        subject: 'Test',
      }

      mockGetServerSession.mockResolvedValue({ user: mockUser } as any)
      mockPrisma.contactforms.findUnique.mockResolvedValue(mockContactForm as any)

      const { req } = createMocks({ method: 'GET' })
      const request = new NextRequest(req.url!, { method: req.method })

      const context = await validateChatAccess(request, 1)

      expect(context.user).toEqual(mockUser)
      expect(context.contactForm).toEqual(mockContactForm)
    })

    it('should validate messaging permissions when target user specified', async () => {
      const mockUser = { id: '2', email: '<EMAIL>', role: 'USER' }
      const mockContactForm = {
        id: BigInt(1),
        senderid: BigInt(2),
        receiverid: BigInt(1),
        subject: 'Test',
      }
      const mockTargetUser = { id: 1, role: 'ADMIN' }

      mockGetServerSession.mockResolvedValue({ user: mockUser } as any)
      mockPrisma.contactforms.findUnique.mockResolvedValue(mockContactForm as any)
      mockPrisma.users.findMany.mockResolvedValue([mockTargetUser] as any)

      const { req } = createMocks({ method: 'GET' })
      const request = new NextRequest(req.url!, { method: req.method })

      const context = await validateChatAccess(request, 1, 1)

      expect(context.user).toEqual(mockUser)
      expect(context.contactForm).toEqual(mockContactForm)
      expect(context.targetUser).toEqual(mockTargetUser)
    })
  })
})
