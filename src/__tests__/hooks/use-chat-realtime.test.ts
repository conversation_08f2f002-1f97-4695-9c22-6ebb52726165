import { renderHook, act, waitFor } from '@testing-library/react'
import { useSession } from 'next-auth/react'
import { useChatRealtime } from '@/hooks/use-chat-realtime'

// Mock dependencies
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}))

// Mock fetch
global.fetch = jest.fn()

const mockUseSession = useSession as jest.MockedFunction<typeof useSession>
const mockFetch = fetch as jest.MockedFunction<typeof fetch>

describe('useChatRealtime', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  const mockMessages = [
    {
      id: 1,
      message: 'Hello',
      createdAt: new Date(),
      sender: { id: 1, email: '<EMAIL>', role: 'USER' },
      receiver: { id: 2, email: '<EMAIL>', role: 'ADMIN' },
    },
    {
      id: 2,
      message: 'Hi there',
      createdAt: new Date(),
      sender: { id: 2, email: '<EMAIL>', role: 'ADMIN' },
      receiver: { id: 1, email: '<EMAIL>', role: 'USER' },
    },
  ]

  it('should fetch messages on mount for admin users', async () => {
    mockUseSession.mockReturnValue({
      data: { user: { id: '1', role: 'ADMIN' } },
    } as any)

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { messages: mockMessages, threadId: '1' },
      }),
    } as any)

    const { result } = renderHook(() =>
      useChatRealtime({
        contactFormId: 1,
        enabled: true,
      })
    )

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(mockFetch).toHaveBeenCalledWith('/api/admin/contact-forms/1/messages')
    expect(result.current.messages).toEqual(mockMessages)
    expect(result.current.error).toBeNull()
  })

  it('should use client API for non-admin users', async () => {
    mockUseSession.mockReturnValue({
      data: { user: { id: '1', role: 'USER' } },
    } as any)

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { messages: mockMessages, threadId: '1' },
      }),
    } as any)

    const { result } = renderHook(() =>
      useChatRealtime({
        contactFormId: 1,
        enabled: true,
      })
    )

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(mockFetch).toHaveBeenCalledWith('/api/chat/contact-forms/1/messages')
  })

  it('should poll for new messages at specified interval', async () => {
    mockUseSession.mockReturnValue({
      data: { user: { id: '1', role: 'ADMIN' } },
    } as any)

    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        success: true,
        data: { messages: mockMessages, threadId: '1' },
      }),
    } as any)

    renderHook(() =>
      useChatRealtime({
        contactFormId: 1,
        enabled: true,
        pollingInterval: 1000,
      })
    )

    // Initial fetch
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledTimes(1)
    })

    // Advance timer and check for polling
    act(() => {
      jest.advanceTimersByTime(1000)
    })

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledTimes(2)
    })
  })

  it('should send messages correctly', async () => {
    mockUseSession.mockReturnValue({
      data: { user: { id: '1', role: 'ADMIN' } },
    } as any)

    // Mock initial fetch
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { messages: [], threadId: '1' },
      }),
    } as any)

    // Mock send message
    const newMessage = {
      id: 3,
      message: 'New message',
      createdAt: new Date(),
      sender: { id: 1, email: '<EMAIL>', role: 'ADMIN' },
    }

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { message: newMessage, threadId: '1' },
      }),
    } as any)

    const { result } = renderHook(() =>
      useChatRealtime({
        contactFormId: 1,
        enabled: true,
      })
    )

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    await act(async () => {
      await result.current.sendMessage('New message', [])
    })

    expect(mockFetch).toHaveBeenCalledWith('/api/admin/contact-forms/1/messages', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: 'New message',
        attachments: [],
        messagetype: 'chat',
        contenttype: 'text',
      }),
    })
  })

  it('should mark messages as read', async () => {
    mockUseSession.mockReturnValue({
      data: { user: { id: '1', role: 'ADMIN' } },
    } as any)

    // Mock initial fetch
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { messages: mockMessages, threadId: '1' },
      }),
    } as any)

    // Mock mark as read
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { message: { ...mockMessages[0], isread: true } },
      }),
    } as any)

    const { result } = renderHook(() =>
      useChatRealtime({
        contactFormId: 1,
        enabled: true,
      })
    )

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    await act(async () => {
      await result.current.markAsRead(1)
    })

    expect(mockFetch).toHaveBeenCalledWith('/api/admin/contact-forms/messages/1/status', {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ isread: true }),
    })
  })

  it('should handle errors gracefully', async () => {
    mockUseSession.mockReturnValue({
      data: { user: { id: '1', role: 'ADMIN' } },
    } as any)

    mockFetch.mockRejectedValueOnce(new Error('Network error'))

    const { result } = renderHook(() =>
      useChatRealtime({
        contactFormId: 1,
        enabled: true,
      })
    )

    await waitFor(() => {
      expect(result.current.error).toBe('Network error')
    })

    expect(result.current.messages).toEqual([])
    expect(result.current.isLoading).toBe(false)
  })

  it('should not fetch when disabled', () => {
    mockUseSession.mockReturnValue({
      data: { user: { id: '1', role: 'ADMIN' } },
    } as any)

    renderHook(() =>
      useChatRealtime({
        contactFormId: 1,
        enabled: false,
      })
    )

    expect(mockFetch).not.toHaveBeenCalled()
  })

  it('should call onNewMessage callback when new messages arrive', async () => {
    const onNewMessage = jest.fn()

    mockUseSession.mockReturnValue({
      data: { user: { id: '1', role: 'ADMIN' } },
    } as any)

    // First fetch - empty messages
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { messages: [], threadId: '1' },
      }),
    } as any)

    // Second fetch - with new message
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { messages: [mockMessages[0]], threadId: '1' },
      }),
    } as any)

    renderHook(() =>
      useChatRealtime({
        contactFormId: 1,
        enabled: true,
        pollingInterval: 1000,
        onNewMessage,
      })
    )

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledTimes(1)
    })

    // Advance timer to trigger polling
    act(() => {
      jest.advanceTimersByTime(1000)
    })

    await waitFor(() => {
      expect(onNewMessage).toHaveBeenCalledWith(mockMessages[0])
    })
  })

  it('should pause polling when page is not visible', async () => {
    mockUseSession.mockReturnValue({
      data: { user: { id: '1', role: 'ADMIN' } },
    } as any)

    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        success: true,
        data: { messages: [], threadId: '1' },
      }),
    } as any)

    renderHook(() =>
      useChatRealtime({
        contactFormId: 1,
        enabled: true,
        pollingInterval: 1000,
      })
    )

    // Initial fetch
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledTimes(1)
    })

    // Simulate page becoming hidden
    Object.defineProperty(document, 'hidden', {
      writable: true,
      value: true,
    })

    act(() => {
      document.dispatchEvent(new Event('visibilitychange'))
    })

    // Advance timer - should not poll when hidden
    act(() => {
      jest.advanceTimersByTime(1000)
    })

    // Should still be only 1 call (initial)
    expect(mockFetch).toHaveBeenCalledTimes(1)
  })
})
