/**
 * @jest-environment node
 */

import { createMocks } from 'node-mocks-http'
import { NextRequest } from 'next/server'
import { GET, POST } from '@/app/api/admin/contact-forms/[id]/messages/route'
import { GET as ClientGET, POST as ClientPOST } from '@/app/api/chat/contact-forms/[id]/messages/route'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    contactforms: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    users: {
      findFirst: jest.fn(),
    },
  },
}))

jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}))

jest.mock('@/app/api/auth/[...nextauth]/route', () => ({
  authOptions: {},
}))

const mockPrisma = prisma as jest.Mocked<typeof prisma>
const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>

describe('/api/admin/contact-forms/[id]/messages', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET - Admin Access', () => {
    it('should return messages for admin users', async () => {
      // Mock admin session
      mockGetServerSession.mockResolvedValue({
        user: { id: '1', email: '<EMAIL>', role: 'ADMIN' },
      } as any)

      // Mock contact form
      mockPrisma.contactforms.findUnique.mockResolvedValue({
        id: BigInt(1),
        threadid: BigInt(1),
        subject: 'Test Subject',
      } as any)

      // Mock messages
      mockPrisma.contactforms.findMany.mockResolvedValue([
        {
          id: BigInt(1),
          subject: 'Test Subject',
          message: 'Test message',
          createdat: new Date(),
          sender: { id: 1, email: '<EMAIL>', role: 'USER' },
          receiver: { id: 2, email: '<EMAIL>', role: 'ADMIN' },
          parent: null,
          attachments: null,
        },
      ] as any)

      const { req } = createMocks({
        method: 'GET',
        url: '/api/admin/contact-forms/1/messages',
      })

      const request = new NextRequest('http://localhost:3000/api/admin/contact-forms/1/messages', {
        method: req.method,
        headers: req.headers as any,
      })

      const response = await GET(request, { params: Promise.resolve({ id: '1' }) })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.messages).toHaveLength(1)
      expect(data.data.threadId).toBe('1')
    })

    it('should return 404 for non-existent contact form', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: '1', email: '<EMAIL>', role: 'ADMIN' },
      } as any)

      mockPrisma.contactforms.findUnique.mockResolvedValue(null)

      const { req } = createMocks({
        method: 'GET',
        url: '/api/admin/contact-forms/999/messages',
      })

      const request = new NextRequest('http://localhost:3000/api/admin/contact-forms/999/messages', {
        method: req.method,
        headers: req.headers as any,
      })

      const response = await GET(request, { params: Promise.resolve({ id: '999' }) })
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.success).toBe(false)
    })
  })

  describe('POST - Admin Access', () => {
    it('should create a new message for admin users', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: '1', email: '<EMAIL>', role: 'ADMIN' },
      } as any)

      mockPrisma.contactforms.findUnique.mockResolvedValue({
        id: BigInt(1),
        threadid: BigInt(1),
        subject: 'Test Subject',
        senderid: BigInt(2),
      } as any)

      const newMessage = {
        id: BigInt(2),
        subject: 'Re: Test Subject',
        message: 'Reply message',
        createdat: new Date(),
        sender: { id: 1, email: '<EMAIL>', role: 'ADMIN' },
        receiver: { id: 2, email: '<EMAIL>', role: 'USER' },
        parent: null,
        attachments: null,
      }

      mockPrisma.contactforms.create.mockResolvedValue(newMessage as any)

      const { req } = createMocks({
        method: 'POST',
        url: '/api/admin/contact-forms/1/messages',
        body: {
          message: 'Reply message',
          contenttype: 'text',
          attachments: [],
          messagetype: 'chat',
        },
      })

      const request = new NextRequest('http://localhost:3000/api/admin/contact-forms/1/messages', {
        method: req.method,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(req.body),
      })

      const response = await POST(request, { params: Promise.resolve({ id: '1' }) })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.message.message).toBe('Reply message')
    })
  })
})

describe('/api/chat/contact-forms/[id]/messages', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET - Client Access', () => {
    it('should return messages for authenticated users', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: '2', email: '<EMAIL>', role: 'USER' },
      } as any)

      mockPrisma.contactforms.findUnique.mockResolvedValue({
        id: BigInt(1),
        threadid: BigInt(1),
        subject: 'Test Subject',
        senderid: BigInt(2),
      } as any)

      mockPrisma.contactforms.findMany.mockResolvedValue([
        {
          id: BigInt(1),
          subject: 'Test Subject',
          message: 'Test message',
          createdat: new Date(),
          senderid: BigInt(2),
          receiverid: BigInt(1),
          sender: { id: 2, email: '<EMAIL>', role: 'USER' },
          receiver: { id: 1, email: '<EMAIL>', role: 'ADMIN' },
          parent: null,
          attachments: null,
        },
      ] as any)

      const { req } = createMocks({
        method: 'GET',
        url: '/api/chat/contact-forms/1/messages',
      })

      const request = new NextRequest('http://localhost:3000/api/chat/contact-forms/1/messages', {
        method: req.method,
        headers: req.headers as any,
      })

      const response = await ClientGET(request, { params: Promise.resolve({ id: '1' }) })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.messages).toHaveLength(1)
    })

    it('should deny access to unauthorized users', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: '3', email: '<EMAIL>', role: 'USER' },
      } as any)

      mockPrisma.contactforms.findUnique.mockResolvedValue({
        id: BigInt(1),
        threadid: BigInt(1),
        subject: 'Test Subject',
        senderid: BigInt(2),
        receiverid: BigInt(1),
      } as any)

      const { req } = createMocks({
        method: 'GET',
        url: '/api/chat/contact-forms/1/messages',
      })

      const request = new NextRequest('http://localhost:3000/api/chat/contact-forms/1/messages', {
        method: req.method,
        headers: req.headers as any,
      })

      const response = await ClientGET(request, { params: Promise.resolve({ id: '1' }) })
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.success).toBe(false)
    })
  })

  describe('POST - Client Access', () => {
    it('should create a new message for authenticated users', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: '2', email: '<EMAIL>', role: 'USER', firstname: 'John', lastname: 'Doe' },
      } as any)

      mockPrisma.contactforms.findUnique.mockResolvedValue({
        id: BigInt(1),
        threadid: BigInt(1),
        subject: 'Test Subject',
        senderid: BigInt(2),
      } as any)

      mockPrisma.users.findFirst.mockResolvedValue({
        id: 1,
      } as any)

      const newMessage = {
        id: BigInt(2),
        subject: 'Re: Test Subject',
        message: 'User reply',
        createdat: new Date(),
        sender: { id: 2, email: '<EMAIL>', role: 'USER' },
        receiver: { id: 1, email: '<EMAIL>', role: 'ADMIN' },
        parent: null,
        attachments: null,
      }

      mockPrisma.contactforms.create.mockResolvedValue(newMessage as any)

      const { req } = createMocks({
        method: 'POST',
        url: '/api/chat/contact-forms/1/messages',
        body: {
          message: 'User reply',
          contenttype: 'text',
          attachments: [],
          messagetype: 'chat',
        },
      })

      const request = new NextRequest('http://localhost:3000/api/chat/contact-forms/1/messages', {
        method: req.method,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(req.body),
      })

      const response = await ClientPOST(request, { params: Promise.resolve({ id: '1' }) })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.message.message).toBe('User reply')
    })
  })
})
