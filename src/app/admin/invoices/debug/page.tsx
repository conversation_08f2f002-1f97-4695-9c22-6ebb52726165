'use client'

import { useState, useEffect } from 'react'

export default function InvoicesDebugPage() {
  const [apiResults, setApiResults] = useState<any>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    testAPIs()
  }, [])

  const testAPIs = async () => {
    const results: any = {}
    
    // Test mock API
    try {
      console.log('Testing mock API...')
      const mockResponse = await fetch('/api/admin/invoices-mock')
      results.mock = {
        status: mockResponse.status,
        ok: mockResponse.ok,
        data: mockResponse.ok ? await mockResponse.json() : await mockResponse.text()
      }
    } catch (error) {
      results.mock = { error: error instanceof Error ? error.message : 'Unknown error' }
    }

    // Test real API
    try {
      console.log('Testing real API...')
      const realResponse = await fetch('/api/admin/invoices')
      results.real = {
        status: realResponse.status,
        ok: realResponse.ok,
        data: realResponse.ok ? await realResponse.json() : await realResponse.text()
      }
    } catch (error) {
      results.real = { error: error instanceof Error ? error.message : 'Unknown error' }
    }

    // Test database test API
    try {
      console.log('Testing database test API...')
      const testResponse = await fetch('/api/test/invoices')
      results.test = {
        status: testResponse.status,
        ok: testResponse.ok,
        data: testResponse.ok ? await testResponse.json() : await testResponse.text()
      }
    } catch (error) {
      results.test = { error: error instanceof Error ? error.message : 'Unknown error' }
    }

    setApiResults(results)
    setLoading(false)
  }

  if (loading) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Testing APIs...</h1>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Invoice API Debug Page</h1>
      
      <div className="space-y-6">
        {/* Mock API Results */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3">Mock API (/api/admin/invoices-mock)</h2>
          <div className="bg-gray-100 p-4 rounded">
            <pre className="text-sm overflow-auto">
              {JSON.stringify(apiResults.mock, null, 2)}
            </pre>
          </div>
        </div>

        {/* Real API Results */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3">Real API (/api/admin/invoices)</h2>
          <div className="bg-gray-100 p-4 rounded">
            <pre className="text-sm overflow-auto">
              {JSON.stringify(apiResults.real, null, 2)}
            </pre>
          </div>
        </div>

        {/* Test API Results */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3">Test API (/api/test/invoices)</h2>
          <div className="bg-gray-100 p-4 rounded">
            <pre className="text-sm overflow-auto">
              {JSON.stringify(apiResults.test, null, 2)}
            </pre>
          </div>
        </div>

        {/* Retry Button */}
        <div className="text-center">
          <button
            onClick={() => {
              setLoading(true)
              testAPIs()
            }}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            Retry Tests
          </button>
        </div>
      </div>
    </div>
  )
}
