# Visual Editor

A modern, intuitive content editor interface built with React and Next.js that allows admins to visually edit text content across the website.

## Features

### 🎯 Core Functionality
- **Live Preview**: Real-time iframe preview of the website
- **Interactive Editing**: Click on any text element to edit it inline
- **Visual Feedback**: Hover effects and selection indicators
- **Responsive Preview**: Desktop, tablet, and mobile view modes

### 🛠️ Editor Tools
- **Edit Mode**: Toggle interactive editing mode
- **Draft Mode**: Work on changes without affecting live content
- **Undo/Redo**: Full change history with undo/redo functionality
- **Change History**: Track all modifications with timestamps
- **Publish Changes**: Deploy changes to production

### 📱 User Interface
- **Collapsible Sidebar**: Page navigation and metadata
- **Modern Toolbar**: Quick access to editing tools
- **Floating Edit Modal**: Clean, contextual editing interface
- **History Panel**: Detailed change tracking and management

## Usage

### Getting Started
1. Navigate to `/admin/visual-editor` in the admin panel
2. Select a page from the left sidebar
3. Click "Edit Mode" in the toolbar to enable interactive editing
4. Click on any text element to edit it
5. Save changes and publish when ready

### Editing Workflow
1. **Select Page**: Choose the page you want to edit from the sidebar
2. **Enable Edit Mode**: Click the "Edit Mode" button in the toolbar
3. **Edit Content**: Click on any text element to open the edit modal
4. **Review Changes**: Check the history panel to see all modifications
5. **Publish**: Click "Publish Changes" to deploy to production

### Preview Modes
- **Desktop**: Full-width preview (default)
- **Tablet**: Medium-width preview for tablet testing
- **Mobile**: Narrow preview for mobile testing

### Keyboard Shortcuts
- `Ctrl/Cmd + Z`: Undo last change
- `Ctrl/Cmd + Y`: Redo last change
- `Escape`: Cancel current edit
- `Enter`: Save current edit

## Technical Details

### Architecture
- **Frontend**: React with TypeScript and Tailwind CSS
- **State Management**: React hooks for local state
- **Animations**: Framer Motion for smooth transitions
- **Icons**: Heroicons for consistent iconography

### Components
- `Toolbar`: Main editing controls and preview modes
- `Sidebar`: Page navigation and metadata
- `HistoryPanel`: Change tracking and management
- `EditModal`: Floating text editing interface

### Data Flow
1. **Page Selection**: Load page data and set preview URL
2. **Interactive Mode**: Inject JavaScript into iframe for element selection
3. **Element Selection**: Capture element data and position edit modal
4. **Content Editing**: Update content via API and sync to iframe
5. **Change Tracking**: Record all modifications in history
6. **Publishing**: Deploy changes to production

### API Endpoints
- `GET /api/content/pages`: Fetch available pages
- `POST /api/content/update`: Update element content
- `POST /api/content/publish`: Publish changes to production

## Customization

### Adding New Page Types
1. Update the `getFallbackPagesData()` function
2. Add corresponding API endpoints
3. Update the page icon mapping

### Styling
The editor uses Tailwind CSS classes and can be customized by:
- Modifying component styles in the respective files
- Updating the color scheme in the Tailwind config
- Adding custom CSS classes for specific elements

### Extending Functionality
- Add new edit modes (style, layout, etc.)
- Implement real-time collaboration
- Add version control and branching
- Integrate with external content management systems

## Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Performance Considerations
- Lazy loading of page content
- Debounced save operations
- Efficient iframe communication
- Optimized change tracking

## Security
- Admin-only access required
- CSRF protection on API endpoints
- Input sanitization for content updates
- Secure iframe communication

## Troubleshooting

### Common Issues
1. **Iframe not loading**: Check CORS settings and page availability
2. **Edit mode not working**: Ensure JavaScript injection is successful
3. **Changes not saving**: Verify API endpoint availability and permissions
4. **History not updating**: Check state management and API responses

### Debug Mode
Enable debug logging by setting `console.log` statements in the component files for detailed troubleshooting.

## Contributing
When contributing to the visual editor:
1. Follow the existing code style and patterns
2. Add TypeScript types for new features
3. Include proper error handling
4. Test across different browsers and devices
5. Update documentation for new features 