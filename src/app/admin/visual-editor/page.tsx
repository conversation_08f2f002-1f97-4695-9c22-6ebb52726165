'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import {
  XMarkIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import EditModal from '@/components/admin/visual-editor/edit-modal'
import ContentAwareIframe from '@/components/admin/visual-editor/content-aware-iframe'
import DirectPreview from '@/components/admin/visual-editor/direct-preview'
import TsxFilePreview from '@/components/admin/visual-editor/tsx-file-preview'
import RichTextEditor from '@/components/admin/visual-editor/rich-text-editor'

interface PageData {
  id: string
  name: string
  path: string
  description: string
  lastModified: string
  status: 'draft' | 'published'
}

interface EditableElement {
  id: string
  text: string
  description: string
  filePath: string
  line?: number
  rect: DOMRect
  className?: string
  elementPath?: string
  selector?: string
  tagName?: string
  sourceLocation?: string
  lineNumber?: number
  columnStart?: number
  elementInfo?: {
    type: 'getContent' | 'stats' | 'const' | 'general' | 'database'
    page?: string
    section?: string
    key?: string
    varName?: string
  }
}

interface ChangeHistory {
  id: string
  type: 'text' | 'style' | 'layout'
  description: string
  timestamp: Date
  elementId: string
  oldValue: string
  newValue: string
}

interface TsxFileData {
  filePath: string
  content: string
  lastModified: string
}

interface HighlightedText {
  id: string
  text: string
  startLine: number
  endLine: number
  startColumn: number
  endColumn: number
  filePath: string
}

function VisualEditor() {
  const { data: session, status } = useSession()
  const [previewUrl, setPreviewUrl] = useState('')
  const [interactiveMode, setInteractiveMode] = useState(false)
  const [selectedElement, setSelectedElement] = useState<EditableElement | null>(null)
  const [editingValue, setEditingValue] = useState('')
  const [editFormPosition, setEditFormPosition] = useState({ x: 0, y: 0 })
  const [isClient, setIsClient] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [pages, setPages] = useState<any[]>([])
  const [selectedPage, setSelectedPage] = useState('')

  // New Text Selection and Editing States
  const [selectedText, setSelectedText] = useState('')
  const [contextMenuPosition, setContextMenuPosition] = useState<{x: number, y: number} | null>(null)
  const [isEditFormOpen, setIsEditFormOpen] = useState(false)

  // TSX File Editor States
  const [showTsxEditor, setShowTsxEditor] = useState(false)
  const [selectedTsxFile, setSelectedTsxFile] = useState<TsxFileData | null>(null)
  const [tsxFiles, setTsxFiles] = useState<TsxFileData[]>([])
  const [highlightedText, setHighlightedText] = useState<HighlightedText | null>(null)
  const [highlightedMatches, setHighlightedMatches] = useState<{
    text: string
    filePath: string
    lineNumber: number
    startColumn?: number
    endColumn?: number
    confidence: number
  }[]>([])
  const [tsxEditingValue, setTsxEditingValue] = useState('')

  // Notification State
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'warning' | 'info'
    message: string
  } | null>(null)

  // Auto-dismiss notification after 5 seconds
  useEffect(() => {
    if (notification) {
      const timer = setTimeout(() => {
        setNotification(null)
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [notification])

  // Handle text selection and right-click context menu
  const handleTextSelection = useCallback((event: MouseEvent) => {
    const selection = window.getSelection()
    if (selection && selection.toString().trim().length > 0) {
      const selectedText = selection.toString().trim()
      setSelectedText(selectedText)

      // Store the selection position for context menu
      setContextMenuPosition({
        x: event.clientX,
        y: event.clientY
      })
    }
  }, [])

  const handleContextMenu = useCallback((event: MouseEvent) => {
    event.preventDefault()

    const selection = window.getSelection()
    if (selection && selection.toString().trim().length > 0) {
      const selectedText = selection.toString().trim()
      setSelectedText(selectedText)
      setContextMenuPosition({
        x: event.clientX,
        y: event.clientY
      })
      setIsEditFormOpen(true)
    }
  }, [])

  // Close context menu when clicking elsewhere
  const handleClickOutside = useCallback((event: MouseEvent) => {
    setContextMenuPosition(null)
    setIsEditFormOpen(false)
  }, [])

  // History and Undo/Redo States
  const [changeHistory, setChangeHistory] = useState<ChangeHistory[]>([])
  const [undoStack, setUndoStack] = useState<ChangeHistory[]>([])
  const [redoStack, setRedoStack] = useState<ChangeHistory[]>([])
  const [draftMode, setDraftMode] = useState(false)

  // iframeRef is no longer needed as we use ContentAwareIframe

  // Detect development mode
  const [isDev, setIsDev] = useState(false)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsDev(process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost')
    }
  }, [])

  // Attach event listeners for text selection
  useEffect(() => {
    const iframe = document.querySelector('iframe')
    if (iframe && iframe.contentDocument) {
      const iframeDoc = iframe.contentDocument

      // Add event listeners to iframe content
      iframeDoc.addEventListener('mouseup', handleTextSelection)
      iframeDoc.addEventListener('contextmenu', handleContextMenu)
      iframeDoc.addEventListener('click', handleClickOutside)

      return () => {
        iframeDoc.removeEventListener('mouseup', handleTextSelection)
        iframeDoc.removeEventListener('contextmenu', handleContextMenu)
        iframeDoc.removeEventListener('click', handleClickOutside)
      }
    }

    // Also add to main document
    document.addEventListener('click', handleClickOutside)
    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [handleTextSelection, handleContextMenu, handleClickOutside])

  // Set client-side flag to prevent hydration issues
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (status === 'loading') return
    if (!session?.user || session.user.role !== 'ADMIN') {
      redirect('/auth/signin')
    }
  }, [session, status])

  // Load pages
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.role === 'ADMIN') {
      loadPages()
    }
  }, [session, status])

  // Load TSX files and initialize preview URL
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.role === 'ADMIN') {
      loadTsxFiles()
      if (typeof window !== 'undefined') {
        const baseUrl = `${window.location.protocol}//${window.location.host}`
        setPreviewUrl(`${baseUrl}/`)
        setSelectedPage('home') // Default to home page
      }
    }
  }, [session, status])

  // Content-aware iframe handles its own script injection
  useEffect(() => {
    console.log('Interactive mode changed:', interactiveMode);
  }, [interactiveMode])

  const loadPages = async () => {
    try {
      const response = await fetch('/api/content/pages', {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.pages) {
          setPages(data.pages)
        }
      }
    } catch (error) {
      console.error('Error loading pages:', error)
    }
  }

  const loadTsxFiles = async () => {
    try {
      const response = await fetch('/api/content/tsx-files', {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.files) {
          setTsxFiles(data.files)
        }
      }
    } catch (error) {
      console.error('Error loading TSX files:', error)
    }
  }

  const enableInteractiveMode = () => {
    console.log('Enabling interactive mode')
    setInteractiveMode(true)
  }

  const disableInteractiveMode = () => {
    console.log('Disabling interactive mode')
    setInteractiveMode(false)
  }

  const handleElementSelection = async (element: EditableElement) => {
    setSelectedElement(element)
    setEditingValue(element.text)
    setEditFormPosition({
      x: Math.min(element.rect.left + element.rect.width / 2, window.innerWidth - 200),
      y: Math.max(element.rect.top - 10, 10)
    })

    // Find the source location of this text using enhanced TSX text locator
    try {
      const response = await fetch(`/api/content/tsx-text-location?text=${encodeURIComponent(element.text)}&pagePath=${encodeURIComponent(element.filePath)}`, {
        credentials: 'include'
      })

      if (response.ok) {
        const result = await response.json()

        if (result.success && result.data.bestMatch) {
          const bestMatch = result.data.bestMatch
          const locationInfo = result.data.totalOccurrences > 1
            ? `${bestMatch.locationDescription} (${result.data.totalOccurrences} occurrences found)`
            : bestMatch.locationDescription

          // Update the element with enhanced location information
          setSelectedElement({
            ...element,
            sourceLocation: locationInfo,
            filePath: bestMatch.filePath,
            lineNumber: bestMatch.lineNumber,
            columnStart: bestMatch.columnStart,
            columnEnd: bestMatch.columnEnd,
            confidence: bestMatch.confidence,
            context: bestMatch.context
          })

          // Show confidence-based notifications
          if (bestMatch.confidence >= 70) {
            setNotification({
              type: 'success',
              message: `Text location detected with ${bestMatch.confidence}% confidence at line ${bestMatch.lineNumber}`
            })
          } else if (bestMatch.confidence >= 50) {
            setNotification({
              type: 'info',
              message: `Text location detected with ${bestMatch.confidence}% confidence. Context: ${bestMatch.context}`
            })
          } else {
            setNotification({
              type: 'warning',
              message: `Text location detected with low confidence (${bestMatch.confidence}%). Please verify the location.`
            })
          }

          // If multiple occurrences, show additional info
          if (result.data.totalOccurrences > 1) {
            setNotification({
              type: 'info',
              message: `Found ${result.data.totalOccurrences} occurrences. Using highest confidence match (${bestMatch.confidence}%).`
            })
          }
        } else {
          setNotification({
            type: 'warning',
            message: `Could not detect exact source location for "${element.text}". Manual file selection may be needed.`
          })
        }
      } else {
        setNotification({
          type: 'warning',
          message: 'Could not find text location in source files'
        })
      }
    } catch (error) {
      setNotification({
        type: 'error',
        message: `Error detecting source location: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
  }

  const saveElementEdit = async () => {
    if (!selectedElement || !editingValue.trim()) return

    setIsSaving(true)

    try {
      const requestBody = {
        oldText: selectedElement.text,
        newText: editingValue,
        pagePath: selectedElement.filePath,
        targetLine: Number(selectedElement.lineNumber),
        targetFile: selectedElement.sourceLocation?.split(' (')[0]
      }

      console.log('Sending source update request:', requestBody)

      const response = await fetch('/api/content/source-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestBody)
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Content updated successfully:', result)

        // Send message to iframe to update the specific element
        if (selectedElement) {
          const iframe = document.querySelector('iframe');
          if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage({
              type: 'UPDATE_ELEMENT_TEXT',
              elementId: selectedElement.id,
              newText: editingValue
            }, '*');
          }
        }

        setSelectedElement(null)
        setEditingValue('')
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('API Error:', errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Error saving element edit:', error)
      alert(`Failed to save changes: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSaving(false)
    }
  }

  const handleTsxFileSelect = async (filePath: string) => {
    console.log('Loading TSX file:', filePath)

    // Validate file path
    if (!filePath || filePath === '/' || !filePath.includes('.tsx')) {
      console.error('Invalid file path for TSX file:', filePath)
      alert('Invalid file path. Please select a valid TSX file.')
      return
    }

    try {
      const response = await fetch(`/api/content/tsx-file-content?filePath=${encodeURIComponent(filePath)}`, {
        credentials: 'include'
      })

      console.log('TSX file response status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('TSX file data:', data)
        if (data.success) {
          setSelectedTsxFile({
            filePath,
            content: data.content,
            lastModified: data.lastModified
          })
          console.log('TSX file loaded successfully:', filePath)
          
          // Clear any previous highlighted text when switching files
          setHighlightedText(null)
          setTsxEditingValue('')
        } else {
          console.error('TSX file load failed:', data.error)
          alert(`Failed to load file: ${data.error || 'Unknown error'}`)
        }
      } else {
        console.error('TSX file request failed:', response.status, response.statusText)
        alert(`Failed to load file: HTTP ${response.status}`)
      }
    } catch (error) {
      console.error('Error loading TSX file content:', error)
      alert(`Error loading file: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const handleTextHighlight = (selectedText: string, startLine: number, endLine: number, startColumn: number, endColumn: number) => {
    if (!selectedTsxFile) return

    const highlighted: HighlightedText = {
      id: Date.now().toString(),
      text: selectedText,
      startLine,
      endLine,
      startColumn,
      endColumn,
      filePath: selectedTsxFile.filePath
    }

    setHighlightedText(highlighted)
    setTsxEditingValue(selectedText)
  }

  const handleInlineEdit = (lineNumber: number, newText: string) => {
    if (!selectedTsxFile) return

    const lines = selectedTsxFile.content.split('\n')
    lines[lineNumber - 1] = newText
    const newContent = lines.join('\n')

    setSelectedTsxFile({
      ...selectedTsxFile,
      content: newContent
    })

    // Update the tsxFiles array as well
    setTsxFiles(prev => prev.map(file => 
      file.filePath === selectedTsxFile.filePath 
        ? { ...file, content: newContent }
        : file
    ))
  }

  // Enhanced text detection that finds ALL matches and highlights them
  const findAllTextMatches = async (targetText: string) => {
    try {
      const response = await fetch(`/api/content/tsx-text-location?text=${encodeURIComponent(targetText)}&pagePath=/`, {
        credentials: 'include'
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data.locations) {
          const matches = result.data.locations.map((location: any) => ({
            text: targetText,
            filePath: location.filePath,
            lineNumber: location.lineNumber,
            startColumn: location.columnStart,
            endColumn: location.columnEnd,
            confidence: location.confidence,
            context: location.context,
            fullLine: location.fullLine
          }))

          setHighlightedMatches(matches)

          if (matches.length > 0) {
            setNotification({
              type: 'success',
              message: `Found ${matches.length} matches for "${targetText}". All matches highlighted in TSX files.`
            })
            return matches[0] // Return best match for editing
          }
        }
      }
    } catch (error) {
      setNotification({
        type: 'error',
        message: `Error finding text matches: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }

    // Fallback: search in loaded TSX files
    const fallbackMatches: any[] = []
    for (const file of tsxFiles) {
      if (!file.content || typeof file.content !== 'string') continue

      const lines = file.content.split('\n')
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i]
        if (line.includes(targetText)) {
          fallbackMatches.push({
            text: targetText,
            filePath: file.filePath,
            lineNumber: i + 1,
            startColumn: line.indexOf(targetText) + 1,
            endColumn: line.indexOf(targetText) + targetText.length + 1,
            confidence: 50,
            context: 'fallback-search',
            fullLine: line
          })
        }
      }
    }

    if (fallbackMatches.length > 0) {
      setHighlightedMatches(fallbackMatches)
      setNotification({
        type: 'info',
        message: `Found ${fallbackMatches.length} matches using fallback search. All matches highlighted.`
      })
      return fallbackMatches[0]
    }

    setNotification({
      type: 'warning',
      message: `No matches found for "${targetText}". Please try selecting different text.`
    })
    return null
  }

  // Extract relevant text from a line that partially matches the target
  const extractRelevantText = (line: string, targetText: string): string | null => {
    // Remove JSX syntax and get clean text
    const cleanLine = line
      .replace(/<[^>]*>/g, '') // Remove JSX tags
      .replace(/[{}]/g, '') // Remove curly braces
      .replace(/['"`]/g, '') // Remove quotes
      .trim()

    if (!cleanLine) return null

    // If the line is short enough, return it as is
    if (cleanLine.length <= 100) {
      return cleanLine
    }

    // Try to find the most relevant part of the line
    const targetWords = targetText.toLowerCase().split(/\s+/)
    const lineWords = cleanLine.split(/\s+/)

    let bestMatch = ''
    let bestScore = 0

    // Try different substrings of the line
    for (let start = 0; start < lineWords.length; start++) {
      for (let end = start + 1; end <= Math.min(start + 10, lineWords.length); end++) {
        const substring = lineWords.slice(start, end).join(' ')
        const substringWords = substring.toLowerCase().split(/\s+/)

        let score = 0
        for (const word of targetWords) {
          if (substringWords.some(sw => sw.includes(word) || word.includes(sw))) {
            score++
          }
        }

        if (score > bestScore && substring.length <= 150) {
          bestScore = score
          bestMatch = substring
        }
      }
    }

    return bestMatch || cleanLine.substring(0, 100)
  }

  const openTsxEditor = async () => {
    if (!selectedElement) return

    // Load TSX files if not already loaded
    if (tsxFiles.length === 0) {
      await loadTsxFiles()
    }

    // Find the source file for the selected element
    let sourceFile = selectedElement.sourceLocation?.split(' (')[0] || selectedElement.filePath
    let foundText = selectedElement.text
    let lineNumber = selectedElement.lineNumber || 1

    // If no valid source file found, try to find a matching TSX file from the loaded files
    if (!sourceFile || sourceFile === '/' || !sourceFile.includes('.tsx')) {
      const searchResult = await findTextInTsxFiles(selectedElement.text)

      if (searchResult.file) {
        sourceFile = searchResult.file.filePath
        foundText = searchResult.foundText
        lineNumber = searchResult.lineNumber

        // Show confidence information to user
        if (searchResult.confidence && searchResult.confidence < 70) {
          setNotification({
            type: 'info',
            message: `Text found with ${searchResult.confidence}% confidence in ${sourceFile} at line ${lineNumber}. Context: ${searchResult.context || 'unknown'}`
          })
        }
      }
    }

    console.log('Opening TSX Editor with sourceFile:', sourceFile)
    console.log('selectedElement.sourceLocation:', selectedElement.sourceLocation)
    console.log('selectedElement.filePath:', selectedElement.filePath)
    console.log('Full selectedElement object:', selectedElement)

    if (sourceFile && sourceFile !== '/' && sourceFile.includes('.tsx')) {
      try {
        // Load the file content and wait for it to complete
        await handleTsxFileSelect(sourceFile)

        // Create highlighted text from selected element (using found text and line number)
        const highlighted: HighlightedText = {
          id: selectedElement.id,
          text: foundText,
          startLine: lineNumber,
          endLine: lineNumber,
          startColumn: selectedElement.columnStart || 1,
          endColumn: (selectedElement.columnStart || 1) + foundText.length,
          filePath: sourceFile
        }

        setHighlightedText(highlighted)
        setTsxEditingValue(foundText)

        // Only open the TSX Editor after file content is loaded
        setShowTsxEditor(true)
      } catch (error) {
        console.error('Error loading TSX file:', error)
        // Show the modal anyway with available files
        setShowTsxEditor(true)
      }
    } else {
      // Show the modal with available TSX files for manual selection
      setShowTsxEditor(true)

      // Show a more helpful message based on what we found
      const searchResult = await findTextInTsxFiles(selectedElement.text)
      if (searchResult.matchType === 'partial' && searchResult.file) {
        setNotification({
          type: 'info',
          message: `Similar content detected in ${searchResult.file.filePath} at line ${searchResult.lineNumber} (${searchResult.confidence}% confidence). Opening TSX Editor with the closest match.`
        })
      } else if (tsxFiles.length > 0) {
        setNotification({
          type: 'warning',
          message: `No source file detected for "${selectedElement.text}". Opening TSX Editor with available files for manual selection.`
        })
      } else {
        setNotification({
          type: 'error',
          message: 'Unable to detect the source file for this text. Please try selecting different text or check if the text is from a TSX file.'
        })
      }
    }
  }

  // Function to handle text editing from right-click selection
  const handleEditSelectedText = async () => {
    if (!selectedText.trim()) {
      setNotification({
        type: 'warning',
        message: 'Please select some text first.'
      })
      return
    }

    setIsEditFormOpen(false)
    setContextMenuPosition(null)

    // Find all matches for the selected text
    const bestMatch = await findAllTextMatches(selectedText)

    if (bestMatch) {
      // Load the TSX file content for the best match
      try {
        const response = await fetch(`/api/content/tsx-file-content?filePath=${encodeURIComponent(bestMatch.filePath)}`, {
          credentials: 'include'
        })

        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            setTsxFileContent(data.content)
            setSelectedTsxFile({
              filePath: bestMatch.filePath,
              content: data.content
            })
            setTsxEditingValue(selectedText) // Set the selected text as initial editing value
            setShowTsxEditor(true)

            setNotification({
              type: 'success',
              message: `Opened ${bestMatch.filePath} for editing. All ${highlightedMatches.length} matches are highlighted.`
            })
          } else {
            setNotification({
              type: 'error',
              message: `Failed to load TSX file: ${data.error}`
            })
          }
        }
      } catch (error) {
        setNotification({
          type: 'error',
          message: `Error loading TSX file: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  }

  const saveTsxEdit = async () => {
    if (!highlightedText || !tsxEditingValue.trim()) return

    setIsSaving(true)

    try {
      const response = await fetch('/api/content/tsx-edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          filePath: highlightedText.filePath,
          oldText: highlightedText.text,
          newText: tsxEditingValue,
          startLine: highlightedText.startLine,
          endLine: highlightedText.endLine,
          startColumn: highlightedText.startColumn,
          endColumn: highlightedText.endColumn
        })
      })

      if (response.ok) {
        const result = await response.json()
        setNotification({
          type: 'success',
          message: `TSX file updated successfully: ${highlightedText.filePath}`
        })

        // Reload the file content
        await handleTsxFileSelect(highlightedText.filePath)

        // Update the original element's text
        if (selectedElement) {
          setEditingValue(tsxEditingValue)
        }

        // Clear TSX editor selection
        setHighlightedText(null)
        setTsxEditingValue('')
        setShowTsxEditor(false)

      } else {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      setNotification({
        type: 'error',
        message: `Failed to save changes: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    } finally {
      setIsSaving(false)
    }
  }

  // injectInteractiveScript function removed - ContentAwareIframe handles script injection

  const handleIframeMessage = useCallback((event: MessageEvent) => {
    console.log('Received message from iframe:', event.data);
    if (event.data.type === 'ELEMENT_SELECTED') {
      console.log('Element selected:', event.data.element);
      handleElementSelection(event.data.element)
    }
  }, [])

  useEffect(() => {
    window.addEventListener('message', handleIframeMessage)
    return () => window.removeEventListener('message', handleIframeMessage)
  }, [handleIframeMessage])

  const undo = () => {
    if (undoStack.length === 0) return
    
    const lastChange = undoStack[0]
    setUndoStack(prev => prev.slice(1))
    setRedoStack(prev => [lastChange, ...prev])
    
    // TODO: Implement undo functionality with ContentAwareIframe
    console.log('Undo:', lastChange)
  }

  const redo = () => {
    if (redoStack.length === 0) return
    
    const nextChange = redoStack[0]
    setRedoStack(prev => prev.slice(1))
    setUndoStack(prev => [nextChange, ...prev])
    
    // TODO: Implement redo functionality with ContentAwareIframe
    console.log('Redo:', nextChange)
  }

  const publishChanges = async () => {
    if (changeHistory.length === 0) {
      alert('No changes to publish')
      return
    }

    setIsSaving(true)
    try {
      const response = await fetch('/api/content/publish', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          changes: changeHistory
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Changes published successfully:', result)
        
        setDraftMode(false)
        setChangeHistory([])
        setUndoStack([])
        setRedoStack([])
        
        alert(`Successfully published ${changeHistory.length} changes!`)
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('Publish API Error:', errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('Error publishing changes:', error)
      alert(`Failed to publish changes: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSaving(false)
    }
  }

  if (!isClient) return null

  const selectedPageData = pages.find(p => p.id === selectedPage)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Notification Component */}
      {notification && (
        <div className={`fixed top-4 right-4 z-50 max-w-md p-4 rounded-lg shadow-lg border-l-4 ${
          notification.type === 'success' ? 'bg-green-50 border-green-400 text-green-800' :
          notification.type === 'error' ? 'bg-red-50 border-red-400 text-red-800' :
          notification.type === 'warning' ? 'bg-yellow-50 border-yellow-400 text-yellow-800' :
          'bg-blue-50 border-blue-400 text-blue-800'
        }`}>
          <div className="flex items-start">
            <div className="flex-shrink-0">
              {notification.type === 'success' && (
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              )}
              {notification.type === 'error' && (
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
              {notification.type === 'warning' && (
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              )}
              {notification.type === 'info' && (
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium">{notification.message}</p>
            </div>
            <div className="ml-4 flex-shrink-0">
              <button
                onClick={() => setNotification(null)}
                className="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none"
              >
                <svg className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {isDev && (
        <div className="bg-blue-100 text-blue-900 border-b border-blue-300 px-6 py-3 text-center text-sm font-medium">
          ℹ️ Development mode: Using direct preview instead of iframe for better compatibility with React Fast Refresh.
        </div>
      )}
      <div className="h-screen flex flex-col bg-gray-50">
        {/* Simple Top Toolbar */}
        <div className="bg-white border-b border-gray-200 px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-semibold text-gray-900">Visual Editor</h1>
              <select
                value={selectedPage}
                onChange={e => {
                  const pageId = e.target.value
                  setSelectedPage(pageId)
                  const page = pages.find(p => p.id === pageId)
                  console.log('Page selected:', pageId, 'Page data:', page)
                  if (page) {
                    const origin = typeof window !== 'undefined' ? window.location.origin : ''
                    const newUrl = page.path === '/' ? `${origin}/` : `${origin}${page.path}`
                    console.log('Setting preview URL to:', newUrl)
                    setPreviewUrl(newUrl)
                  } else {
                    setPreviewUrl(typeof window !== 'undefined' ? window.location.origin + '/' : '/')
                  }
                }}
                className="ml-2 px-3 py-1 border border-gray-300 rounded text-sm bg-white"
                style={{ minWidth: 120 }}
              >
                <option value="">Select page…</option>
                {pages.map(page => (
                  <option key={page.id} value={page.id}>
                    {page.name || page.path}
                  </option>
                ))}
              </select>
            </div>
            <button
              onClick={interactiveMode ? disableInteractiveMode : enableInteractiveMode}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                interactiveMode
                  ? 'bg-red-100 text-red-700 hover:bg-red-200'
                  : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
              }`}
            >
              {interactiveMode ? 'Exit Edit Mode' : 'Edit Mode'}
            </button>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">

          {/* Live Preview */}
          <div className="flex-1 bg-gray-100 p-6">
            <div className="mx-auto bg-white shadow-lg rounded-lg overflow-hidden h-full">
              {isDev ? (
                <DirectPreview
                  pagePath={previewUrl}
                  onElementSelect={handleElementSelection}
                  onContentLoad={(contentMap) => {
                    console.log('Content loaded:', contentMap)
                  }}
                  interactiveMode={interactiveMode}
                />
              ) : (
                <ContentAwareIframe
                  pagePath={previewUrl}
                  onElementSelect={handleElementSelection}
                  onContentLoad={(contentMap) => {
                    console.log('Content loaded:', contentMap)
                  }}
                  interactiveMode={interactiveMode}
                />
              )}
            </div>
          </div>
        </div>

        {/* Edit Form Modal */}
        {selectedElement && (
          <div
            className="fixed bg-white border border-gray-300 rounded-lg shadow-lg p-4 z-50 min-w-80"
            style={{
              left: `${editFormPosition.x}px`,
              top: `${editFormPosition.y}px`,
              transform: 'translateX(-50%)'
            }}
          >
            <div className="mb-3">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Edit Text
              </label>
              <textarea
                value={editingValue}
                onChange={(e) => setEditingValue(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
                rows={3}
                placeholder="Enter new text..."
              />
            </div>

            {selectedElement.sourceLocation && (
              <div className="mb-3 text-xs text-gray-500">
                Source: {selectedElement.sourceLocation}
              </div>
            )}

            <div className="flex gap-2">
              <button
                onClick={saveElementEdit}
                disabled={isSaving || !editingValue.trim()}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
              >
                {isSaving ? 'Saving...' : 'Save'}
              </button>
              <button
                onClick={openTsxEditor}
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              >
                TSX Editor
              </button>
              <button
                onClick={() => setSelectedElement(null)}
                className="px-3 py-1 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        {/* TSX Editor Modal */}
        {showTsxEditor && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
              <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">TSX File Editor</h2>
                  {selectedTsxFile && highlightedText && (
                    <p className="text-sm text-green-600 mt-1">
                      ✓ Intelligent detection: Auto-selected file and highlighted text
                    </p>
                  )}
                </div>
                <button
                  onClick={() => setShowTsxEditor(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <XMarkIcon className="w-6 h-6" />
                </button>
              </div>

              <div className="flex-1 flex overflow-hidden">
                {/* Simplified TSX File Editor - Direct editing only */}
                <div className="flex-1 flex flex-col">
                  {selectedTsxFile ? (
                    <>
                      <div className="p-4 border-b border-gray-200">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium text-gray-900">{selectedTsxFile.filePath}</h3>
                          <div className="text-sm text-gray-600">
                            {highlightedMatches.length > 0 && (
                              <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                                {highlightedMatches.length} matches highlighted
                              </span>
                            )}
                          </div>
                        </div>
                        <p className="text-xs text-blue-600 mt-2">
                          💡 Edit the file directly below. All matching text is highlighted in yellow.
                        </p>
                      </div>
                      <div className="flex-1 overflow-hidden">
                        <TsxFilePreview
                          content={selectedTsxFile.content}
                          filePath={selectedTsxFile.filePath}
                          onTextSelect={handleTextHighlight}
                          highlightedText={highlightedText}
                          highlightedMatches={highlightedMatches}
                          onInlineEdit={handleInlineEdit}
                        />
                      </div>
                    </>
                  ) : (
                    <div className="flex-1 flex flex-col">
                      <div className="p-4 border-b border-gray-200">
                        <h3 className="font-medium text-gray-900 mb-2">Available TSX Files</h3>
                        <p className="text-sm text-gray-600 mb-4">
                          No specific file was detected for the selected text. Choose a file to edit manually:
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-60 overflow-y-auto">
                          {tsxFiles.map((file) => {
                            const containsSelectedText = selectedElement && file.content && typeof file.content === 'string' && file.content.includes(selectedElement.text)
                            return (
                              <button
                                key={file.filePath}
                                onClick={() => handleTsxFileSelect(file.filePath)}
                                className={`text-left p-3 border rounded-lg transition-colors ${
                                  containsSelectedText 
                                    ? 'border-green-300 bg-green-50 hover:bg-green-100' 
                                    : 'border-gray-200 hover:bg-gray-50'
                                }`}
                              >
                                <div className="font-medium text-sm text-gray-900 truncate">
                                  {file.filePath.split('/').pop()}
                                  {containsSelectedText && (
                                    <span className="ml-2 text-xs bg-green-200 text-green-800 px-2 py-1 rounded">
                                      Contains text
                                    </span>
                                  )}
                                </div>
                                <div className="text-xs text-gray-500 truncate">
                                  {file.filePath}
                                </div>
                                <div className="text-xs text-gray-400 mt-1">
                                  {file.content.length} characters
                                </div>
                              </button>
                            )
                          })}
                        </div>
                        {tsxFiles.length === 0 && (
                          <div className="text-center py-8">
                            <DocumentTextIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                            <p className="text-lg font-medium mb-2">No TSX files available</p>
                            <p className="text-sm text-gray-500">
                              No TSX files were found. Please check your file structure or try selecting different text.
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* TSX Edit Form */}
              {highlightedText && (
                <div className="p-4 border-t border-gray-200 bg-gray-50">
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Edit Selected Text
                    </label>
                    <div className="text-xs text-gray-500 mb-2">
                      Line {highlightedText.startLine}, Column {highlightedText.startColumn}
                    </div>
                    <RichTextEditor
                      value={tsxEditingValue}
                      onChange={setTsxEditingValue}
                      placeholder="Enter new text with rich formatting..."
                      rows={4}
                      className="w-full"
                    />
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={saveTsxEdit}
                      disabled={isSaving || !tsxEditingValue.trim()}
                      className="px-4 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
                    >
                      {isSaving ? 'Saving...' : 'Save Changes'}
                    </button>
                    <button
                      onClick={() => {
                        setHighlightedText(null)
                        setTsxEditingValue('')
                        setShowTsxEditor(false)
                      }}
                      className="px-4 py-2 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Context Menu for Text Selection */}
        {contextMenuPosition && (
          <div
            className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg py-2 min-w-[200px]"
            style={{
              left: contextMenuPosition.x,
              top: contextMenuPosition.y,
            }}
          >
            <div className="px-4 py-2 text-sm text-gray-700 border-b border-gray-100">
              Selected: "{selectedText.length > 30 ? selectedText.substring(0, 30) + '...' : selectedText}"
            </div>
            <button
              onClick={handleEditSelectedText}
              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Edit in TSX File
            </button>
          </div>
        )}

        {/* Simple Edit Form Modal */}
        {isEditFormOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-96 max-w-[90vw]">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Edit Selected Text</h3>
                <button
                  onClick={() => setIsEditFormOpen(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <XMarkIcon className="w-6 h-6" />
                </button>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Selected Text:
                </label>
                <div className="p-3 bg-gray-50 rounded border text-sm">
                  {selectedText}
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setIsEditFormOpen(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded hover:bg-gray-200"
                >
                  Cancel
                </button>
                <button
                  onClick={handleEditSelectedText}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded hover:bg-blue-700"
                >
                  Open TSX Editor
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default VisualEditor