import { Metadata } from 'next';
import { TeamMembersManager } from '@/components/admin/team-members/team-members-manager';
import { teamMemberConfig } from '@/components/admin/team-members/team-member-config';

export const metadata: Metadata = {
  title: 'Team Members Management - Technoloway Admin',
  description: 'Manage your team members, roles, and information in the Technoloway admin dashboard.',
  keywords: [
    'team members management',
    'admin dashboard',
    'employee management',
    'team roles',
    'technoloway admin',
    'content management',
    'staff management',
    'employee directory'
  ],
  robots: {
    index: false,
    follow: false,
    googleBot: {
      index: false,
      follow: false,
    },
  },
  alternates: {
    canonical: '/admin/team-members',
  },
};

export default function TeamMembersPage() {
  return <TeamMembersManager config={teamMemberConfig} />;
}

