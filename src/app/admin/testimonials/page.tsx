'use client';

import { TestimonialsManager } from '@/components/admin/testimonials/testimonials-manager';
import { CrudConfig } from '@/components/admin/crud/types';

interface Testimonial {
  id: number
  clientName: string
  clientTitle: string
  clientCompany: string
  clientPhotoUrl?: string
  content: string
  rating: number
  isFeatured: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  _count?: {
    likes?: number;
    shares?: number;
    [key: string]: number | undefined;
  }
}

const testimonialConfig: CrudConfig<Testimonial> = {
  title: 'Testimonials',
  description: 'Manage customer testimonials and reviews',
  endpoint: 'testimonials', // API endpoint

  // Table columns configuration
  columns: [
    {
      key: 'clientName',
      label: 'Client',
      sortable: true,
      searchable: true,
      width: '25%'
    },
    {
      key: 'clientCompany',
      label: 'Company',
      sortable: true,
      searchable: true,
      width: '20%'
    },
    {
      key: 'content',
      label: 'Testimonial',
      sortable: false,
      searchable: true,
      width: '30%'
    },
    {
      key: 'rating',
      label: 'Rating',
      sortable: true,
      searchable: false,
      width: '10%'
    },
    {
      key: 'isFeatured',
      label: 'Featured',
      sortable: true,
      searchable: false,
      width: '10%'
    },
    {
      key: 'updatedAt',
      label: 'Last Updated',
      sortable: true,
      searchable: false,
      width: '15%'
    }
  ],

  // Filters configuration
  filters: [
    {
      key: 'isFeatured',
      label: 'Featured Status',
      type: 'select',
      options: [
        { value: '', label: 'All Testimonials' },
        { value: 'true', label: 'Featured' },
        { value: 'false', label: 'Regular' }
      ]
    },
    {
      key: 'rating',
      label: 'Rating',
      type: 'select',
      options: [
        { value: '', label: 'All Ratings' },
        { value: '5', label: '5 Stars' },
        { value: '4', label: '4 Stars' },
        { value: '3', label: '3 Stars' },
        { value: '2', label: '2 Stars' },
        { value: '1', label: '1 Star' }
      ]
    }
  ],

  // Bulk actions configuration
  bulkActions: [
    {
      action: 'feature',
      label: 'Feature',
      icon: 'StarIcon',
      variant: 'warning',
      confirmationMessage: 'Are you sure you want to feature the selected testimonials?'
    },
    {
      action: 'unfeature',
      label: 'Unfeature',
      icon: 'StarIcon',
      variant: 'secondary',
      confirmationMessage: 'Are you sure you want to unfeature the selected testimonials?'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      confirmationMessage: 'Are you sure you want to delete the selected testimonials? This action cannot be undone.'
    }
  ],

  // Action buttons for each row
  actions: [
    {
      action: 'view',
      label: 'View',
      icon: 'EyeIcon',
      variant: 'secondary',
      tooltip: 'View testimonial details'
    },
    {
      action: 'edit',
      label: 'Edit',
      icon: 'PencilIcon',
      variant: 'primary',
      tooltip: 'Edit testimonial'
    },
    {
      action: 'toggle-featured',
      label: 'Toggle Featured',
      icon: 'StarIcon',
      variant: 'warning',
      tooltip: 'Feature/Unfeature testimonial'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      tooltip: 'Delete testimonial'
    }
  ],

  fields: [
    {
      key: 'clientName',
      label: 'Client Name',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'e.g., John Smith'
    },
    {
      key: 'clientTitle',
      label: 'Client Title',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'e.g., CEO, Marketing Director'
    },
    {
      key: 'clientCompany',
      label: 'Company',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'e.g., Acme Corporation'
    },
    {
      key: 'clientPhotoUrl',
      label: 'Client Photo',
      type: 'url',
      searchable: false,
      placeholder: 'Enter photo URL or click Upload to select file'
    },
    {
      key: 'content',
      label: 'Testimonial Content',
      type: 'textarea',
      required: true,
      searchable: true,
      placeholder: 'Enter the client\'s testimonial content...',
      rows: 4
    },
    {
      key: 'rating',
      label: 'Rating',
      type: 'number',
      required: true,
      defaultValue: 5,
      searchable: false,
      min: 1,
      max: 5
    },
    {
      key: 'isFeatured',
      label: 'Featured Testimonial',
      type: 'boolean',
      defaultValue: false,
      searchable: false,
    },
    {
      key: 'displayOrder',
      label: 'Display Order',
      type: 'number',
      defaultValue: 0,
      searchable: false,
      placeholder: '0'
    },
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search testimonials by client name, company, content...',
  defaultSort: { field: 'updatedAt', direction: 'desc' }, // Sort by Last Active (most recent)
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true,
  enableViewControls: true,
  enableDensityControls: true,
  enableColumnVisibility: true,
  defaultViewSettings: {
    mode: 'list',
    density: 'comfortable',
    visibleColumns: ['clientName', 'clientCompany', 'content', 'rating', 'isFeatured', 'updatedAt']
  },

  // Form layout configuration
  formLayout: {
    type: 'compact',
    columns: 2,
    sections: [
      {
        title: 'Client Information',
        fields: ['clientName', 'clientTitle', 'clientCompany', 'clientPhotoUrl']
      },
      {
        title: 'Testimonial Content',
        fields: ['content', 'rating']
      },
      {
        title: 'Display Settings',
        fields: ['isFeatured', 'displayOrder']
      }
    ]
  }
};

export default function TestimonialsPage() {
  return <TestimonialsManager config={testimonialConfig} />;
}
