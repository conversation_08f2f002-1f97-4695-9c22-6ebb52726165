import { notFound } from 'next/navigation'
import Link from 'next/link'
import { 
  DocumentTextIcon, 
  ChartBarIcon, 
  CurrencyDollarIcon, 
  CreditCardIcon,
  ArrowRightIcon,
  CalendarIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import { getClientDetails } from '@/lib/fetchers/client'

interface ClientDetailPageProps {
  params: Promise<{ clientId: string }>
}

export default async function ClientDetailPage({ params }: ClientDetailPageProps) {
  const { clientId } = await params

  // Fetch detailed client data
  let client
  try {
    client = await getClientDetails(clientId)
  } catch (error) {
    console.error('Error fetching client details:', error)
    notFound()
  }

  if (!client) {
    notFound()
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'paid':
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'in_progress':
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'draft':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-blue-100 text-blue-800'
    }
  }

  return (
    <div className="space-y-8">
      {/* Quick Overview Cards */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Recent Projects */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <DocumentTextIcon className="h-5 w-5 mr-2 text-blue-600" />
                Recent Projects
              </h3>
              <Link
                href={`/clients/${clientId}/projects`}
                className="text-sm text-blue-600 hover:text-blue-500 flex items-center"
              >
                View all
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </div>
          <div className="px-6 py-4">
            {client.projects.length > 0 ? (
              <div className="space-y-3">
                {client.projects.slice(0, 3).map((project) => (
                  <div key={project.id} className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {project.name}
                      </p>
                      {project.status && (
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getStatusColor(project.status)}`}>
                          {project.status.replace('_', ' ')}
                        </span>
                      )}
                    </div>
                    {project.estimateCost && (
                      <div className="text-sm text-gray-500">
                        {formatCurrency(project.estimateCost)}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No projects yet</p>
            )}
          </div>
        </div>

        {/* Recent Contracts */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <ChartBarIcon className="h-5 w-5 mr-2 text-green-600" />
                Recent Contracts
              </h3>
              <Link
                href={`/clients/${clientId}/contracts`}
                className="text-sm text-blue-600 hover:text-blue-500 flex items-center"
              >
                View all
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </div>
          <div className="px-6 py-4">
            {client.contracts.length > 0 ? (
              <div className="space-y-3">
                {client.contracts.slice(0, 3).map((contract) => (
                  <div key={contract.id} className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {contract.contName}
                      </p>
                      {contract.contStatus && (
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getStatusColor(contract.contStatus)}`}>
                          {contract.contStatus}
                        </span>
                      )}
                    </div>
                    {contract.contValue && (
                      <div className="text-sm text-gray-500">
                        {formatCurrency(contract.contValue)}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No contracts yet</p>
            )}
          </div>
        </div>

        {/* Recent Invoices */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <CurrencyDollarIcon className="h-5 w-5 mr-2 text-yellow-600" />
                Recent Invoices
              </h3>
              <Link
                href={`/clients/${clientId}/invoices`}
                className="text-sm text-blue-600 hover:text-blue-500 flex items-center"
              >
                View all
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </div>
          <div className="px-6 py-4">
            {client.invoices.length > 0 ? (
              <div className="space-y-3">
                {client.invoices.slice(0, 3).map((invoice) => (
                  <div key={invoice.id} className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {formatCurrency(invoice.totalAmount)}
                      </p>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getStatusColor(invoice.status)}`}>
                          {invoice.status}
                        </span>
                        <span className="text-xs text-gray-500 flex items-center">
                          <CalendarIcon className="h-3 w-3 mr-1" />
                          Due {formatDate(invoice.dueDate)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No invoices yet</p>
            )}
          </div>
        </div>

        {/* Activity Summary */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <ClockIcon className="h-5 w-5 mr-2 text-purple-600" />
              Activity Summary
            </h3>
          </div>
          <div className="px-6 py-4">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Projects</span>
                <span className="text-sm font-medium text-gray-900">{client._count.projects}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Active Contracts</span>
                <span className="text-sm font-medium text-gray-900">{client._count.contracts}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Invoices</span>
                <span className="text-sm font-medium text-gray-900">{client._count.invoices}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Payments Received</span>
                <span className="text-sm font-medium text-gray-900">{client._count.payments}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <Link
              href={`/clients/${clientId}/projects`}
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 border border-gray-200 rounded-lg hover:border-gray-300"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
                  <DocumentTextIcon className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" aria-hidden="true" />
                  View Projects
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  Manage and track all client projects
                </p>
              </div>
            </Link>

            <Link
              href={`/clients/${clientId}/contracts`}
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-green-500 border border-gray-200 rounded-lg hover:border-gray-300"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                  <ChartBarIcon className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" aria-hidden="true" />
                  View Contracts
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  Review contract terms and status
                </p>
              </div>
            </Link>

            <Link
              href={`/clients/${clientId}/invoices`}
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-yellow-500 border border-gray-200 rounded-lg hover:border-gray-300"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-yellow-50 text-yellow-700 ring-4 ring-white">
                  <CurrencyDollarIcon className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" aria-hidden="true" />
                  View Invoices
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  Track billing and payment status
                </p>
              </div>
            </Link>

            <Link
              href={`/clients/${clientId}/payments`}
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-purple-500 border border-gray-200 rounded-lg hover:border-gray-300"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white">
                  <CreditCardIcon className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" aria-hidden="true" />
                  View Payments
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  Monitor payment history and methods
                </p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
