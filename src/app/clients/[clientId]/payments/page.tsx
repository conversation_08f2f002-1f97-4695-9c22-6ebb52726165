'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { PaymentList } from '@/components/clients/PaymentList'
import { PaymentListItem } from '@/lib/fetchers/payment'
import { CurrencyDollarIcon, CreditCardIcon, CheckCircleIcon, ClockIcon } from '@heroicons/react/24/outline'

interface PaymentsPageState {
  payments: PaymentListItem[]
  loading: boolean
  error: string | null
  stats: {
    totalAmount: number
    totalPayments: number
    statusBreakdown: Record<string, number>
    methodBreakdown: Record<string, number>
  } | null
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export default function ClientPaymentsPage() {
  const params = useParams()
  const clientId = params.clientId as string

  const [state, setState] = useState<PaymentsPageState>({
    payments: [],
    loading: true,
    error: null,
    stats: null,
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
    },
  })

  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState<any>({})

  const fetchPayments = async (page = 1, search = '', filterParams = {}) => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: state.pagination.limit.toString(),
        stats: 'true', // Include stats in the response
      })

      if (search) {
        params.append('search', search)
      }

      if (Object.keys(filterParams).length > 0) {
        params.append('filter', JSON.stringify(filterParams))
      }

      const response = await fetch(`/api/clients/${clientId}/payments?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch payments')
      }

      const data = await response.json()

      if (data.success) {
        setState(prev => ({
          ...prev,
          payments: data.data.payments,
          stats: data.data.stats,
          loading: false,
          pagination: data.data.pagination,
        }))
      } else {
        throw new Error(data.error || 'Failed to fetch payments')
      }
    } catch (error) {
      console.error('Error fetching payments:', error)
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'An error occurred',
      }))
    }
  }

  useEffect(() => {
    fetchPayments()
  }, [clientId])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    fetchPayments(1, query, filters)
  }

  const handleFilter = (newFilters: any) => {
    setFilters(newFilters)
    fetchPayments(1, searchQuery, newFilters)
  }

  const handlePageChange = (page: number) => {
    fetchPayments(page, searchQuery, filters)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  if (state.error) {
    return (
      <div className="bg-white shadow rounded-lg p-12 text-center">
        <div className="text-red-600 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Payments</h3>
        <p className="text-sm text-gray-500 mb-4">{state.error}</p>
        <button
          onClick={() => fetchPayments()}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header with Stats */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Payments</h1>
            <p className="mt-1 text-sm text-gray-500">
              {state.pagination.total} payment{state.pagination.total !== 1 ? 's' : ''} total
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        {state.stats && (
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <div className="bg-gray-50 overflow-hidden rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-3 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Total Received
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {formatCurrency(state.stats.totalAmount)}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 overflow-hidden rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CreditCardIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-3 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Total Payments
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {state.stats.totalPayments}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 overflow-hidden rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircleIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-3 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Completed
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {state.stats.statusBreakdown.Completed || 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 overflow-hidden rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-3 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Pending
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {state.stats.statusBreakdown.Pending || 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Payment Methods Breakdown */}
        {state.stats && Object.keys(state.stats.methodBreakdown).length > 0 && (
          <div className="mt-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Payment Methods</h3>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
              {Object.entries(state.stats.methodBreakdown).map(([method, count]) => (
                <div key={method} className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{count}</div>
                  <div className="text-sm text-gray-500">{method}</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Payments List */}
      <PaymentList
        payments={state.payments}
        loading={state.loading}
        onSearch={handleSearch}
        onFilter={handleFilter}
        searchQuery={searchQuery}
        filters={filters}
      />

      {/* Pagination */}
      {state.pagination.totalPages > 1 && (
        <div className="bg-white shadow rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing{' '}
              <span className="font-medium">
                {(state.pagination.page - 1) * state.pagination.limit + 1}
              </span>{' '}
              to{' '}
              <span className="font-medium">
                {Math.min(state.pagination.page * state.pagination.limit, state.pagination.total)}
              </span>{' '}
              of{' '}
              <span className="font-medium">{state.pagination.total}</span>{' '}
              results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(state.pagination.page - 1)}
                disabled={state.pagination.page <= 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              
              {/* Page Numbers */}
              {Array.from({ length: Math.min(5, state.pagination.totalPages) }, (_, i) => {
                const pageNum = Math.max(1, state.pagination.page - 2) + i
                if (pageNum > state.pagination.totalPages) return null
                
                return (
                  <button
                    key={pageNum}
                    onClick={() => handlePageChange(pageNum)}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md ${
                      pageNum === state.pagination.page
                        ? 'bg-blue-600 border-blue-600 text-white'
                        : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                    }`}
                  >
                    {pageNum}
                  </button>
                )
              })}
              
              <button
                onClick={() => handlePageChange(state.pagination.page + 1)}
                disabled={state.pagination.page >= state.pagination.totalPages}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
