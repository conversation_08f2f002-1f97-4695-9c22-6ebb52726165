# Client Detail Pages

This directory contains the complete implementation of the Client Detail Pages with Next.js App Router, TypeScript, Prisma, and TailwindCSS.

## Structure

```
/app/clients/[clientId]/
├── layout.tsx                  # Layout with client header and tabs
├── page.tsx                    # Client overview/summary page
├── projects/
│   └── page.tsx               # Client projects list
├── contracts/
│   └── page.tsx               # Client contracts list
├── invoices/
│   └── page.tsx               # Client invoices list
└── payments/
    └── page.tsx               # Client payments list

/api/clients/[clientId]/
├── route.ts                   # Get client details
├── projects/
│   └── route.ts              # Get client projects
├── contracts/
│   └── route.ts              # Get client contracts
├── invoices/
│   └── route.ts              # Get client invoices
└── payments/
    └── route.ts              # Get client payments

/components/clients/
├── ClientHeader.tsx           # Client summary header
├── ClientTabs.tsx            # Navigation tabs
├── ProjectList.tsx           # Projects list component
├── ContractList.tsx          # Contracts list component
├── InvoiceList.tsx           # Invoices list component
└── PaymentList.tsx           # Payments list component

/lib/fetchers/
├── client.ts                 # Client data fetchers
├── project.ts                # Project data fetchers
├── contract.ts               # Contract data fetchers
├── invoice.ts                # Invoice data fetchers
├── payment.ts                # Payment data fetchers
└── index.ts                  # Exports
```

## Features

### Client Overview Page
- Client summary with company information
- Contact details and address
- Activity statistics (projects, contracts, invoices, payments)
- Recent items from each category
- Quick action cards for navigation

### Client Header Component
- Company logo and basic information
- Contact details with clickable email/phone
- Status indicator (Active/Inactive)
- Statistics cards showing counts
- Notes section if available

### Navigation Tabs
- Overview, Projects, Contracts, Invoices, Payments
- Count badges for each section
- Active state highlighting

### Resource List Components
Each list component includes:
- Search functionality
- Filtering options
- Responsive card-based layout
- Status indicators with appropriate colors
- Pagination support
- Loading states
- Empty states
- Error handling

### API Endpoints
All endpoints follow RESTful conventions:
- `GET /api/clients/[clientId]` - Get client details
- `GET /api/clients/[clientId]/projects` - Get client projects
- `GET /api/clients/[clientId]/contracts` - Get client contracts
- `GET /api/clients/[clientId]/invoices` - Get client invoices (with stats)
- `GET /api/clients/[clientId]/payments` - Get client payments (with stats)

### Data Relationships
The implementation properly handles the following relationships:
- Client → Projects (one-to-many)
- Client → Contracts (one-to-many)
- Client → Invoices (one-to-many)
- Invoice → Payments (one-to-many)
- Contract → Invoices (one-to-many)
- Project → Contracts (one-to-many)

## Usage

### Accessing Client Details
Navigate to `/clients/[clientId]` where `clientId` is the numeric ID of the client.

### Example URLs
- `/clients/1` - Client overview
- `/clients/1/projects` - Client projects
- `/clients/1/contracts` - Client contracts
- `/clients/1/invoices` - Client invoices
- `/clients/1/payments` - Client payments

### API Usage
```typescript
// Get client details
const client = await getClientDetails('1')

// Get client projects with pagination
const { projects, total } = await getClientProjects('1', {
  page: 1,
  limit: 10,
  search: 'website',
  status: 'IN_PROGRESS'
})

// Get client invoices with stats
const response = await fetch('/api/clients/1/invoices?stats=true')
const { invoices, stats } = await response.json()
```

## Styling

The implementation uses TailwindCSS with a consistent design system:
- Light background (`bg-gray-50`)
- White cards with subtle shadows
- Proper spacing and padding
- Responsive grid layouts
- Status-based color coding
- Hover effects and transitions

## Error Handling

- Client not found → 404 page
- API errors → Error states with retry buttons
- Loading states → Skeleton placeholders
- Empty states → Helpful messages with icons

## Performance Considerations

- Server-side rendering for initial page load
- Client-side navigation between tabs
- Pagination for large datasets
- Optimized database queries with proper includes
- Lazy loading of related data

## Testing

To test the implementation:

1. Ensure you have clients in your database
2. Navigate to `/clients/[clientId]` with a valid client ID
3. Test navigation between tabs
4. Test search and filtering functionality
5. Verify data relationships are working correctly

## Dependencies

- Next.js 15 (App Router)
- TypeScript
- Prisma (database ORM)
- TailwindCSS (styling)
- Heroicons (icons)
- React hooks for state management
