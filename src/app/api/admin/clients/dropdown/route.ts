import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin
} from '@/lib/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/clients/dropdown - Get clients for dropdown selection
export const GET = with<PERSON><PERSON>r<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const clients = await prisma.clientss.findMany({
    select: {
      id: true,
      companyname: true,
      contactname: true,
      contactemail: true,
    },
    orderBy: {
      companyname: 'asc',
    },
  })

  // Format for dropdown
  const formattedClients = clients.map(client => ({
    value: client.id,
    label: `${client.companyname} - ${client.contactname}`,
    companyname: client.companyname,
    contactname: client.contactname,
    contactemail: client.contactemail,
  }))

  return successResponse(formattedClients)
})
