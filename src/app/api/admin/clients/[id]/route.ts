import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/clients/[id] - Get a specific client
export const GET = withError<PERSON>andler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params

  const client = await prisma.clients.findUnique({
    where: { id: Number(id) },
    include: {
      projects: {
        select: {
          id: true,
          name: true,
          status: true,
          projstartdate: true,
          projcompletiondate: true,
          estimatecost: true,
        },
        orderBy: {
          createdat: 'desc',
        },
      },
      orders: {
        select: {
          id: true,
          ordertitle: true,
          ordertotalamount: true,
          status: true,
          orderdate: true,
        },
        orderBy: {
          createdat: 'desc',
        },
      },
      testimonials: {
        select: {
          id: true,
          content: true,
          rating: true,
          isfeatured: true,
          createdat: true,
        },
        orderBy: {
          createdat: 'desc',
        },
      },
      users: {
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true
        }
      },
      _count: {
        select: {
          projects: true,
          orders: true,
          testimonials: true,
        },
      },
    },
  })

  if (!client) {
    throw new ApiError('Client not found', 404)
  }

  const transformedClient = transformFromDbFields.client(client)
  return successResponse(transformedClient)
})

// PUT /api/admin/clients/[id] - Update a client
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.client.update)
  const data = await validate(request)

  // Check if client exists
  const existingClient = await prisma.clients.findUnique({
    where: { id: Number(id) },
  })

  if (!existingClient) {
    throw new ApiError('Client not found', 404)
  }

  // Check if email is being changed and if it conflicts with another client
  if (data.contactEmail && data.contactEmail !== existingClient.contactemail) {
    const emailConflict = await prisma.clients.findFirst({
      where: {
        contactemail: data.contactEmail,
        id: { not: Number(id) }
      },
    })

    if (emailConflict) {
      throw new ApiError('A client with this email already exists', 400)
    }
  }

  // If userId is provided, check if the user exists
  if (data.userId && Number(data.userId) !== existingClient.userid) {
    const user = await prisma.users.findUnique({
      where: { id: Number(data.userId) },
    })

    if (!user) {
      throw new ApiError('User not found', 400)
    }
  }

  const client = await prisma.clients.update({
    where: { id: Number(id) },
    data: transformToDbFields.client(data),
    include: {
      users: {
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true
        }
      },
      _count: {
        select: {
          projects: true,
          orders: true,
          testimonials: true,
        },
      },
    },
  })

  const transformedClient = transformFromDbFields.client(client)
  return successResponse(transformedClient, 'Client updated successfully')
})

// DELETE /api/admin/clients/[id] - Delete a client
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params

  // Check if client exists
  const existingClient = await prisma.clients.findUnique({
    where: { id: Number(id) },
    include: {
      _count: {
        select: {
          projects: true,
          orders: true,
          testimonials: true,
        },
      },
    },
  })

  if (!existingClient) {
    throw new ApiError('Client not found', 404)
  }

  // Check if client has related data
  const hasRelatedData = existingClient._count.projects > 0 ||
                        existingClient._count.orders > 0 ||
                        existingClient._count.testimonials > 0

  if (hasRelatedData) {
    throw new ApiError('Cannot delete client with related projects, orders, or testimonials. Please remove them first.', 400)
  }

  await prisma.clients.delete({
    where: { id: Number(id) },
  })

  return successResponse(null, 'Client deleted successfully')
})

// PATCH /api/admin/clients/[id] - Partial update (e.g., toggle status)
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params
  const body = await request.json()

  // Check if client exists
  const existingClient = await prisma.clients.findUnique({
    where: { id: Number(id) },
  })

  if (!existingClient) {
    throw new ApiError('Client not found', 404)
  }

  // Only allow specific fields for PATCH
  const allowedFields = ['isActive', 'notes']
  const updateData: any = {}

  for (const field of allowedFields) {
    if (body[field] !== undefined) {
      if (field === 'isActive') {
        updateData.isactive = body[field] === true || body[field] === 'true'
      } else {
        updateData[field] = body[field]
      }
    }
  }

  if (Object.keys(updateData).length === 0) {
    throw new ApiError('No valid fields to update', 400)
  }

  const client = await prisma.clients.update({
    where: { id: Number(id) },
    data: updateData,
    include: {
      _count: {
        select: {
          projects: true,
          orders: true,
          testimonials: true,
        },
      },
    },
  })

  const transformedClient = transformFromDbFields.client(client)
  return successResponse(transformedClient, 'Client updated successfully')
})
