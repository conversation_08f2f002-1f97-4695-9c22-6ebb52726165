import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth-config'

// Utility function to automatically infer icon and color based on category name
function inferCategoryStyle(categoryName: string) {
  const categoryLower = categoryName.toLowerCase()
  
  // Comprehensive icon inference with many more options
  let icon = 'DocumentTextIcon'
  
  // General/System categories
  if (categoryLower.includes('general') || categoryLower.includes('basic') || categoryLower.includes('main')) {
    icon = 'CogIcon'
  } else if (categoryLower.includes('system') || categoryLower.includes('server') || categoryLower.includes('infrastructure')) {
    icon = 'ServerIcon'
  } else if (categoryLower.includes('config') || categoryLower.includes('setup') || categoryLower.includes('preferences')) {
    icon = 'Cog6ToothIcon'
  }
  
  // User/Auth categories
  else if (categoryLower.includes('user') || categoryLower.includes('auth') || categoryLower.includes('login') || categoryLower.includes('account')) {
    icon = 'UserIcon'
  } else if (categoryLower.includes('profile') || categoryLower.includes('avatar') || categoryLower.includes('picture')) {
    icon = 'UserCircleIcon'
  } else if (categoryLower.includes('role') || categoryLower.includes('permission') || categoryLower.includes('access')) {
    icon = 'KeyIcon'
  }
  
  // Communication categories
  else if (categoryLower.includes('email') || categoryLower.includes('mail') || categoryLower.includes('smtp')) {
    icon = 'EnvelopeIcon'
  } else if (categoryLower.includes('chat') || categoryLower.includes('message') || categoryLower.includes('conversation')) {
    icon = 'ChatBubbleLeftRightIcon'
  } else if (categoryLower.includes('notification') || categoryLower.includes('alert') || categoryLower.includes('reminder')) {
    icon = 'BellIcon'
  } else if (categoryLower.includes('sms') || categoryLower.includes('text') || categoryLower.includes('mobile')) {
    icon = 'DevicePhoneMobileIcon'
  }
  
  // Social/Network categories
  else if (categoryLower.includes('social') || categoryLower.includes('network') || categoryLower.includes('community')) {
    icon = 'GlobeAltIcon'
  } else if (categoryLower.includes('facebook') || categoryLower.includes('twitter') || categoryLower.includes('instagram')) {
    icon = 'ShareIcon'
  } else if (categoryLower.includes('share') || categoryLower.includes('like') || categoryLower.includes('follow')) {
    icon = 'HeartIcon'
  }
  
  // Payment/Financial categories
  else if (categoryLower.includes('payment') || categoryLower.includes('billing') || categoryLower.includes('invoice')) {
    icon = 'CreditCardIcon'
  } else if (categoryLower.includes('stripe') || categoryLower.includes('paypal') || categoryLower.includes('gateway')) {
    icon = 'BanknotesIcon'
  } else if (categoryLower.includes('subscription') || categoryLower.includes('plan') || categoryLower.includes('pricing')) {
    icon = 'CurrencyDollarIcon'
  } else if (categoryLower.includes('tax') || categoryLower.includes('vat') || categoryLower.includes('gst')) {
    icon = 'ReceiptPercentIcon'
  }
  
  // Security/Privacy categories
  else if (categoryLower.includes('security') || categoryLower.includes('privacy') || categoryLower.includes('protection')) {
    icon = 'ShieldCheckIcon'
  } else if (categoryLower.includes('encryption') || categoryLower.includes('hash') || categoryLower.includes('password')) {
    icon = 'LockClosedIcon'
  } else if (categoryLower.includes('firewall') || categoryLower.includes('antivirus') || categoryLower.includes('malware')) {
    icon = 'ShieldExclamationIcon'
  } else if (categoryLower.includes('backup') || categoryLower.includes('restore') || categoryLower.includes('recovery')) {
    icon = 'ArrowPathIcon'
  }
  
  // Appearance/UI categories
  else if (categoryLower.includes('appearance') || categoryLower.includes('theme') || categoryLower.includes('style')) {
    icon = 'PaintBrushIcon'
  } else if (categoryLower.includes('color') || categoryLower.includes('palette') || categoryLower.includes('design')) {
    icon = 'SwatchIcon'
  } else if (categoryLower.includes('font') || categoryLower.includes('typography') || categoryLower.includes('text')) {
    icon = 'DocumentTextIcon'
  } else if (categoryLower.includes('layout') || categoryLower.includes('template') || categoryLower.includes('structure')) {
    icon = 'Squares2X2Icon'
  }
  
  // Contact/Location categories
  else if (categoryLower.includes('contact') || categoryLower.includes('phone') || categoryLower.includes('call')) {
    icon = 'PhoneIcon'
  } else if (categoryLower.includes('address') || categoryLower.includes('location') || categoryLower.includes('map')) {
    icon = 'MapPinIcon'
  } else if (categoryLower.includes('timezone') || categoryLower.includes('time') || categoryLower.includes('schedule')) {
    icon = 'ClockIcon'
  } else if (categoryLower.includes('country') || categoryLower.includes('region') || categoryLower.includes('locale')) {
    icon = 'FlagIcon'
  }
  
  // Analytics/Data categories
  else if (categoryLower.includes('analytics') || categoryLower.includes('chart') || categoryLower.includes('statistics')) {
    icon = 'ChartBarIcon'
  } else if (categoryLower.includes('report') || categoryLower.includes('dashboard') || categoryLower.includes('metrics')) {
    icon = 'ChartBarSquareIcon'
      } else if (categoryLower.includes('data') || categoryLower.includes('database') || categoryLower.includes('storage')) {
      icon = 'CircleStackIcon'
  } else if (categoryLower.includes('export') || categoryLower.includes('import') || categoryLower.includes('sync')) {
    icon = 'ArrowDownTrayIcon'
  }
  
  // Cloud/Storage categories
  else if (categoryLower.includes('cloud') || categoryLower.includes('aws') || categoryLower.includes('azure')) {
    icon = 'CloudIcon'
  } else if (categoryLower.includes('file') || categoryLower.includes('upload') || categoryLower.includes('media')) {
    icon = 'DocumentIcon'
  } else if (categoryLower.includes('image') || categoryLower.includes('photo') || categoryLower.includes('picture')) {
    icon = 'PhotoIcon'
  } else if (categoryLower.includes('video') || categoryLower.includes('movie') || categoryLower.includes('stream')) {
    icon = 'VideoCameraIcon'
  }
  
  // Integration/API categories
  else if (categoryLower.includes('api') || categoryLower.includes('endpoint') || categoryLower.includes('webhook')) {
    icon = 'CodeBracketIcon'
  } else if (categoryLower.includes('integration') || categoryLower.includes('connect') || categoryLower.includes('link')) {
    icon = 'LinkIcon'
  } else if (categoryLower.includes('oauth') || categoryLower.includes('sso') || categoryLower.includes('federation')) {
    icon = 'KeyIcon'
  } else if (categoryLower.includes('webhook') || categoryLower.includes('callback') || categoryLower.includes('hook')) {
    icon = 'ArrowPathRoundedSquareIcon'
  }
  
  // Performance/Technical categories
  else if (categoryLower.includes('performance') || categoryLower.includes('speed') || categoryLower.includes('optimization')) {
    icon = 'BoltIcon'
  } else if (categoryLower.includes('cache') || categoryLower.includes('memory') || categoryLower.includes('redis')) {
    icon = 'CpuChipIcon'
  } else if (categoryLower.includes('cdn') || categoryLower.includes('delivery') || categoryLower.includes('distribution')) {
    icon = 'GlobeAltIcon'
  } else if (categoryLower.includes('ssl') || categoryLower.includes('certificate') || categoryLower.includes('https')) {
    icon = 'LockClosedIcon'
  }
  
  // Business/Marketing categories
  else if (categoryLower.includes('business') || categoryLower.includes('company') || categoryLower.includes('organization')) {
    icon = 'BuildingOfficeIcon'
  } else if (categoryLower.includes('marketing') || categoryLower.includes('campaign') || categoryLower.includes('promotion')) {
    icon = 'MegaphoneIcon'
  } else if (categoryLower.includes('seo') || categoryLower.includes('search') || categoryLower.includes('google')) {
    icon = 'MagnifyingGlassIcon'
  } else if (categoryLower.includes('brand') || categoryLower.includes('logo') || categoryLower.includes('identity')) {
    icon = 'StarIcon'
  }
  
  // Support/Help categories
  else if (categoryLower.includes('support') || categoryLower.includes('help') || categoryLower.includes('assistance')) {
    icon = 'QuestionMarkCircleIcon'
  } else if (categoryLower.includes('faq') || categoryLower.includes('knowledge') || categoryLower.includes('guide')) {
    icon = 'BookOpenIcon'
  } else if (categoryLower.includes('ticket') || categoryLower.includes('issue') || categoryLower.includes('bug')) {
    icon = 'ExclamationTriangleIcon'
  } else if (categoryLower.includes('feedback') || categoryLower.includes('review') || categoryLower.includes('rating')) {
    icon = 'ChatBubbleOvalLeftIcon'
  }
  
  // Legal/Compliance categories
  else if (categoryLower.includes('legal') || categoryLower.includes('terms') || categoryLower.includes('privacy')) {
    icon = 'DocumentTextIcon'
  } else if (categoryLower.includes('gdpr') || categoryLower.includes('compliance') || categoryLower.includes('regulation')) {
    icon = 'ShieldCheckIcon'
  } else if (categoryLower.includes('cookie') || categoryLower.includes('consent') || categoryLower.includes('permission')) {
    icon = 'CheckCircleIcon'
  }
  
  // Development/Technical categories
  else if (categoryLower.includes('development') || categoryLower.includes('dev') || categoryLower.includes('code')) {
    icon = 'CodeBracketIcon'
  } else if (categoryLower.includes('testing') || categoryLower.includes('test') || categoryLower.includes('qa')) {
    icon = 'BeakerIcon'
  } else if (categoryLower.includes('deployment') || categoryLower.includes('release') || categoryLower.includes('version')) {
    icon = 'RocketLaunchIcon'
  } else if (categoryLower.includes('monitoring') || categoryLower.includes('log') || categoryLower.includes('error')) {
    icon = 'ExclamationTriangleIcon'
  }
  
  // Content/Media categories
  else if (categoryLower.includes('content') || categoryLower.includes('article') || categoryLower.includes('blog')) {
    icon = 'DocumentTextIcon'
  } else if (categoryLower.includes('news') || categoryLower.includes('announcement') || categoryLower.includes('update')) {
    icon = 'NewspaperIcon'
  } else if (categoryLower.includes('gallery') || categoryLower.includes('album') || categoryLower.includes('collection')) {
    icon = 'PhotoIcon'
  } else if (categoryLower.includes('download') || categoryLower.includes('attachment') || categoryLower.includes('file')) {
    icon = 'ArrowDownTrayIcon'
  }
  
  // Language/Localization categories
  else if (categoryLower.includes('language') || categoryLower.includes('locale') || categoryLower.includes('translation')) {
    icon = 'LanguageIcon'
  } else if (categoryLower.includes('currency') || categoryLower.includes('money') || categoryLower.includes('exchange')) {
    icon = 'CurrencyDollarIcon'
  } else if (categoryLower.includes('date') || categoryLower.includes('calendar') || categoryLower.includes('time')) {
    icon = 'CalendarIcon'
  }
  
  // Mobile/App categories
  else if (categoryLower.includes('mobile') || categoryLower.includes('app') || categoryLower.includes('ios')) {
    icon = 'DevicePhoneMobileIcon'
  } else if (categoryLower.includes('android') || categoryLower.includes('google') || categoryLower.includes('play')) {
    icon = 'DeviceTabletIcon'
  } else if (categoryLower.includes('push') || categoryLower.includes('notification') || categoryLower.includes('alert')) {
    icon = 'BellIcon'
  }
  
  // E-commerce/Shop categories
  else if (categoryLower.includes('shop') || categoryLower.includes('store') || categoryLower.includes('product')) {
    icon = 'ShoppingBagIcon'
  } else if (categoryLower.includes('cart') || categoryLower.includes('checkout') || categoryLower.includes('order')) {
    icon = 'ShoppingCartIcon'
  } else if (categoryLower.includes('inventory') || categoryLower.includes('stock') || categoryLower.includes('warehouse')) {
    icon = 'ArchiveBoxIcon'
  }
  
  // Comprehensive color inference with many more options
  let color = 'gray'
  
  // Blue themes - Professional, Trust, Technology
  if (categoryLower.includes('general') || categoryLower.includes('main') || categoryLower.includes('basic')) {
    color = 'blue'
  } else if (categoryLower.includes('system') || categoryLower.includes('config') || categoryLower.includes('setup')) {
    color = 'indigo'
  } else if (categoryLower.includes('api') || categoryLower.includes('integration') || categoryLower.includes('webhook')) {
    color = 'blue'
  } else if (categoryLower.includes('development') || categoryLower.includes('code') || categoryLower.includes('technical')) {
    color = 'blue'
  }
  
  // Green themes - Success, Money, Growth
  else if (categoryLower.includes('payment') || categoryLower.includes('billing') || categoryLower.includes('subscription')) {
    color = 'green'
  } else if (categoryLower.includes('success') || categoryLower.includes('completed') || categoryLower.includes('verified')) {
    color = 'emerald'
  } else if (categoryLower.includes('business') || categoryLower.includes('company') || categoryLower.includes('organization')) {
    color = 'green'
  } else if (categoryLower.includes('growth') || categoryLower.includes('progress') || categoryLower.includes('improvement')) {
    color = 'emerald'
  }
  
  // Red themes - Security, Danger, Errors
  else if (categoryLower.includes('security') || categoryLower.includes('privacy') || categoryLower.includes('protection')) {
    color = 'red'
  } else if (categoryLower.includes('error') || categoryLower.includes('bug') || categoryLower.includes('issue')) {
    color = 'red'
  } else if (categoryLower.includes('warning') || categoryLower.includes('alert') || categoryLower.includes('danger')) {
    color = 'red'
  } else if (categoryLower.includes('delete') || categoryLower.includes('remove') || categoryLower.includes('trash')) {
    color = 'red'
  }
  
  // Yellow themes - Warning, Attention, Performance
  else if (categoryLower.includes('notification') || categoryLower.includes('alert') || categoryLower.includes('reminder')) {
    color = 'yellow'
  } else if (categoryLower.includes('performance') || categoryLower.includes('speed') || categoryLower.includes('optimization')) {
    color = 'yellow'
  } else if (categoryLower.includes('warning') || categoryLower.includes('caution') || categoryLower.includes('attention')) {
    color = 'yellow'
  } else if (categoryLower.includes('maintenance') || categoryLower.includes('update') || categoryLower.includes('upgrade')) {
    color = 'yellow'
  }
  
  // Purple themes - Creative, Social, Premium
  else if (categoryLower.includes('social') || categoryLower.includes('network') || categoryLower.includes('community')) {
    color = 'purple'
  } else if (categoryLower.includes('appearance') || categoryLower.includes('theme') || categoryLower.includes('design')) {
    color = 'purple'
  } else if (categoryLower.includes('creative') || categoryLower.includes('art') || categoryLower.includes('media')) {
    color = 'purple'
  } else if (categoryLower.includes('premium') || categoryLower.includes('vip') || categoryLower.includes('exclusive')) {
    color = 'purple'
  }
  
  // Pink themes - Marketing, Brand, Fashion
  else if (categoryLower.includes('marketing') || categoryLower.includes('campaign') || categoryLower.includes('promotion')) {
    color = 'pink'
  } else if (categoryLower.includes('brand') || categoryLower.includes('logo') || categoryLower.includes('identity')) {
    color = 'pink'
  } else if (categoryLower.includes('fashion') || categoryLower.includes('style') || categoryLower.includes('beauty')) {
    color = 'pink'
  } else if (categoryLower.includes('romance') || categoryLower.includes('love') || categoryLower.includes('heart')) {
    color = 'pink'
  }
  
  // Emerald themes - Nature, Health, Environment
  else if (categoryLower.includes('health') || categoryLower.includes('medical') || categoryLower.includes('wellness')) {
    color = 'emerald'
  } else if (categoryLower.includes('nature') || categoryLower.includes('environment') || categoryLower.includes('eco')) {
    color = 'emerald'
  } else if (categoryLower.includes('fitness') || categoryLower.includes('sport') || categoryLower.includes('exercise')) {
    color = 'emerald'
  } else if (categoryLower.includes('organic') || categoryLower.includes('natural') || categoryLower.includes('green')) {
    color = 'emerald'
  }
  
  // Indigo themes - Technology, Innovation, Future
  else if (categoryLower.includes('technology') || categoryLower.includes('innovation') || categoryLower.includes('future')) {
    color = 'indigo'
  } else if (categoryLower.includes('ai') || categoryLower.includes('machine') || categoryLower.includes('automation')) {
    color = 'indigo'
  } else if (categoryLower.includes('blockchain') || categoryLower.includes('crypto') || categoryLower.includes('web3')) {
    color = 'indigo'
  } else if (categoryLower.includes('cloud') || categoryLower.includes('aws') || categoryLower.includes('azure')) {
    color = 'indigo'
  }
  
  // Orange themes - Energy, Action, Creativity
  else if (categoryLower.includes('energy') || categoryLower.includes('power') || categoryLower.includes('electric')) {
    color = 'orange'
  } else if (categoryLower.includes('action') || categoryLower.includes('activity') || categoryLower.includes('movement')) {
    color = 'orange'
  } else if (categoryLower.includes('creative') || categoryLower.includes('art') || categoryLower.includes('design')) {
    color = 'orange'
  } else if (categoryLower.includes('fire') || categoryLower.includes('hot') || categoryLower.includes('burn')) {
    color = 'orange'
  }
  
  // Teal themes - Communication, Information, Knowledge
  else if (categoryLower.includes('communication') || categoryLower.includes('message') || categoryLower.includes('chat')) {
    color = 'teal'
  } else if (categoryLower.includes('information') || categoryLower.includes('data') || categoryLower.includes('knowledge')) {
    color = 'teal'
  } else if (categoryLower.includes('education') || categoryLower.includes('learning') || categoryLower.includes('training')) {
    color = 'teal'
  } else if (categoryLower.includes('research') || categoryLower.includes('study') || categoryLower.includes('analysis')) {
    color = 'teal'
  }
  
  // Cyan themes - Water, Cool, Fresh
  else if (categoryLower.includes('water') || categoryLower.includes('ocean') || categoryLower.includes('sea')) {
    color = 'cyan'
  } else if (categoryLower.includes('cool') || categoryLower.includes('fresh') || categoryLower.includes('clean')) {
    color = 'cyan'
  } else if (categoryLower.includes('ice') || categoryLower.includes('cold') || categoryLower.includes('winter')) {
    color = 'cyan'
  }
  
  // Rose themes - Love, Romance, Passion
  else if (categoryLower.includes('love') || categoryLower.includes('romance') || categoryLower.includes('passion')) {
    color = 'rose'
  } else if (categoryLower.includes('heart') || categoryLower.includes('emotion') || categoryLower.includes('feeling')) {
    color = 'rose'
  }
  
  // Amber themes - Warm, Autumn, Harvest
  else if (categoryLower.includes('warm') || categoryLower.includes('autumn') || categoryLower.includes('harvest')) {
    color = 'amber'
  } else if (categoryLower.includes('gold') || categoryLower.includes('premium') || categoryLower.includes('luxury')) {
    color = 'amber'
  }
  
  // Lime themes - Spring, Growth, Youth
  else if (categoryLower.includes('spring') || categoryLower.includes('growth') || categoryLower.includes('youth')) {
    color = 'lime'
  } else if (categoryLower.includes('fresh') || categoryLower.includes('new') || categoryLower.includes('start')) {
    color = 'lime'
  }
  
  // Sky themes - Air, Freedom, Open
  else if (categoryLower.includes('air') || categoryLower.includes('freedom') || categoryLower.includes('open')) {
    color = 'sky'
  } else if (categoryLower.includes('sky') || categoryLower.includes('heaven') || categoryLower.includes('spiritual')) {
    color = 'sky'
  }
  
  // Violet themes - Royal, Luxury, Mystery
  else if (categoryLower.includes('royal') || categoryLower.includes('luxury') || categoryLower.includes('mystery')) {
    color = 'violet'
  } else if (categoryLower.includes('magic') || categoryLower.includes('mystical') || categoryLower.includes('spiritual')) {
    color = 'violet'
  }
  
  // Fuchsia themes - Bold, Vibrant, Energetic
  else if (categoryLower.includes('bold') || categoryLower.includes('vibrant') || categoryLower.includes('energetic')) {
    color = 'fuchsia'
  } else if (categoryLower.includes('party') || categoryLower.includes('celebration') || categoryLower.includes('fun')) {
    color = 'fuchsia'
  }
  
  return { icon, color }
}

// GET /api/admin/settings/categories - Get all categories with their metadata
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get all category info settings
    const categoryInfoSettings = await prisma.sitesettings.findMany({
      where: {
        key: {
          endsWith: '_CATEGORY_INFO'
        },
        fieldtype: 'json'
      },
      orderBy: {
        category: 'asc'
      }
    })

    // Parse category data and include order information
    const categories = categoryInfoSettings.map((setting, index) => {
      try {
        const categoryData = JSON.parse(setting.value)
        return {
          name: setting.category,
          label: categoryData.label || setting.category,
          description: categoryData.description || `Settings for ${categoryData.label || setting.category}`,
          color: categoryData.color || 'gray',
          icon: categoryData.icon || 'DocumentTextIcon',
          order: categoryData.order || index
        }
      } catch (error) {
        // Fallback for invalid JSON
        return {
          name: setting.category,
          label: setting.category,
          description: `Settings for ${setting.category}`,
          color: 'gray',
          icon: 'DocumentTextIcon',
          order: index
        }
      }
    })

    // Also get categories that don't have info settings (legacy categories)
    const allCategories = await prisma.sitesettings.findMany({
      select: { category: true },
      distinct: ['category'],
      where: {
        NOT: {
          key: { endsWith: '_CATEGORY_INFO' }
        }
      }
    })

    // Add legacy categories that don't have info settings
    const existingCategoryNames = categories.map(c => c.name)
    const legacyCategories = allCategories
      .filter(c => !existingCategoryNames.includes(c.category))
      .map((c, index) => {
        const inferredStyle = inferCategoryStyle(c.category)
        return {
          name: c.category,
          label: c.category.charAt(0) + c.category.slice(1).toLowerCase(),
          description: `Settings for ${c.category}`,
          color: inferredStyle.color,
          icon: inferredStyle.icon,
          order: categories.length + index
        }
      })

    const allCategoriesWithInfo = [...categories, ...legacyCategories]
      .sort((a, b) => a.order - b.order)

    return NextResponse.json({
      success: true,
      data: allCategoriesWithInfo,
      message: 'Categories retrieved successfully'
    })

  } catch (error) {
    console.error('Error fetching categories:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/admin/settings/categories - Create a new category (by creating a placeholder setting)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()

    if (!body.name || !body.label) {
      return NextResponse.json(
        { success: false, error: 'Category name and label are required' },
        { status: 400 }
      )
    }

    const categoryName = body.name.toUpperCase().replace(/\s+/g, '_')

    // Check if category already exists
    const existingCategory = await prisma.sitesettings.findFirst({
      where: { category: categoryName }
    })

    if (existingCategory) {
      return NextResponse.json(
        { success: false, error: 'Category already exists' },
        { status: 400 }
      )
    }

    // Automatically infer icon and color based on category name
    const inferredStyle = inferCategoryStyle(categoryName)
    
    // Build style JSON with automatic inference
    const styleJson = {
      label: body.label,
      description: body.description || `Settings for ${body.label}`,
      color: body.color || inferredStyle.color,
      icon: body.icon || inferredStyle.icon,
      order: body.order || 0
    }

    // Create a placeholder setting for the new category
    const placeholderSetting = await prisma.sitesettings.create({
      data: {
        key: `${categoryName}_CATEGORY_INFO`,
        value: JSON.stringify(styleJson),
        category: categoryName,
        fieldtype: 'json',
        description: `Category information for ${body.label}`,
        ispublic: false,
        isactive: true,
        categorystyle: styleJson
      } as any
    })

    // Propagate style to all settings in this category (should be none on creation)

    return NextResponse.json({
      success: true,
      data: {
        name: categoryName,
        label: body.label,
        description: body.description,
        color: body.color,
        icon: body.icon,
        order: body.order,
        categorystyle: styleJson
      },
      message: 'Category created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating category:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/admin/settings/categories - Update an existing category
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()

    if (!body.name) {
      return NextResponse.json(
        { success: false, error: 'Category name is required' },
        { status: 400 }
      )
    }

    const categoryName = body.name.toUpperCase().replace(/\s+/g, '_')

    // Use the categorystyle from the request if provided, otherwise build from individual fields
    let styleJson = body.categorystyle
    if (!styleJson) {
      const inferredStyle = inferCategoryStyle(categoryName)
      styleJson = {
        label: body.label,
        description: body.description || `Settings for ${body.label}`,
        color: body.color || inferredStyle.color,
        icon: body.icon || inferredStyle.icon,
        order: body.order || 0
      }
    }

    console.log('Updating category:', categoryName, 'with style:', styleJson)

    // Update the categorystyle for all settings in this category
    const updateResult = await prisma.sitesettings.updateMany({
      where: { category: categoryName },
      data: { categorystyle: styleJson } as any
    });

    // Fetch updated settings to return to the frontend (optional)
    // const updatedSettings = await prisma.sitesettings.findMany({
    //   where: { category: categoryName },
    //   select: { category: true, categorystyle: true } as any
    // });

    return NextResponse.json({
      success: true,
      data: {
        name: categoryName,
        label: styleJson.label,
        description: styleJson.description,
        color: styleJson.color,
        icon: styleJson.icon,
        order: styleJson.order,
        categorystyle: styleJson
      },
      message: `Category theme updated for all settings in this category. Updated ${updateResult.count} records.`
      // , updatedSettings
    });

  } catch (error) {
    console.error('Error updating category:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
