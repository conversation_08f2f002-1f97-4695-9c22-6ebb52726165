import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON>rror<PERSON>and<PERSON>,
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth-config'

// GET /api/admin/settings - Get all site settings with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['key', 'value', 'description', 'category'])
  
  // Add category filter if provided
  if (filter) {
    searchQuery.category = filter
  }
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get site settings with pagination
  const [siteSettings, total] = await Promise.all([
    prisma.sitesettings.findMany({
      where: searchQuery,
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.sitesettings.count({ where: searchQuery })
  ])

  return paginatedResponse(siteSettings, page, limit, total)
})

// POST /api/admin/settings - Create a new site setting
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()

    // Validate required fields
    if (!body.key) {
      return NextResponse.json(
        { success: false, error: 'Key is required' },
        { status: 400 }
      )
    }

    // Allow empty values for optional settings
    const value = body.value || ''

    // Create the setting
    const siteSetting = await prisma.sitesettings.create({
      data: {
        key: body.key,
        value: value,
        description: body.description || null,
        icon: body.icon || null,
        category: body.category || 'GENERAL',
        fieldtype: body.fieldType || 'text',
        options: body.options || null,
        isactive: body.isactive !== undefined ? body.isactive : true,
        ispublic: body.ispublic !== undefined ? body.ispublic : true,
      }
    })

    // Convert BigInt to string for JSON serialization
    const serializedSetting = {
      ...siteSetting,
      id: siteSetting.id.toString(),
      createdat: siteSetting.createdat?.toISOString(),
      updatedat: siteSetting.updatedat?.toISOString(),
    }

    return NextResponse.json({
      success: true,
      data: serializedSetting,
      message: 'Site setting created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating site setting:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
