import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/job-applications - Get all job applications with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['applicantName', 'applicantEmail', 'applicantPhone'])
  
  // Add status filter if provided
  if (filter) {
    searchQuery.status = filter
  }
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get job applications with pagination
  const [jobApplications, total] = await Promise.all([
    prisma.jobapplications.findMany({
      where: searchQuery,
      include: {
        jobListing: {
          select: {
            id: true,
            title: true,
            location: true,
            employmenttype: true,
            isactive: true
          }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.jobapplications.count({ where: searchQuery })
  ])

  return paginatedResponse(jobApplications, page, limit, total)
})

// POST /api/admin/job-applications - Create a new job application (usually from public form)
export const POST = withErrorHandler(async (request: NextRequest) => {
  const validate = validateRequest(schemas.jobApplication.create)
  const data = await validate(request)

  // Check if job listing exists and is active
  const jobListing = await prisma.joblistings.findUnique({
    where: { id: data.jobListingId },
  })

  if (!jobListing) {
    throw new Error('Job listing not found')
  }

  if (!jobListing.isactive) {
    throw new Error('Job listing is no longer active')
  }

  const jobApplication = await prisma.jobapplications.create({
    data,
    include: {
      jobListing: {
        select: {
          id: true,
          title: true,
          location: true,
          employmenttype: true
        }
      }
    }
  })

  return successResponse(jobApplication, 'Job application submitted successfully', 201)
})
