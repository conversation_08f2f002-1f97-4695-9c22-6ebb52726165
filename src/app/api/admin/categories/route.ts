import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/categories - Get all categories with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query using correct database field names
  const searchQuery = buildSearchQuery(search, ['categname', 'categdesc'])

  // Build sort query using correct database field names
  const sortQuery = buildSortQuery(sortBy, sortOrder, 'displayorder')

  // Get categories with pagination
  const [categories, total] = await Promise.all([
    prisma.categories.findMany({
      where: searchQuery,
      include: {
        services: {
          select: {
            id: true,
            name: true,
            isactive: true
          }
        },
        _count: {
          select: {
            services: true
          }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.categories.count({ where: searchQuery })
  ])

  // Transform the data to match frontend expectations
  const transformedCategories = categories.map(cat => ({
    id: String(cat.id),
    categname: cat.categname,
    categdesc: cat.categdesc,
    parentid: cat.parentid,
    isactive: cat.isactive,
    displayorder: cat.displayorder,
    createdat: cat.createdat,
    updatedat: cat.updatedat,
    services: cat.services,
    _count: cat._count
  }))

  return paginatedResponse(transformedCategories, page, limit, total)
})

// POST /api/admin/categories - Create a new category
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const body = await request.json()

  // Transform frontend data to database field names
  const dbData = {
    categname: body.categname || body.name,
    categdesc: body.categdesc || body.description,
    parentid: body.parentid ? Number(body.parentid) : 0,
    isactive: body.isactive !== undefined ? body.isactive : true,
    displayorder: body.displayorder || 0
  }

  const category = await prisma.categories.create({
    data: dbData,
    include: {
      services: {
        select: {
          id: true,
          name: true,
          isactive: true
        }
      },
      _count: {
        select: {
          services: true
        }
      }
    }
  })

  // Transform the response back to frontend format
  const transformedCategory = {
    id: String(category.id),
    categname: category.categname,
    categdesc: category.categdesc,
    parentid: category.parentid,
    isactive: category.isactive,
    displayorder: category.displayorder,
    createdat: category.createdat,
    updatedat: category.updatedat,
    services: category.services,
    _count: category._count
  }

  return successResponse(transformedCategory, 'Category created successfully', 201)
})
