import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/jobs/[id] - Get a specific job listing
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const jobListing = await prisma.joblistings.findUnique({
    where: { id: BigInt(id) },
    include: {
      jobapplications: {
        select: {
          id: true,
          applicantname: true,
          applicantemail: true,
          status: true,
          createdat: true
        },
        orderBy: { createdat: 'desc' }
      }
    }
  })

  if (!jobListing) {
    throw new ApiError('Job listing not found', 404)
  }

  // Transform BigInt to string for JSON serialization
  const transformedJobListing = {
    ...jobListing,
    id: jobListing.id.toString(),
    salarymin: jobListing.salarymin ? Number(jobListing.salarymin) : null,
    salarymax: jobListing.salarymax ? Number(jobListing.salarymax) : null,
    jobapplications: jobListing.jobapplications?.map(app => ({
      ...app,
      id: app.id.toString()
    }))
  }

  return successResponse(transformedJobListing)
})

// PUT /api/admin/jobs/[id] - Update a job listing
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.jobListing.update)
  const data = await validate(request)

  // Check if job listing exists
  const existingJob = await prisma.joblistings.findUnique({
    where: { id: BigInt(id) },
  })

  if (!existingJob) {
    throw new ApiError('Job listing not found', 404)
  }

  const jobListing = await prisma.joblistings.update({
    where: { id: BigInt(id) },
    data,
    include: {
      jobapplications: {
        select: {
          id: true,
          applicantname: true,
          applicantemail: true,
          status: true,
          createdat: true
        }
      }
    }
  })

  // Transform BigInt to string for JSON serialization
  const transformedJobListing = {
    ...jobListing,
    id: jobListing.id.toString(),
    salarymin: jobListing.salarymin ? Number(jobListing.salarymin) : null,
    salarymax: jobListing.salarymax ? Number(jobListing.salarymax) : null,
    jobapplications: jobListing.jobapplications?.map(app => ({
      ...app,
      id: app.id.toString()
    }))
  }

  return successResponse(transformedJobListing, 'Job listing updated successfully')
})

// PATCH /api/admin/jobs/[id] - Update job status
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const body = await request.json()

  // Check if job listing exists
  const existingJob = await prisma.joblistings.findUnique({
    where: { id: BigInt(id) },
  })

  if (!existingJob) {
    throw new ApiError('Job listing not found', 404)
  }

  const jobListing = await prisma.joblistings.update({
    where: { id: BigInt(id) },
    data: {
      isactive: body.isactive,
      updatedat: new Date()
    },
    include: {
      jobapplications: {
        select: {
          id: true,
          applicantname: true,
          applicantemail: true,
          status: true,
          createdat: true
        }
      }
    }
  })

  // Transform BigInt to string for JSON serialization
  const transformedJobListing = {
    ...jobListing,
    id: jobListing.id.toString(),
    salarymin: jobListing.salarymin ? Number(jobListing.salarymin) : null,
    salarymax: jobListing.salarymax ? Number(jobListing.salarymax) : null,
    jobapplications: jobListing.jobapplications?.map(app => ({
      ...app,
      id: app.id.toString()
    }))
  }

  return successResponse(transformedJobListing, 'Job status updated successfully')
})

// DELETE /api/admin/jobs/[id] - Delete a job listing
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if job listing exists
  const existingJob = await prisma.joblistings.findUnique({
    where: { id: BigInt(id) },
  })

  if (!existingJob) {
    throw new ApiError('Job listing not found', 404)
  }

  // Delete all applications first (using correct foreign key field name)
  await prisma.jobapplications.deleteMany({
    where: { joblistingid: BigInt(id) }
  })

  // Then delete the job listing
  await prisma.joblistings.delete({
    where: { id: BigInt(id) }
  })

  return successResponse(null, 'Job listing deleted successfully')
})
