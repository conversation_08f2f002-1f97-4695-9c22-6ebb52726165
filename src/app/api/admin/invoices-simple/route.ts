import { NextRequest, NextResponse } from 'next/server'

// Very simple API endpoint for testing
export async function GET(request: NextRequest) {
  try {
    console.log('Simple invoices API called')
    
    // Return empty data in the expected format
    return NextResponse.json({
      success: true,
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0
      }
    })
  } catch (error) {
    console.error('Simple API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Simple API error',
      code: 'INTERNAL_ERROR'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('Simple POST request:', body)
    
    // Return mock created invoice
    return NextResponse.json({
      success: true,
      data: {
        id: '1',
        ...body,
        createdat: new Date().toISOString(),
        updatedat: new Date().toISOString()
      },
      message: 'Invoice created successfully'
    }, { status: 201 })
  } catch (error) {
    console.error('Simple POST error:', error)
    return NextResponse.json({
      success: false,
      error: 'Simple POST error',
      code: 'INTERNAL_ERROR'
    }, { status: 500 })
  }
}
