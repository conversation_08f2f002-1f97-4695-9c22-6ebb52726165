import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON>rror<PERSON>and<PERSON>,
  paginatedResponse,
  successResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  ApiError
} from '@/lib/api-utils'

// GET /api/admin/services - List all services with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter, categoryId } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}

  // Filter by category if provided using direct foreign key field
  if (categoryId) {
    console.log('🔍 FILTERING SERVICES - categoryId:', categoryId)
    where.categid = BigInt(categoryId)
    console.log('🔍 FILTERING SERVICES - where clause:', { categid: where.categid.toString() })
  } else {
    console.log('🔍 FILTERING SERVICES - No categoryId, fetching all services')
  }

  // Add search functionality using correct database field names
  if (search && search.trim()) {
    const searchQuery = buildSearchQuery(search.trim(), ['name', 'description'])
    Object.assign(where, searchQuery)
  }

  // Add filter for active/inactive services
  if (filter === 'active') {
    where.isactive = true
  } else if (filter === 'inactive') {
    where.isactive = false
  }

  // Build orderBy clause
  const orderBy: any = {}
  if (sortBy) {
    orderBy[sortBy] = sortOrder || 'asc'
  } else {
    orderBy.displayorder = 'asc'
  }

  const [services, total] = await Promise.all([
    prisma.services.findMany({
      where,
      skip,
      take,
      orderBy,
      include: {
        categories: {
          select: {
            id: true,
            categname: true,
            categdesc: true,
          },
        },
        serviceoptions: {
          select: {
            id: true,
            optname: true,
            optprice: true,
          },
        },
        _count: {
          select: {
            orderdetails: true,
            serviceoptions: true,
          },
        },
      },
    }),
    prisma.services.count({ where }),
  ])

  console.log('🔍 FILTERING SERVICES - Found:', services.length, 'services')
  if (categoryId) {
    console.log('🔍 FILTERING SERVICES - Results:', services.map(s => `${s.name} (categid: ${s.categid})`))
  }

  // Transform the data for frontend
  const transformedServices = services.map(service => ({
    id: String(service.id),
    categoryId: String(service.categid),
    name: service.name,
    description: service.description,
    iconClass: service.iconclass,
    price: service.price,
    discountRate: service.discountrate,
    totalDiscount: service.totaldiscount,
    manager: service.manager,
    isActive: service.isactive,
    displayOrder: service.displayorder,
    createdAt: service.createdat,
    updatedAt: service.updatedat,
    category: service.categories ? {
      id: String(service.categories.id),
      name: service.categories.categname
    } : null,
    _count: {
      serviceOptions: service._count.serviceoptions,
      orderDetails: service._count.orderdetails
    }
  }))

  return paginatedResponse(transformedServices, page, limit, total)
})

// POST /api/admin/services - Create a new service
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const body = await request.json()

  // Check if category exists
  const category = await prisma.categories.findUnique({
    where: { id: BigInt(body.categoryId) },
  })

  if (!category) {
    throw new ApiError('Category not found', 404)
  }

  // Transform frontend data to database field names
  const dbData = {
    categid: BigInt(body.categoryId),
    name: body.name,
    description: body.description,
    iconclass: body.iconClass,
    price: body.price,
    discountrate: body.discountRate || 0,
    totaldiscount: body.totalDiscount || 0,
    manager: body.manager,
    isactive: body.isActive !== undefined ? body.isActive : true,
    displayorder: body.displayOrder || 0
  }

  const service = await prisma.services.create({
    data: dbData,
    include: {
      categories: {
        select: {
          id: true,
          categname: true,
          categdesc: true,
        },
      },
      serviceoptions: {
        select: {
          id: true,
          optname: true,
          optprice: true,
        },
      },
      _count: {
        select: {
          orderdetails: true,
          serviceoptions: true,
        },
      },
    },
  })

  // Transform the response for frontend
  const transformedService = {
    id: String(service.id),
    categoryId: String(service.categid),
    name: service.name,
    description: service.description,
    iconClass: service.iconclass,
    price: service.price,
    discountRate: service.discountrate,
    totalDiscount: service.totaldiscount,
    manager: service.manager,
    isActive: service.isactive,
    displayOrder: service.displayorder,
    createdAt: service.createdat,
    updatedAt: service.updatedat,
    category: service.categories ? {
      id: String(service.categories.id),
      name: service.categories.categname
    } : null,
    _count: {
      serviceOptions: service._count.serviceoptions,
      orderDetails: service._count.orderdetails
    }
  }

  return successResponse(transformedService, 'Service created successfully', 201)
})

// POST /api/admin/services/bulk-delete - Bulk delete services
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const { ids } = await request.json()

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid or empty IDs array')
  }

  // Check if any services are being used in orders
  const servicesInUse = await prisma.services.findMany({
    where: {
      id: { in: ids.map(id => BigInt(id)) },
      orderdetails: { some: {} },
    },
    select: { id: true, name: true },
  })

  if (servicesInUse.length > 0) {
    throw new ApiError(
      `Cannot delete services that are used in orders: ${servicesInUse.map(s => s.name).join(', ')}`,
      400,
      'SERVICES_USED_IN_ORDERS'
    )
  }

  // Check if any services have service options
  const servicesWithOptions = await prisma.services.findMany({
    where: {
      id: { in: ids.map(id => BigInt(id)) },
      serviceoptions: { some: {} },
    },
    select: { id: true, name: true },
  })

  if (servicesWithOptions.length > 0) {
    throw new ApiError(
      `Cannot delete services that have service options: ${servicesWithOptions.map(s => s.name).join(', ')}. Please delete the service options first.`,
      400,
      'SERVICES_HAVE_OPTIONS'
    )
  }

  // Delete services
  const result = await prisma.services.deleteMany({
    where: { id: { in: ids.map(id => BigInt(id)) } },
  })

  return successResponse(
    { deletedCount: result.count },
    `${result.count} service(s) deleted successfully`
  )
})
