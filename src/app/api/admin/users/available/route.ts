import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, requireAdmin } from '@/lib/api-utils'

// GET /api/admin/users/available - Get users available for linking (CLIENT role users not already linked)
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const url = new URL(request.url)
  const excludeClientId = url.searchParams.get('excludeClientId') // For edit mode

  // Get CLIENT role users only (for client linking)
  const where: any = {
    role: 'CLIENT',
    isactive: true
  }

  const users = await prisma.users.findMany({
    where,
    select: {
      id: true,
      email: true,
      firstname: true,
      lastname: true,
      role: true
    },
    orderBy: {
      email: 'asc'
    }
  })

  // Convert BigInt to number for JSON serialization
  const serializedUsers = users.map(user => ({
    value: Number(user.id),
    label: `${user.email} (${user.firstname || ''} ${user.lastname || ''} - ${user.role})`.trim().replace(/\s+/g, ' '),
    email: user.email,
    role: user.role
  }))

  return Response.json({
    success: true,
    data: serializedUsers
  })
})
