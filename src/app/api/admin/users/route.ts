import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import bcrypt from 'bcryptjs'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/users - Get all users with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const url = new URL(request.url)
  const page = parseInt(url.searchParams.get('page') || '1')
  const limit = parseInt(url.searchParams.get('limit') || '10')
  const search = url.searchParams.get('search') || ''
  const sortBy = url.searchParams.get('sortBy') || 'updatedat'
  const sortOrder = url.searchParams.get('sortOrder') || 'desc'
  const role = url.searchParams.get('role') || ''
  const isactive = url.searchParams.get('isactive') || ''
  const emailverified = url.searchParams.get('emailverified') || ''

  const skip = (page - 1) * limit

  // Build where clause
  const where: any = {}

  // Add search functionality
  if (search && search.trim()) {
    const searchTerm = search.trim()
    where.OR = [
      { email: { contains: searchTerm, mode: 'insensitive' } },
      { firstname: { contains: searchTerm, mode: 'insensitive' } },
      { lastname: { contains: searchTerm, mode: 'insensitive' } },
    ]
  }

  // Add role filter
  if (role) {
    where.role = role
  }

  // Add active status filter
  if (isactive !== '') {
    where.isactive = isactive === 'true'
  }

  // Add email verified filter
  if (emailverified !== '') {
    where.emailverified = emailverified === 'true' ? { not: null } : null
  }

  // Build sort query
  const orderBy: any = {}
  orderBy[sortBy] = sortOrder

  // Get users with pagination (exclude password field)
  const [users, total] = await Promise.all([
    prisma.users.findMany({
      where,
      select: {
        id: true,
        email: true,
        emailverified: true,
        firstname: true,
        lastname: true,
        imageurl: true,
        role: true,
        isactive: true,
        createdat: true,
        updatedat: true,
        clients: {
          select: {
            id: true,
            companyname: true,
            contactname: true
          },
          take: 1 // Get the first linked client
        },
        _count: {
          select: {
            clients: true,
            auditlogs: true
          }
        }
      },
      orderBy,
      skip,
      take: limit,
    }),
    prisma.users.count({ where })
  ])

  // Convert BigInt to number for JSON serialization
  const serializedUsers = users.map(user => ({
    ...user,
    id: Number(user.id),
    linkedclient: user.clients[0] ? {
      id: Number(user.clients[0].id),
      companyname: user.clients[0].companyname,
      contactname: user.clients[0].contactname
    } : null,
    linkedclientid: user.clients[0] ? Number(user.clients[0].id) : null,
    clients: undefined // Remove the clients array from response
  }))

  return Response.json({
    data: serializedUsers,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit)
  })
})

// POST /api/admin/users - Create a new user
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const body = await request.json()

  // Basic validation
  const data = {
    email: body.email?.trim(),
    firstname: body.firstname?.trim() || null,
    lastname: body.lastname?.trim() || null,
    imageurl: body.imageurl?.trim() || null,
    role: body.role || 'USER',
    isactive: body.isactive ?? true,
    password: body.password?.trim(),
    linkedclientid: body.linkedclientid ? parseInt(body.linkedclientid) : null
  }

  // Validate required fields
  if (!data.email) {
    throw new Error('Email address is required')
  }

  if (!data.password) {
    throw new Error('Password is required for new users')
  }

  // Check if user with this email already exists
  const existingUser = await prisma.users.findUnique({
    where: { email: data.email }
  })

  if (existingUser) {
    throw new Error('A user with this email already exists')
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(data.password, 12)

  // If linking to a client, validate the client exists and is not already linked
  if (data.linkedclientid) {
    const existingClient = await prisma.clients.findUnique({
      where: { id: BigInt(data.linkedclientid) },
      include: { users: true }
    })

    if (!existingClient) {
      throw new Error('Selected client does not exist')
    }

    if (existingClient.userid) {
      throw new Error('Selected client is already linked to another user')
    }
  }

  const user = await prisma.users.create({
    data: {
      email: data.email,
      firstname: data.firstname,
      lastname: data.lastname,
      imageurl: data.imageurl,
      role: data.role,
      isactive: data.isactive,
      password: hashedPassword
    },
    select: {
      id: true,
      email: true,
      emailverified: true,
      firstname: true,
      lastname: true,
      imageurl: true,
      role: true,
      isactive: true,
      createdat: true,
      updatedat: true
    }
  })

  // If linking to a client, update the client record
  if (data.linkedclientid) {
    await prisma.clients.update({
      where: { id: BigInt(data.linkedclientid) },
      data: { userid: user.id }
    })
  }

  // Convert BigInt to number for JSON serialization
  const serializedUser = {
    ...user,
    id: Number(user.id)
  }

  return Response.json({
    success: true,
    data: serializedUser,
    message: 'User created successfully'
  }, { status: 201 })
})
