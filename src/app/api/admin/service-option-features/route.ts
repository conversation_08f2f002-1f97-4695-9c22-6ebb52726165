import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse, 
  paginatedResponse,
  validateRequest,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  ApiError
} from '@/lib/api-utils'
import { createServiceOptionFeatureSchema } from '@/lib/validations'

// GET /api/admin/service-option-features - List all service option features with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, optionId } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}

  // Filter by option if provided using direct foreign key field
  if (optionId) {
    console.log('🔍 FILTERING FEATURES - optionId:', optionId)
    where.optid = BigInt(optionId)
    console.log('🔍 FILTERING FEATURES - where clause:', { optid: where.optid.toString() })
  } else {
    console.log('🔍 FILTERING FEATURES - No optionId, fetching all features')
  }

  // Add search functionality
  if (search && search.trim()) {
    const searchQuery = buildSearchQuery(search.trim(), ['featname', 'featdesc'])
    Object.assign(where, searchQuery)
  }

  // Build sort query with correct field mapping
  const sortQuery: any = {}
  if (sortBy === 'name') {
    sortQuery.featname = sortOrder
  } else if (sortBy === 'cost') {
    sortQuery.featcost = sortOrder
  } else if (sortBy === 'createdAt') {
    sortQuery.createdat = sortOrder
  } else if (sortBy === 'updatedAt') {
    sortQuery.updatedat = sortOrder
  } else {
    sortQuery.createdat = sortOrder // default sort
  }

  // Get service option features with pagination
  const [features, total] = await Promise.all([
    prisma.serviceoptionfeatures.findMany({
      where,
      orderBy: sortQuery,
      skip,
      take,
      include: {
        serviceoptions: {
          select: {
            id: true,
            optname: true,
            services: {
              select: {
                id: true,
                name: true,
                categories: {
                  select: {
                    id: true,
                    categname: true,
                  },
                },
              },
            },
          },
        },
        _count: {
          select: {
            orderdetails: true,
          },
        },
      },
    }),
    prisma.serviceoptionfeatures.count({ where })
  ])

  // Transform the response data
  const transformedFeatures = features.map(feature => ({
    id: String(feature.id),
    optionId: String(feature.optid),
    name: feature.featname,
    description: feature.featdesc,
    cost: feature.featcost,
    discountRate: feature.featdiscountrate,
    totalDiscount: feature.feattotaldiscount,
    isIncluded: feature.isincluded,
    createdAt: feature.createdat,
    updatedAt: feature.updatedat,
    option: feature.serviceoptions ? {
      id: String(feature.serviceoptions.id),
      name: feature.serviceoptions.optname,
      service: feature.serviceoptions.services ? {
        id: String(feature.serviceoptions.services.id),
        name: feature.serviceoptions.services.name,
        category: feature.serviceoptions.services.categories ? {
          id: String(feature.serviceoptions.services.categories.id),
          name: feature.serviceoptions.services.categories.categname,
        } : null,
      } : null,
    } : null,
    _count: {
      orderDetails: feature._count.orderdetails,
    },
  }))

  return paginatedResponse(transformedFeatures, page, limit, total)
})

// POST /api/admin/service-option-features - Create a new service option feature
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const validate = validateRequest(createServiceOptionFeatureSchema)
  const data = await validate(request)

  // Check if service option exists
  const serviceOption = await prisma.serviceoptions.findUnique({
    where: { id: BigInt(data.optionId) },
  })

  if (!serviceOption) {
    throw new ApiError('Service option not found', 404)
  }

  const feature = await prisma.serviceoptionfeatures.create({
    data: {
      optid: BigInt(data.optionId),
      featname: data.name,
      featdesc: data.description,
      featcost: data.cost,
      featdiscountrate: data.discountRate,
      feattotaldiscount: data.totalDiscount,
      isincluded: data.isIncluded,
    },
    include: {
      serviceoptions: {
        select: {
          id: true,
          optname: true,
          services: {
            select: {
              id: true,
              name: true,
              categories: {
                select: {
                  id: true,
                  categname: true,
                },
              },
            },
          },
        },
      },
      _count: {
        select: {
          orderdetails: true,
        },
      },
    },
  })

  // Transform the response data
  const transformedFeature = {
    id: String(feature.id),
    optionId: String(feature.optid),
    name: feature.featname,
    description: feature.featdesc,
    cost: feature.featcost,
    discountRate: feature.featdiscountrate,
    totalDiscount: feature.feattotaldiscount,
    isIncluded: feature.isincluded,
    createdAt: feature.createdat,
    updatedAt: feature.updatedat,
    option: feature.serviceoptions ? {
      id: String(feature.serviceoptions.id),
      name: feature.serviceoptions.optname,
      service: feature.serviceoptions.services ? {
        id: String(feature.serviceoptions.services.id),
        name: feature.serviceoptions.services.name,
        category: feature.serviceoptions.services.categories ? {
          id: String(feature.serviceoptions.services.categories.id),
          name: feature.serviceoptions.services.categories.categname,
        } : null,
      } : null,
    } : null,
    _count: {
      orderDetails: feature._count.orderdetails,
    },
  }

  return successResponse(transformedFeature, 'Service option feature created successfully', 201)
})

// DELETE /api/admin/service-option-features - Bulk delete service option features
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { ids } = await request.json()

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new ApiError('Invalid or empty IDs array', 400)
  }

  // Check if any features are being used in orders
  const usedFeatures = await prisma.orderdetails.findMany({
    where: { featid: { in: ids.map(Number) } },
    select: { featid: true },
  })

  if (usedFeatures.length > 0) {
    const usedIds = usedFeatures.map(order => order.featid).filter(Boolean)
    throw new ApiError(
      `Cannot delete service option features that are used in orders. Features with IDs: ${usedIds.join(', ')}`,
      400
    )
  }

  // Delete service option features
  const result = await prisma.serviceoptionfeatures.deleteMany({
    where: { id: { in: ids.map(Number) } },
  })

  return successResponse(
    { deletedCount: result.count },
    `${result.count} service option feature(s) deleted successfully`
  )
})
