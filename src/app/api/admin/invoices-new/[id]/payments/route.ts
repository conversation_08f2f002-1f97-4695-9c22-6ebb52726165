import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: invoiceId } = await params
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    
    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      invoiceid: parseInt(invoiceId)
    }

    if (search) {
      where.OR = [
        { paymentmethod: { contains: search, mode: 'insensitive' } },
        { status: { contains: search, mode: 'insensitive' } },
        { notes: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (status) {
      where.status = status
    }

    // Get payments with related data
    const [payments, total] = await Promise.all([
      prisma.payments.findMany({
        where,
        include: {
          invoices: {
            select: {
              id: true,
              totalamount: true,
              status: true,
              duedate: true,
              description: true,
              projects: {
                select: {
                  id: true,
                  name: true
                }
              },
              clients: {
                select: {
                  id: true,
                  companyname: true
                }
              }
            }
          }
        },
        orderBy: { paymentdate: 'desc' },
        skip,
        take: limit
      }),
      prisma.payments.count({ where })
    ])

    // Transform the data to match expected format
    const transformedPayments = payments.map(payment => ({
      id: Number(payment.id),
      amount: Number(payment.amount),
      paymentDate: payment.paymentdate.toISOString(),
      paymentMethod: payment.paymentmethod,
      status: payment.status,
      notes: payment.notes,
      invoiceId: Number(payment.invoiceid),
      createdAt: payment.createdat.toISOString(),
      updatedAt: payment.updatedat?.toISOString(),
      invoice: payment.invoices ? {
        id: Number(payment.invoices.id),
        totalAmount: Number(payment.invoices.totalamount),
        status: payment.invoices.status,
        dueDate: payment.invoices.duedate.toISOString(),
        description: payment.invoices.description,
        project: payment.invoices.projects ? {
          id: Number(payment.invoices.projects.id),
          name: payment.invoices.projects.name
        } : null,
        client: payment.invoices.clients ? {
          id: Number(payment.invoices.clients.id),
          companyname: payment.invoices.clients.companyname
        } : null
      } : null
    }))

    return NextResponse.json({
      success: true,
      data: transformedPayments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching invoice payments:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch payments',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const invoiceId = parseInt(id)
    const body = await request.json()

    const {
      amount,
      paymentMethod,
      paymentDate,
      notes,
      status,
      reference,
      transactionId,
      processingFee,
      clientId,
      projectId,
      // Enhanced payment form fields
      currency,
      promoCode,
      discount,
      emailReceipt,
      receiptEmail,
      termsAccepted,
      paymentDetails,
      stripePaymentIntentId,
      stripeClientSecret
    } = body

    // Validate required fields
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { success: false, error: 'Valid payment amount is required' },
        { status: 400 }
      )
    }

    if (!paymentMethod) {
      return NextResponse.json(
        { success: false, error: 'Payment method is required' },
        { status: 400 }
      )
    }

    if (!paymentDate) {
      return NextResponse.json(
        { success: false, error: 'Payment date is required' },
        { status: 400 }
      )
    }

    // Verify invoice exists and get total amount
    const invoice = await prisma.invoices.findUnique({
      where: { id: invoiceId },
      select: {
        id: true,
        totalamount: true,
        status: true
      }
    })

    if (!invoice) {
      return NextResponse.json(
        { success: false, error: 'Invoice not found' },
        { status: 404 }
      )
    }

    // Check if payment amount exceeds remaining balance
    const existingPayments = await prisma.payments.findMany({
      where: { invoiceid: invoiceId },
      select: { amount: true }
    })

    const totalPaid = existingPayments.reduce((sum, payment) => sum + Number(payment.amount), 0)
    const remainingBalance = Number(invoice.totalamount) - totalPaid

    if (amount > remainingBalance) {
      return NextResponse.json(
        {
          success: false,
          error: `Payment amount ($${amount}) exceeds remaining balance ($${remainingBalance.toFixed(2)})`
        },
        { status: 400 }
      )
    }

    // Create the payment
    const payment = await prisma.payments.create({
      data: {
        invoiceid: invoiceId,
        amount: amount,
        paymentdate: new Date(paymentDate),
        paymentmethod: paymentMethod,
        status: status || 'completed',
        notes: notes || null,
        reference: reference || null,
        transactionid: transactionId || null,
        processingfee: processingFee || null,
        // Enhanced payment form fields
        currency: currency || 'USD',
        promocode: promoCode || null,
        discount: discount || 0,
        emailreceipt: emailReceipt || false,
        receiptemail: receiptEmail || null,
        termsaccepted: termsAccepted !== undefined ? termsAccepted : true,
        paymentdetails: paymentDetails || null,
        stripepaymentintentid: stripePaymentIntentId || null,
        stripeclientsecret: stripeClientSecret || null
      },
      include: {
        invoices: {
          select: {
            id: true,
            totalamount: true,
            status: true,
            duedate: true,
            description: true,
            projects: {
              select: {
                id: true,
                name: true
              }
            },
            clients: {
              select: {
                id: true,
                companyname: true
              }
            }
          }
        }
      }
    })

    // Update invoice status if fully paid
    const newTotalPaid = totalPaid + amount
    if (newTotalPaid >= Number(invoice.totalamount)) {
      await prisma.invoices.update({
        where: { id: invoiceId },
        data: {
          status: 'paid',
          paidat: new Date()
        }
      })
    } else if (invoice.status === 'draft') {
      // Update to pending if it was draft and now has partial payment
      await prisma.invoices.update({
        where: { id: invoiceId },
        data: { status: 'pending' }
      })
    }

    // Transform the response
    const transformedPayment = {
      id: Number(payment.id),
      amount: Number(payment.amount),
      paymentDate: payment.paymentdate.toISOString(),
      paymentMethod: payment.paymentmethod,
      status: payment.status,
      notes: payment.notes,
      reference: payment.reference,
      transactionId: payment.transactionid,
      processingFee: payment.processingfee ? Number(payment.processingfee) : null,
      invoiceId: Number(payment.invoiceid),
      createdAt: payment.createdat.toISOString(),
      updatedAt: payment.updatedat?.toISOString(),
      invoice: payment.invoices ? {
        id: Number(payment.invoices.id),
        totalAmount: Number(payment.invoices.totalamount),
        status: payment.invoices.status,
        dueDate: payment.invoices.duedate.toISOString(),
        description: payment.invoices.description,
        project: payment.invoices.projects ? {
          id: Number(payment.invoices.projects.id),
          name: payment.invoices.projects.name
        } : null,
        client: payment.invoices.clients ? {
          id: Number(payment.invoices.clients.id),
          companyname: payment.invoices.clients.companyname
        } : null
      } : null
    }

    return NextResponse.json({
      success: true,
      payment: transformedPayment,
      message: 'Payment created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating payment:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create payment',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
