import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON>and<PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  generateSlug
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/legal-pages - Get all legal pages with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['title', 'slug', 'content'])

  // Build sort query with field name mapping
  const fieldMapping: { [key: string]: string } = {
    'updatedAt': 'updatedat',
    'createdAt': 'createdat',
    'lastModified': 'lastmodified',
    'modifiedBy': 'modifiedby',
    'displayOrder': 'displayorder',
    'isActive': 'isactive',
    'metaDescription': 'metadescription'
  }

  const mappedSortBy = fieldMapping[sortBy] || sortBy
  const sortQuery = buildSortQuery(mappedSortBy, sortOrder)

  // Get legal pages with pagination
  const [legalPages, total] = await Promise.all([
    prisma.legalpages.findMany({
      where: searchQuery,
      include: {
        legalpagesections: {
          orderBy: { displayorder: 'asc' }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.legalpages.count({ where: searchQuery })
  ])

  // Transform the data for frontend
  const transformedLegalPages = legalPages.map(page => transformFromDbFields.legalPage(page))

  return paginatedResponse(transformedLegalPages, page, limit, total)
})

// POST /api/admin/legal-pages - Create a new legal page
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.legalPage.create)
  const validatedData = await validate(request)

  // Generate slug if not provided
  if (!validatedData.slug) {
    validatedData.slug = generateSlug(validatedData.title)
  }

  // Transform data to database format
  const dbData = transformToDbFields.legalPage(validatedData)

  const legalPage = await prisma.legalpages.create({
    data: dbData,
    include: {
      legalpagesections: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  const transformedLegalPage = transformFromDbFields.legalPage(legalPage)
  return successResponse(transformedLegalPage, 'Legal page created successfully', 201)
})
