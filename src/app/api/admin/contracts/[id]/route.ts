import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/contracts/[id] - Get a specific contract
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const contract = await prisma.contracts.findUnique({
    where: { id },
    include: {
      client: true,
      project: {
        include: {
          services: true,
          technologies: true
        }
      },
      order: {
        include: {
          orderDetails: {
            include: {
              service: true,
              serviceOption: true
            }
          }
        }
      },
      invoices: {
        include: {
          items: true,
          payments: true
        }
      }
    }
  })

  if (!contract) {
    throw new ApiError('Contract not found', 404)
  }

  return successResponse(contract)
})

// PUT /api/admin/contracts/[id] - Update a contract
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.contract.update)
  const data = await validate(request)

  // Check if contract exists
  const existingContract = await prisma.contracts.findUnique({
    where: { id },
  })

  if (!existingContract) {
    throw new ApiError('Contract not found', 404)
  }

  // Check if client exists if being updated
  if (data.clientid && data.clientid !== existingContract.clientid) {
    const client = await prisma.clients.findUnique({
      where: { id: data.clientid },
    })

    if (!client) {
      throw new ApiError('Client not found', 400)
    }
  }

  // Check if project exists if being updated
  if (data.projectid && data.projectid !== existingContract.projectid) {
    const project = await prisma.projects.findUnique({
      where: { id: data.projectid },
    })

    if (!project) {
      throw new ApiError('Project not found', 400)
    }
  }

  // Check if order exists if being updated
  if (data.orderid && data.orderid !== existingContract.orderid) {
    const order = await prisma.orders.findUnique({
      where: { id: data.orderid },
    })

    if (!order) {
      throw new ApiError('Order not found', 400)
    }
  }

  const contract = await prisma.contracts.update({
    where: { id },
    data,
    include: {
      client: {
        select: {
          id: true,
          companyname: true,
          contactname: true,
          contactemail: true
        }
      },
      project: {
        select: {
          id: true,
          name: true,
          status: true
        }
      },
      order: {
        select: {
          id: true,
          orderNumber: true,
          status: true
        }
      }
    }
  })

  return successResponse(contract, 'Contract updated successfully')
})

// DELETE /api/admin/contracts/[id] - Delete a contract
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if contract exists
  const existingContract = await prisma.contracts.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          invoices: true
        }
      }
    }
  })

  if (!existingContract) {
    throw new ApiError('Contract not found', 404)
  }

  // Check if contract has related invoices
  if (existingContract._count.invoices > 0) {
    throw new ApiError('Cannot delete contract with related invoices. Please remove them first.', 400)
  }

  await prisma.contracts.delete({
    where: { id }
  })

  return successResponse(null, 'Contract deleted successfully')
})

// PATCH /api/admin/contracts/[id] - Partial update (e.g., status change)
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const body = await request.json()

  // Check if contract exists
  const existingContract = await prisma.contracts.findUnique({
    where: { id },
  })

  if (!existingContract) {
    throw new ApiError('Contract not found', 404)
  }

  // Only allow specific fields for PATCH
  const allowedFields = ['status', 'signedAt', 'expiresat']
  const updateData: any = {}

  for (const field of allowedFields) {
    if (body[field] !== undefined) {
      if (field === 'signedAt' || field === 'expiresat') {
        updateData[field] = body[field] ? new Date(body[field]) : null
      } else {
        updateData[field] = body[field]
      }
    }
  }

  if (Object.keys(updateData).length === 0) {
    throw new ApiError('No valid fields to update', 400)
  }

  const contract = await prisma.contracts.update({
    where: { id },
    data: updateData,
    include: {
      client: {
        select: {
          id: true,
          companyname: true,
          contactname: true,
          contactemail: true
        }
      },
      project: {
        select: {
          id: true,
          name: true,
          status: true
        }
      }
    }
  })

  return successResponse(contract, 'Contract updated successfully')
})
