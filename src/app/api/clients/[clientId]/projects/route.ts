import { NextRequest } from 'next/server'
import { getClientProjects, getProjectStatusOptions } from '@/lib/fetchers/project'
import { clientExists } from '@/lib/fetchers/client'
import {
  withErrorHandler,
  successResponse,
  paginatedResponse,
  ApiError,
  getQueryParams,
  getPaginationParams
} from '@/lib/api-utils'

interface RouteParams {
  params: Promise<{ clientId: string }>
}

// GET /api/clients/[clientId]/projects - Get projects for a specific client
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const { clientId } = await params

  if (!clientId || isNaN(Number(clientId))) {
    throw new ApiError('Invalid client ID', 400)
  }

  // Check if client exists
  const exists = await clientExists(clientId)
  if (!exists) {
    throw new ApiError('Client not found', 404)
  }

  try {
    const { page, limit, search, filter } = getQueryParams(request)
    const { skip, take } = getPaginationParams(page, limit)

    // Extract status filter
    const status = filter ? JSON.parse(filter).status : undefined

    const result = await getClientProjects(clientId, {
      page,
      limit: take,
      search,
      status,
    })

    return paginatedResponse(result.projects, page, take, result.total)
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('Error fetching client projects:', error)
    throw new ApiError('Failed to fetch client projects', 500)
  }
})

// OPTIONS /api/clients/[clientId]/projects - Get available options for projects
export const OPTIONS = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const { clientId } = await params

  if (!clientId || isNaN(Number(clientId))) {
    throw new ApiError('Invalid client ID', 400)
  }

  // Check if client exists
  const exists = await clientExists(clientId)
  if (!exists) {
    throw new ApiError('Client not found', 404)
  }

  try {
    const options = {
      statusOptions: getProjectStatusOptions(),
      sortOptions: [
        { value: 'name', label: 'Name' },
        { value: 'status', label: 'Status' },
        { value: 'projStartDate', label: 'Start Date' },
        { value: 'projCompletionDate', label: 'Completion Date' },
        { value: 'estimateCost', label: 'Estimated Cost' },
        { value: 'createdAt', label: 'Created Date' },
      ],
    }

    return successResponse(options)
  } catch (error) {
    console.error('Error fetching project options:', error)
    throw new ApiError('Failed to fetch project options', 500)
  }
})
