import { NextRequest } from 'next/server'
import { getClientInvoices, getInvoiceStatusOptions, getClientInvoiceStats } from '@/lib/fetchers/invoice'
import { clientExists } from '@/lib/fetchers/client'
import {
  withErrorHandler,
  successResponse,
  paginatedResponse,
  ApiError,
  getQueryParams,
  getPaginationParams
} from '@/lib/api-utils'

interface RouteParams {
  params: Promise<{ clientId: string }>
}

// GET /api/clients/[clientId]/invoices - Get invoices for a specific client
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const { clientId } = await params

  if (!clientId || isNaN(Number(clientId))) {
    throw new ApiError('Invalid client ID', 400)
  }

  // Check if client exists
  const exists = await clientExists(clientId)
  if (!exists) {
    throw new ApiError('Client not found', 404)
  }

  try {
    const url = new URL(request.url)
    const { page, limit, search, filter } = getQueryParams(request)
    const { skip, take } = getPaginationParams(page, limit)

    // Check if stats are requested
    const includeStats = url.searchParams.get('stats') === 'true'

    // Extract filters
    let status, dateFrom, dateTo
    if (filter) {
      const filters = JSON.parse(filter)
      status = filters.status
      dateFrom = filters.dateFrom
      dateTo = filters.dateTo
    }

    const result = await getClientInvoices(clientId, {
      page,
      limit: take,
      search,
      status,
      dateFrom,
      dateTo,
    })

    let responseData: any = {
      invoices: result.invoices,
      pagination: {
        page,
        limit: take,
        total: result.total,
        totalPages: Math.ceil(result.total / take),
      },
    }

    // Include stats if requested
    if (includeStats) {
      const stats = await getClientInvoiceStats(clientId)
      responseData.stats = stats
    }

    return successResponse(responseData)
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('Error fetching client invoices:', error)
    throw new ApiError('Failed to fetch client invoices', 500)
  }
})

// OPTIONS /api/clients/[clientId]/invoices - Get available options for invoices
export const OPTIONS = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const { clientId } = await params

  if (!clientId || isNaN(Number(clientId))) {
    throw new ApiError('Invalid client ID', 400)
  }

  // Check if client exists
  const exists = await clientExists(clientId)
  if (!exists) {
    throw new ApiError('Client not found', 404)
  }

  try {
    const options = {
      statusOptions: getInvoiceStatusOptions(),
      sortOptions: [
        { value: 'totalAmount', label: 'Total Amount' },
        { value: 'status', label: 'Status' },
        { value: 'dueDate', label: 'Due Date' },
        { value: 'createdAt', label: 'Created Date' },
        { value: 'paidAt', label: 'Paid Date' },
      ],
    }

    return successResponse(options)
  } catch (error) {
    console.error('Error fetching invoice options:', error)
    throw new ApiError('Failed to fetch invoice options', 500)
  }
})
