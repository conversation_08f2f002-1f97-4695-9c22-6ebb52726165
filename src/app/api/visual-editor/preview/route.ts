import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth-config'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const pagePath = searchParams.get('path') || '/'

    // Get the base URL for the current request
    const protocol = request.headers.get('x-forwarded-proto') || 'http'
    const host = request.headers.get('host') || 'localhost:3000'
    const baseUrl = `${protocol}://${host}`

    // Fetch the page content
    const pageUrl = `${baseUrl}${pagePath}`
    
    try {
      const response = await fetch(pageUrl, {
        headers: {
          'Accept': 'text/html',
          'User-Agent': 'Visual-Editor-Preview/1.0',
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch page: ${response.status}`)
      }

      let html = await response.text()

      // Process the HTML for the visual editor
      html = processHtmlForEditor(html, baseUrl)

      return new NextResponse(html, {
        headers: {
          'Content-Type': 'text/html',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        }
      })

    } catch (fetchError) {
      console.error('Error fetching page:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch page content' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error in preview API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function processHtmlForEditor(html: string, baseUrl: string): string {
  // Remove scripts that might interfere with the editor
  html = html.replace(/<script[^>]*src="[^"]*\/_next\/static\/chunks\/webpack[^"]*"[^>]*><\/script>/gi, '')
  html = html.replace(/<script[^>]*src="[^"]*\/_next\/static\/chunks\/main[^"]*"[^>]*><\/script>/gi, '')
  html = html.replace(/<script[^>]*>[\s\S]*?__webpack_require__[\s\S]*?<\/script>/gi, '')
  
  // Remove Next.js development scripts
  html = html.replace(/<script[^>]*>[\s\S]*?__nextjs[\s\S]*?<\/script>/gi, '')
  html = html.replace(/<script[^>]*>[\s\S]*?__NEXT_DATA__[\s\S]*?<\/script>/gi, '')
  
  // Remove React Fast Refresh scripts
  html = html.replace(/<script[^>]*>[\s\S]*?react-refresh[\s\S]*?<\/script>/gi, '')
  
  // Add visual editor styles and scripts
  const editorScript = `
    <script>
      (function() {
        let interactiveMode = false;
        
        // Listen for messages from parent window
        window.addEventListener('message', function(event) {
          if (event.data.type === 'UPDATE_INTERACTIVE_MODE') {
            interactiveMode = event.data.interactiveMode;
            updateInteractiveMode();
          }
        });
        
        function updateInteractiveMode() {
          const elements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div, a');
          elements.forEach(element => {
            if (interactiveMode) {
              element.style.cursor = 'pointer';
              element.addEventListener('mouseover', handleMouseOver);
              element.addEventListener('mouseout', handleMouseOut);
              element.addEventListener('click', handleClick);
            } else {
              element.style.cursor = '';
              element.style.outline = '';
              element.style.backgroundColor = '';
              element.removeEventListener('mouseover', handleMouseOver);
              element.removeEventListener('mouseout', handleMouseOut);
              element.removeEventListener('click', handleClick);
            }
          });
        }
        
        function handleMouseOver(e) {
          if (!interactiveMode) return;
          const element = e.target;
          if (element.textContent && element.textContent.trim().length > 0) {
            element.style.outline = '2px solid #3B82F6';
            element.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
          }
        }
        
        function handleMouseOut(e) {
          const element = e.target;
          element.style.outline = '';
          element.style.backgroundColor = '';
        }
        
        function handleClick(e) {
          if (!interactiveMode) return;
          
          const element = e.target;
          if (!element.textContent || element.textContent.trim().length === 0) return;
          
          e.preventDefault();
          e.stopPropagation();
          
          const rect = element.getBoundingClientRect();
          const elementData = {
            id: 'element-' + Date.now(),
            text: element.textContent.trim(),
            description: element.tagName.toLowerCase() + ' element',
            filePath: window.location.pathname,
            rect: {
              left: rect.left,
              top: rect.top,
              width: rect.width,
              height: rect.height
            },
            className: element.className,
            tagName: element.tagName,
            selector: generateSelector(element)
          };
          
          window.parent.postMessage({
            type: 'ELEMENT_SELECTED',
            element: elementData
          }, '*');
        }
        
        function generateSelector(element) {
          if (element.id) return '#' + element.id;
          
          let selector = element.tagName.toLowerCase();
          if (element.className) {
            const classes = element.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) {
              selector += '.' + classes.join('.');
            }
          }
          
          return selector;
        }
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', updateInteractiveMode);
        } else {
          updateInteractiveMode();
        }
      })();
    </script>
  `;
  
  // Insert the editor script before closing body tag
  html = html.replace('</body>', editorScript + '</body>');
  
  // Fix relative URLs to absolute URLs
  html = html.replace(/href="\/([^"]*)"/g, `href="${baseUrl}/$1"`);
  html = html.replace(/src="\/([^"]*)"/g, `src="${baseUrl}/$1"`);
  
  return html;
}
