import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth-config'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const pagePath = searchParams.get('pagePath') || '/'

    // Get all static content
    const allContent = await prisma.staticcontent.findMany({
      where: {
        isactive: true
      },
      orderBy: { displayorder: 'asc' }
    })

    // Get content for specific page
    const pageContent = await prisma.staticcontent.findMany({
      where: {
        page: pagePath.replace('/', '') || 'home',
        isactive: true
      },
      orderBy: { displayorder: 'asc' }
    })

    return NextResponse.json({
      success: true,
      data: {
        allContent,
        pageContent,
        pagePath
      }
    })

  } catch (error) {
    console.error('Error in debug endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 