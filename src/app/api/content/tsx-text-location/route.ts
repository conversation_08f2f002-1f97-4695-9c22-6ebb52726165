import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth-config'
import { TsxTextLocator } from '@/lib/tsx-text-locator'

// GET /api/content/tsx-text-location - Enhanced text location detection for TSX files
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const text = searchParams.get('text')
    const pagePath = searchParams.get('pagePath') || '/'

    if (!text) {
      return NextResponse.json(
        { error: 'Missing required parameter: text' },
        { status: 400 }
      )
    }

    console.log('Enhanced TSX text location search:', { text, pagePath })

    // Clean the pagePath to avoid double paths
    const cleanPagePath = pagePath.replace('src/app', '').replace('/page.tsx', '')
    const normalizedPath = cleanPagePath === '/' || cleanPagePath === '' ? '' : cleanPagePath

    // Define potential TSX files based on the page path (using actual existing files)
    const potentialFiles = [
      `src/app${normalizedPath}/page.tsx`,
      `src/app${normalizedPath}/layout.tsx`,
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/home/<USER>
      `src/components/header.tsx`,
      `src/components/footer.tsx`,
      `src/components/services/service-card.tsx`,
      `src/components/services/services-grid.tsx`,
      `src/components/projects/project-card.tsx`,
      `src/components/projects/project-grid.tsx`,
      `src/components/technologies/technology-card.tsx`
    ]

    // Use enhanced TSX text locator
    const locations = await TsxTextLocator.searchTextInTsxFiles(text, potentialFiles)

    if (locations.length === 0) {
      return NextResponse.json(
        { 
          success: false,
          error: `Text "${text}" not found in any TSX files for page ${pagePath}`,
          searchedFiles: potentialFiles.length
        },
        { status: 404 }
      )
    }

    // Format the results for the frontend
    const formattedLocations = locations.map(location => ({
      filePath: location.filePath,
      lineNumber: location.lineNumber,
      columnStart: location.columnStart,
      columnEnd: location.columnEnd,
      fullLine: location.fullLine.trim(),
      context: location.context,
      confidence: location.confidence,
      locationDescription: `${location.filePath} (line ${location.lineNumber}, column ${location.columnStart}) - ${location.context} context`
    }))

    // Group by confidence levels
    const highConfidence = formattedLocations.filter(loc => loc.confidence >= 70)
    const mediumConfidence = formattedLocations.filter(loc => loc.confidence >= 50 && loc.confidence < 70)
    const lowConfidence = formattedLocations.filter(loc => loc.confidence < 50)

    console.log(`Found ${locations.length} locations:`)
    console.log(`- High confidence (≥70%): ${highConfidence.length}`)
    console.log(`- Medium confidence (50-69%): ${mediumConfidence.length}`)
    console.log(`- Low confidence (<50%): ${lowConfidence.length}`)

    return NextResponse.json({
      success: true,
      data: {
        text: text,
        totalOccurrences: locations.length,
        locations: formattedLocations,
        bestMatch: formattedLocations[0], // Highest confidence match
        confidence: {
          high: highConfidence,
          medium: mediumConfidence,
          low: lowConfidence
        },
        searchedFiles: potentialFiles.length
      }
    })

  } catch (error) {
    console.error('Error in TSX text location API:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST /api/content/tsx-text-location - Update text at specific TSX location
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { filePath, lineNumber, columnStart, columnEnd, oldText, newText } = body

    if (!filePath || !lineNumber || !oldText || newText === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: filePath, lineNumber, oldText, newText' },
        { status: 400 }
      )
    }

    console.log('TSX text update request:', {
      filePath,
      lineNumber,
      columnStart,
      columnEnd,
      oldText: oldText.substring(0, 50) + (oldText.length > 50 ? '...' : ''),
      newText: newText.substring(0, 50) + (newText.length > 50 ? '...' : '')
    })

    // Verify the text location before updating
    const locations = await TsxTextLocator.findTextInTsxFile(filePath, oldText)
    const exactMatch = locations.find(loc => 
      loc.lineNumber === lineNumber && 
      loc.columnStart === columnStart &&
      loc.textContent === oldText
    )

    if (!exactMatch) {
      return NextResponse.json(
        { 
          error: 'Text location verification failed. The text may have been modified since detection.',
          details: {
            expectedLine: lineNumber,
            expectedColumn: columnStart,
            expectedText: oldText,
            foundLocations: locations.length
          }
        },
        { status: 400 }
      )
    }

    // Use the existing TSX edit API for the actual update
    const editResponse = await fetch(`${request.nextUrl.origin}/api/content/tsx-edit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': request.headers.get('Cookie') || ''
      },
      body: JSON.stringify({
        filePath,
        oldText,
        newText,
        startLine: lineNumber,
        endLine: lineNumber,
        startColumn: columnStart,
        endColumn: columnEnd
      })
    })

    if (!editResponse.ok) {
      const errorData = await editResponse.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${editResponse.status}`)
    }

    const result = await editResponse.json()

    return NextResponse.json({
      success: true,
      message: 'Text updated successfully with enhanced location detection',
      data: {
        filePath,
        lineNumber,
        columnStart,
        columnEnd,
        oldText,
        newText,
        confidence: exactMatch.confidence,
        context: exactMatch.context,
        user: session.user.email,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Error updating TSX text:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to update text',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
