import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth-config'
import { ContentManager } from '../content-manager'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const pagePath = searchParams.get('pagePath')

    console.log('Page content API called with pagePath:', pagePath)

    if (!pagePath) {
      return NextResponse.json(
        { error: 'Missing required parameter: pagePath' },
        { status: 400 }
      )
    }

    // Extract just the path part if it's a full URL
    let cleanPath = pagePath
    try {
      const url = new URL(pagePath)
      cleanPath = url.pathname
    } catch {
      // If it's not a valid URL, assume it's already a path
      cleanPath = pagePath
    }

    console.log('Cleaned path:', cleanPath)

    // Get the editable content for the page
    const content = await ContentManager.getPageContent(cleanPath)
    const contentMap = await ContentManager.getContentForIframe(cleanPath)

    console.log('Returning content for path:', cleanPath, 'Content fields:', content.length)

    return NextResponse.json({
      success: true,
      data: {
        content,
        contentMap,
        pagePath: cleanPath
      }
    })

  } catch (error) {
    console.error('Error getting page content:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 