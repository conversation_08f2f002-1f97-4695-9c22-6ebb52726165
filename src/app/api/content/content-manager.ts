import { prisma } from '@/lib/prisma'
import fs from 'fs/promises'
import path from 'path'
import { FileUpdater } from '@/lib/file-updater'

export interface ContentField {
  id: string
  pagePath: string
  fieldType: 'title' | 'subtitle' | 'description' | 'content' | 'buttonText'
  fieldKey: string // e.g., 'hero-title', 'services-description'
  currentValue: string
  lineNumber: number
  filePath: string
  isEditable: boolean
}

export interface ContentUpdate {
  fieldId: string
  newValue: string
  userId: string
  pagePath: string
  oldText?: string
  elementInfo?: {
    tagName?: string
    className?: string
    selector?: string
  }
}

export class ContentManager {
  // Map of page paths to their content structure
  private static pageContentMap: Record<string, ContentField[]> = {
    '/': [
      {
        id: 'hero-title',
        pagePath: '/',
        fieldType: 'title',
        fieldKey: 'hero-title',
        currentValue: 'Transform Your Business with Technology',
        lineNumber: 15,
        filePath: 'src/app/page.tsx',
        isEditable: true
      },
      {
        id: 'hero-subtitle',
        pagePath: '/',
        fieldType: 'subtitle',
        fieldKey: 'hero-subtitle',
        currentValue: 'We help businesses leverage cutting-edge technology to achieve their goals',
        lineNumber: 16,
        filePath: 'src/app/page.tsx',
        isEditable: true
      },
      {
        id: 'hero-description',
        pagePath: '/',
        fieldType: 'description',
        fieldKey: 'hero-description',
        currentValue: 'From web development to AI solutions, we provide comprehensive technology services',
        lineNumber: 17,
        filePath: 'src/app/page.tsx',
        isEditable: true
      }
    ],
    '/services': [
      {
        id: 'services-title',
        pagePath: '/services',
        fieldType: 'title',
        fieldKey: 'services-title',
        currentValue: 'Our Services',
        lineNumber: 12,
        filePath: 'src/app/services/page.tsx',
        isEditable: true
      },
      {
        id: 'services-subtitle',
        pagePath: '/services',
        fieldType: 'subtitle',
        fieldKey: 'services-subtitle',
        currentValue: 'Comprehensive technology solutions for your business',
        lineNumber: 13,
        filePath: 'src/app/services/page.tsx',
        isEditable: true
      }
    ],
    '/about': [
      {
        id: 'about-title',
        pagePath: '/about',
        fieldType: 'title',
        fieldKey: 'about-title',
        currentValue: 'About Us',
        lineNumber: 10,
        filePath: 'src/app/about/page.tsx',
        isEditable: true
      },
      {
        id: 'about-description',
        pagePath: '/about',
        fieldType: 'description',
        fieldKey: 'about-description',
        currentValue: 'We are a team of passionate technologists dedicated to helping businesses succeed',
        lineNumber: 11,
        filePath: 'src/app/about/page.tsx',
        isEditable: true
      }
    ]
  }

  static async getPageContent(pagePath: string): Promise<ContentField[]> {
    // First, try to get content from database
    const dbContent = await prisma.staticcontent.findMany({
      where: {
        page: pagePath.replace('/', '') || 'home',
        isactive: true
      },
      orderBy: { displayorder: 'asc' }
    })

    // Map database content to ContentField structure
    const contentFields: ContentField[] = []
    
    for (const content of dbContent) {
      const fieldId = `${content.section}-${content.contentkey}`
      const fieldType = this.getFieldType(content.contentkey)
      
      contentFields.push({
        id: fieldId,
        pagePath: pagePath,
        fieldType,
        fieldKey: fieldId,
        currentValue: content.content,
        lineNumber: content.displayorder, // Use display order as line number for now
        filePath: `src/app${pagePath}/page.tsx`,
        isEditable: true
      })
    }

    // If no database content, fall back to static mapping
    if (contentFields.length === 0) {
      return this.pageContentMap[pagePath] || []
    }

    return contentFields
  }

  private static getFieldType(contentKey: string): ContentField['fieldType'] {
    if (contentKey.includes('title')) return 'title'
    if (contentKey.includes('subtitle')) return 'subtitle'
    if (contentKey.includes('description')) return 'description'
    if (contentKey.includes('button')) return 'buttonText'
    return 'content'
  }

  static async updateContent(update: ContentUpdate): Promise<{ success: boolean; message: string }> {
    try {
      // First, update the database
      const [section, contentKey] = update.fieldId.split('-')
      
      await prisma.staticcontent.upsert({
        where: {
          page_section_contentkey: {
            page: update.pagePath.replace('/', '') || 'home',
            section,
            contentkey: contentKey
          }
        },
        update: {
          content: update.newValue,
          updatedat: new Date()
        },
        create: {
          page: update.pagePath.replace('/', '') || 'home',
          section,
          contentkey: contentKey,
          content: update.newValue,
          contenttype: 'text',
          displayorder: 0,
          isactive: true
        }
      })

      // Get the old text from the request or database
      const oldText = update.oldText || ''
      
      // Update the source file
      if (update.fieldId.startsWith('temp-')) {
        // For temporary IDs, try to update the source file by finding the text
        console.log('Temporary field ID detected, attempting to update source file by text search')
        
        if (oldText && update.newValue) {
          const filePath = `src/app${update.pagePath}/page.tsx`
          
          console.log(`Attempting to update source file: ${filePath}`)
          console.log(`Searching for: "${oldText}" -> "${update.newValue}"`)
          
          try {
            const fileUpdateResult = await FileUpdater.updateLine({
              filePath,
              lineNumber: 1, // Start from line 1, FileUpdater will search nearby lines
              oldText,
              newText: update.newValue
            })
            
            if (fileUpdateResult.success) {
              console.log('Source file updated successfully:', fileUpdateResult.message)
            } else {
              console.log('Warning: Could not update source file:', fileUpdateResult.message)
            }
          } catch (fileError) {
            console.log('Warning: Error updating source file:', fileError)
          }
        }
      } else {
        // For structured content, update the source file
        const currentContent = await prisma.staticcontent.findFirst({
          where: {
            page: update.pagePath.replace('/', '') || 'home',
            section,
            contentkey: contentKey
          }
        })
        
        const filePath = `src/app${update.pagePath}/page.tsx`
        const lineNumber = currentContent?.displayorder || 1
        
        console.log(`Attempting to update source file: ${filePath} at line ${lineNumber}`)
        
        try {
          const fileUpdateResult = await FileUpdater.updateLine({
            filePath,
            lineNumber,
            oldText: oldText || currentContent?.content || '',
            newText: update.newValue
          })
          
          if (fileUpdateResult.success) {
            console.log('Source file updated successfully:', fileUpdateResult.message)
          } else {
            console.log('Warning: Could not update source file:', fileUpdateResult.message)
          }
        } catch (fileError) {
          console.log('Warning: Error updating source file:', fileError)
        }
      }

      // Log the change
      await prisma.contentchanges.create({
        data: {
          pagepath: update.pagePath,
          elementid: update.fieldId,
          oldtext: oldText,
          newtext: update.newValue,
          filepath: `src/app${update.pagePath}/page.tsx`,
          userid: update.userId,
          status: 'published'
        }
      })

      return {
        success: true,
        message: 'Content updated successfully'
      }

    } catch (error) {
      console.error('Error updating content:', error)
      return {
        success: false,
        message: `Failed to update content: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  static async getContentForIframe(pagePath: string): Promise<Record<string, string>> {
    const content = await this.getPageContent(pagePath)
    const contentMap: Record<string, string> = {}
    
    content.forEach(field => {
      contentMap[field.id] = field.currentValue
    })
    
    return contentMap
  }

  static async getEditableElements(pagePath: string): Promise<ContentField[]> {
    return this.getPageContent(pagePath)
  }
} 