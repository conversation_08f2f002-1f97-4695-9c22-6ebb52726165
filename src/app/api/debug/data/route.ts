import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Get all categories with their services
    const categories = await prisma.categories.findMany({
      include: {
        services: {
          include: {
            serviceoptions: {
              include: {
                serviceoptionfeatures: true
              }
            }
          }
        }
      }
    })

    // Get raw counts
    const counts = {
      categories: await prisma.categories.count(),
      services: await prisma.services.count(),
      serviceoptions: await prisma.serviceoptions.count(),
      serviceoptionfeatures: await prisma.serviceoptionfeatures.count()
    }

    // Get some sample data
    const sampleServices = await prisma.services.findMany({
      take: 5,
      include: {
        categories: true
      }
    })

    return Response.json({
      counts,
      categories: categories.map(cat => ({
        id: String(cat.id),
        name: cat.categname,
        servicesCount: cat.services.length,
        services: cat.services.map(service => ({
          id: String(service.id),
          name: service.name,
          categid: String(service.categid),
          optionsCount: service.serviceoptions.length
        }))
      })),
      sampleServices: sampleServices.map(service => ({
        id: String(service.id),
        name: service.name,
        categid: String(service.categid),
        categoryName: service.categories?.categname
      }))
    })
  } catch (error) {
    console.error('Debug API error:', error)
    return Response.json({ error: 'Failed to fetch debug data' }, { status: 500 })
  }
}
