import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: invoiceId } = await params
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    
    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      invoiceid: parseInt(invoiceId)
    }

    if (search) {
      where.OR = [
        { paymentMethod: { contains: search, mode: 'insensitive' } },
        { status: { contains: search, mode: 'insensitive' } },
        { notes: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (status) {
      where.status = status
    }

    // Get payments with related data
    const [payments, total] = await Promise.all([
      prisma.payments.findMany({
        where,
        include: {
          invoice: {
            select: {
              id: true,
              totalAmount: true,
              status: true,
              dueDate: true,
              description: true,
              project: {
                select: {
                  id: true,
                  name: true
                }
              },
              client: {
                select: {
                  id: true,
                  companyName: true
                }
              }
            }
          }
        },
        orderBy: { paymentDate: 'desc' },
        skip,
        take: limit
      }),
      prisma.payments.count({ where })
    ])

    // Transform the data to match expected format
    const transformedPayments = payments.map(payment => ({
      id: payment.id,
      amount: payment.amount,
      paymentDate: payment.paymentDate.toISOString(),
      paymentMethod: payment.paymentMethod,
      status: payment.status,
      notes: payment.notes,
      invoiceId: payment.invoiceid,
      createdAt: payment.createdAt.toISOString(),
      updatedAt: payment.updatedAt?.toISOString(),
      invoice: payment.invoice ? {
        id: payment.invoice.id,
        totalAmount: payment.invoice.totalAmount,
        status: payment.invoice.status,
        dueDate: payment.invoice.dueDate.toISOString(),
        description: payment.invoice.description,
        project: payment.invoice.project,
        client: payment.invoice.client
      } : null
    }))

    return NextResponse.json({
      success: true,
      data: transformedPayments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching invoice payments:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch payments',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const invoiceId = parseInt(id)
    const body = await request.json()

    // Validate required fields
    const requiredFields = ['amount', 'paymentDate', 'paymentMethod', 'status']
    for (const field of requiredFields) {
      if (!(field in body)) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Verify invoice exists
    const invoice = await prisma.invoices.findUnique({
      where: { id: invoiceId },
      select: { id: true }
    })

    if (!invoice) {
      return NextResponse.json(
        { success: false, error: 'Invoice not found' },
        { status: 404 }
      )
    }

    // Create the payment
    const payment = await prisma.payments.create({
      data: {
        invoiceid: invoiceId,
        amount: body.amount,
        paymentDate: new Date(body.paymentDate),
        paymentMethod: body.paymentMethod,
        status: body.status,
        notes: body.notes
      },
      include: {
        invoice: {
          select: {
            id: true,
            totalAmount: true,
            status: true,
            dueDate: true,
            description: true,
            project: {
              select: {
                id: true,
                name: true
              }
            },
            client: {
              select: {
                id: true,
                companyName: true
              }
            }
          }
        }
      }
    })

    // Transform the response
    const transformedPayment = {
      id: payment.id,
      amount: payment.amount,
      paymentDate: payment.paymentDate.toISOString(),
      paymentMethod: payment.paymentMethod,
      status: payment.status,
      notes: payment.notes,
      invoiceId: payment.invoiceid,
      createdAt: payment.createdAt.toISOString(),
      updatedAt: payment.updatedAt?.toISOString(),
      invoice: payment.invoice ? {
        id: payment.invoice.id,
        totalAmount: payment.invoice.totalAmount,
        status: payment.invoice.status,
        dueDate: payment.invoice.dueDate.toISOString(),
        description: payment.invoice.description,
        project: payment.invoice.project,
        client: payment.invoice.client
      } : null
    }

    return NextResponse.json({
      success: true,
      data: transformedPayment
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating payment:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create payment',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
