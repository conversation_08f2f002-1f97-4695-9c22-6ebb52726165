import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'

// Check if Stripe is properly configured
const stripeSecretKey = process.env.STRIPE_SECRET_KEY
const isStripeConfigured = stripeSecretKey && !stripeSecretKey.includes('...')

let stripe: Stripe | null = null
if (isStripeConfigured) {
  stripe = new Stripe(stripeSecretKey!, {
    apiVersion: '2024-06-20',
  })
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { amount, currency, metadata } = body

    console.log('Creating payment intent with:', { amount, currency, metadata })
    console.log('Stripe configured:', isStripeConfigured)

    // Check if Stripe is configured
    if (!isStripeConfigured) {
      console.log('Stripe not configured - returning mock response for development')
      return NextResponse.json({
        clientSecret: 'pi_mock_client_secret_for_development',
        paymentIntentId: 'pi_mock_payment_intent_id',
        mock: true,
        message: 'This is a mock response. Configure Stripe keys for real payments.'
      })
    }

    // Validate required fields
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Invalid amount' },
        { status: 400 }
      )
    }

    // Stripe minimum amount validation (50 cents for USD)
    const minimumAmount = currency?.toLowerCase() === 'usd' ? 0.50 : 0.50 // Adjust for other currencies as needed
    if (amount < minimumAmount) {
      return NextResponse.json(
        {
          error: `Amount must be at least $${minimumAmount.toFixed(2)} ${currency?.toUpperCase() || 'USD'}`,
          minimumAmount,
          providedAmount: amount
        },
        { status: 400 }
      )
    }

    if (!currency) {
      return NextResponse.json(
        { error: 'Currency is required' },
        { status: 400 }
      )
    }

    // Create a PaymentIntent with the order amount and currency
    const paymentIntent = await stripe!.paymentIntents.create({
      amount: Math.round(amount * 100), // Stripe expects amount in cents
      currency: currency.toLowerCase(),
      metadata: metadata || {},
      automatic_payment_methods: {
        enabled: true,
      },
    })

    console.log('Payment intent created successfully:', paymentIntent.id)

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
    })
  } catch (error) {
    console.error('Error creating payment intent:', error)
    console.error('Error details:', error instanceof Error ? error.message : error)

    return NextResponse.json(
      {
        error: 'Failed to create payment intent',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
