import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { transformFromDbFields } from '@/lib/data-transform'
import { z } from 'zod'
import { getAuthenticatedUser } from '@/lib/chat-access-control'

interface RouteParams {
  params: { messageId: string }
}

// Status update schema
const updateStatusSchema = z.object({
  isread: z.boolean().optional(),
  isdelivered: z.boolean().optional(),
  status: z.enum(['New', 'In Progress', 'Resolved', 'Closed']).optional(),
})

// PATCH /api/chat/messages/[messageId]/status - Update message status (client access)
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const resolvedParams = await params
  const messageId = parseInt(resolvedParams.messageId)
  if (isNaN(messageId)) {
    throw new ApiError('Invalid message ID', 400)
  }

  // Get authenticated user
  const user = await getAuthenticatedUser(request)

  // Validate the status data
  const validate = validateRequest(updateStatusSchema)
  const data = await validate(request)

  // Check if message exists and user has access
  const existingMessage = await prisma.contactforms.findUnique({
    where: { id: BigInt(messageId) },
    include: {
      sender: true,
      receiver: true
    }
  })

  if (!existingMessage) {
    throw new ApiError('Message not found', 404)
  }

  // Check if user has permission to update this message
  const userId = user.id.toString()
  const senderId = existingMessage.senderid?.toString()
  const receiverId = existingMessage.receiverid?.toString()
  
  // Users can only update messages they sent or received, admins can update any message
  if (user.role !== 'ADMIN' && userId !== senderId && userId !== receiverId) {
    throw new ApiError('Access denied to update this message', 403)
  }

  // Prepare update data
  const updateData: any = {}
  
  if (data.isread !== undefined) {
    updateData.isread = data.isread
    if (data.isread) {
      updateData.readat = new Date()
    }
  }

  if (data.isdelivered !== undefined) {
    updateData.isdelivered = data.isdelivered
    if (data.isdelivered) {
      updateData.deliveredat = new Date()
    }
  }

  if (data.status !== undefined) {
    updateData.status = data.status
  }

  updateData.updatedat = new Date()

  // Update the message
  const updatedMessage = await prisma.contactforms.update({
    where: { id: BigInt(messageId) },
    data: updateData,
    include: {
      sender: {
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          imageurl: true,
          role: true
        }
      },
      receiver: {
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          imageurl: true,
          role: true
        }
      },
      parent: {
        select: {
          id: true,
          subject: true,
          message: true,
          createdat: true
        }
      }
    }
  })

  // Transform the response
  const transformedMessage = {
    ...transformFromDbFields.contactForm(updatedMessage),
    sender: updatedMessage.sender,
    receiver: updatedMessage.receiver,
    parent: updatedMessage.parent ? transformFromDbFields.contactForm(updatedMessage.parent) : null,
    attachments: updatedMessage.attachments ? JSON.parse(updatedMessage.attachments) : []
  }

  return successResponse({
    message: transformedMessage
  })
})

// GET /api/chat/messages/[messageId]/status - Get message status (client access)
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const resolvedParams = await params
  const messageId = parseInt(resolvedParams.messageId)
  if (isNaN(messageId)) {
    throw new ApiError('Invalid message ID', 400)
  }

  // Get authenticated user
  const user = await getAuthenticatedUser(request)

  // Check if message exists and user has access
  const message = await prisma.contactforms.findUnique({
    where: { id: BigInt(messageId) },
    include: {
      sender: {
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          imageurl: true,
          role: true
        }
      },
      receiver: {
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          imageurl: true,
          role: true
        }
      }
    }
  })

  if (!message) {
    throw new ApiError('Message not found', 404)
  }

  // Check if user has permission to view this message
  const userId = user.id.toString()
  const senderId = message.senderid?.toString()
  const receiverId = message.receiverid?.toString()
  
  // Users can only view messages they sent or received, admins can view any message
  if (user.role !== 'ADMIN' && userId !== senderId && userId !== receiverId) {
    throw new ApiError('Access denied to view this message', 403)
  }

  // Transform the response
  const transformedMessage = {
    ...transformFromDbFields.contactForm(message),
    sender: message.sender,
    receiver: message.receiver,
    attachments: message.attachments ? JSON.parse(message.attachments) : []
  }

  return successResponse({
    message: transformedMessage
  })
})
