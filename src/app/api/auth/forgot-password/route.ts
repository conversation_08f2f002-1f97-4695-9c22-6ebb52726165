import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { AuditLogger } from '@/lib/audit-log'
import crypto from 'crypto'

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address').transform(val => val.toLowerCase().trim()),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const { email } = forgotPasswordSchema.parse(body)
    
    // Check if user exists
    const user = await prisma.users.findUnique({
      where: { email }
    })
    
    // Always return success for security reasons (don't reveal if email exists)
    if (!user) {
      // Log the attempt but still return success
      await AuditLogger.logAuth(
        'PASSWORD_RESET_REQUESTED',
        'unknown',
        { email, found: false }
      )
      
      return NextResponse.json(
        { message: 'If an account with that email exists, you will receive password reset instructions.' },
        { status: 200 }
      )
    }
    
    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex')
    const resetTokenExpiry = new Date(Date.now() + 3600000) // 1 hour from now
    
    // Store reset token in database (you might want to create a password_resets table)
    // For now, we'll just log it - in production you'd send an email
    await AuditLogger.logAuth(
      'PASSWORD_RESET_REQUESTED',
      user.id.toString(),
      { 
        email, 
        resetToken, // In production, don't log the actual token
        expiresAt: resetTokenExpiry.toISOString()
      }
    )
    
    // TODO: Send email with reset link
    // const resetLink = `${process.env.NEXTAUTH_URL}/client-auth/reset-password?token=${resetToken}`
    // await sendPasswordResetEmail(email, resetLink)
    
    console.log(`Password reset requested for ${email}. Reset token: ${resetToken}`)
    console.log(`Reset link would be: ${process.env.NEXTAUTH_URL}/client-auth/reset-password?token=${resetToken}`)
    
    return NextResponse.json(
      { message: 'If an account with that email exists, you will receive password reset instructions.' },
      { status: 200 }
    )
    
  } catch (error) {
    console.error('Forgot password error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid email address' },
        { status: 400 }
      )
    }
    
    // Log failed attempt
    try {
      await AuditLogger.logAuth(
        'PASSWORD_RESET_FAILED',
        'unknown',
        { error: error instanceof Error ? error.message : 'Unknown error' }
      )
    } catch (logError) {
      console.error('Failed to log password reset error:', logError)
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
