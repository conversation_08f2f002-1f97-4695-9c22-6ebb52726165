import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse, 
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  validateMethod,
  requireAdmin
} from '@/lib/api-utils'
import { createTechnologySchema, updateTechnologySchema } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/technologies - List all technologies with pagination and search
export const GET = withError<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}
  
  // Add search functionality
  if (search) {
    Object.assign(where, buildSearchQuery(search, ['name', 'description']))
  }
  
  // Add filter for active/inactive technologies
  if (filter === 'active') {
    where.isactive = true
  } else if (filter === 'inactive') {
    where.isactive = false
  }

  // Get total count for pagination
  const total = await prisma.technologies.count({ where })

  // Get technologies with pagination
  const technologies = await prisma.technologies.findMany({
    where,
    include: {
      projecttechnologies: {
        include: {
          projects: {
            select: {
              id: true,
              name: true,
              status: true,
              clients: {
                select: {
                  id: true,
                  companyname: true,
                },
              },
            },
          },
        },
        take: 5, // Limit to recent projects
      },
      _count: {
        select: {
          projecttechnologies: true,
        },
      },
    },
    orderBy: buildSortQuery(sortBy, sortOrder),
    skip,
    take,
  })

  // Transform the data for frontend
  const transformedTechnologies = technologies.map(tech => transformFromDbFields.technology(tech))

  return paginatedResponse(transformedTechnologies, page, limit, total)
})

// POST /api/technologies - Create a new technology
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['POST'])
  
  const validate = validateRequest(createTechnologySchema)
  const data = await validate(request)

  // Check if a technology with the same name already exists
  const existingTechnology = await prisma.technologies.findFirst({
    where: {
      name: data.name,
    },
  })

  if (existingTechnology) {
    throw new Error('A technology with this name already exists')
  }

  const technology = await prisma.technologies.create({
    data,
    include: {
      _count: {
        select: {
          projects: true,
        },
      },
    },
  })

  return successResponse(technology, 'Technology created successfully', 201)
})

// PUT /api/technologies - Bulk update technologies (admin only)
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['PUT'])
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid technology IDs provided')
  }

  const validate = validateRequest(updateTechnologySchema)
  const updateData = await validate({ json: () => data } as NextRequest)

  const updatedTechnologies = await prisma.technologies.updateMany({
    where: {
      id: {
        in: ids,
      },
    },
    data: updateData,
  })

  return successResponse(
    { count: updatedTechnologies.count },
    `${updatedTechnologies.count} technologies updated successfully`
  )
})

// DELETE /api/technologies - Bulk delete technologies (admin only)
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['DELETE'])
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid technology IDs provided')
  }

  // Check if any technologies are being used in projects
  const technologiesInUse = await prisma.technologies.findMany({
    where: {
      id: { in: ids },
      projects: { some: {} },
    },
    select: { id: true, name: true },
  })

  if (technologiesInUse.length > 0) {
    const technologyNames = technologiesInUse.map(t => t.name).join(', ')
    throw new Error(
      `Cannot delete technologies that are in use: ${technologyNames}. Please remove them from projects first.`
    )
  }

  const deletedTechnologies = await prisma.technologies.deleteMany({
    where: {
      id: {
        in: ids,
      },
    },
  })

  return successResponse(
    { count: deletedTechnologies.count },
    `${deletedTechnologies.count} technologies deleted successfully`
  )
})
