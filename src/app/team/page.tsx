'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import {
  EnvelopeIcon,
  MapPinIcon,
  AcademicCapIcon,
  BriefcaseIcon,
  HeartIcon,
  CodeBracketIcon,
  DevicePhoneMobileIcon,
  CloudIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';

const teamMembers = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'CEO & Co-Founder',
    department: 'Leadership',
    bio: 'Visionary leader with 15+ years in tech. <PERSON> founded Technoloway with a mission to democratize cutting-edge software development.',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face',
    location: 'San Francisco, CA',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/sarah<PERSON><PERSON><PERSON>',
    twitter: 'https://twitter.com/sarah<PERSON><PERSON><PERSON>',
    github: 'https://github.com/sarahjo<PERSON>son',
    skills: ['Strategic Planning', 'Team Leadership', 'Product Vision', 'Business Development'],
    education: 'MBA Stanford, BS Computer Science MIT',
    experience: '15+ years',
    languages: ['English', 'Spanish', 'French'],
    interests: ['AI Ethics', 'Sustainable Tech', 'Mentoring'],
    icon: BriefcaseIcon,
    featured: true,
  },
  {
    id: 2,
    name: 'Mike Chen',
    role: 'CTO & Co-Founder',
    department: 'Engineering',
    bio: 'Full-stack architect passionate about scalable systems. Mike leads our technical vision and ensures we stay at the forefront of technology.',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
    location: 'San Francisco, CA',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/mikechen',
    twitter: 'https://twitter.com/mikechen',
    github: 'https://github.com/mikechen',
    skills: ['System Architecture', 'Cloud Computing', 'DevOps', 'Team Management'],
    education: 'MS Computer Science Stanford, BS Engineering UC Berkeley',
    experience: '12+ years',
    languages: ['English', 'Mandarin', 'Japanese'],
    interests: ['Open Source', 'Cloud Architecture', 'Photography'],
    icon: CloudIcon,
    featured: true,
  },
  {
    id: 3,
    name: 'Emily Davis',
    role: 'Lead Frontend Developer',
    department: 'Engineering',
    bio: 'UI/UX enthusiast who crafts beautiful, accessible user experiences. Emily ensures our applications are both functional and delightful.',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
    location: 'Austin, TX',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/emilydavis',
    twitter: 'https://twitter.com/emilydavis',
    github: 'https://github.com/emilydavis',
    skills: ['React', 'TypeScript', 'UI/UX Design', 'Accessibility'],
    education: 'BS Computer Science UT Austin, UX Design Certification',
    experience: '8+ years',
    languages: ['English', 'German'],
    interests: ['Design Systems', 'Accessibility', 'Digital Art'],
    icon: CodeBracketIcon,
    featured: false,
  },
  {
    id: 4,
    name: 'David Rodriguez',
    role: 'Senior Backend Developer',
    department: 'Engineering',
    bio: 'Database wizard and API architect. David builds the robust backend systems that power our applications.',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
    location: 'Miami, FL',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/davidrodriguez',
    twitter: 'https://twitter.com/davidrodriguez',
    github: 'https://github.com/davidrodriguez',
    skills: ['Node.js', 'Python', 'PostgreSQL', 'Microservices'],
    education: 'MS Software Engineering, BS Computer Science FIU',
    experience: '10+ years',
    languages: ['English', 'Spanish', 'Portuguese'],
    interests: ['Database Optimization', 'Distributed Systems', 'Soccer'],
    icon: CogIcon,
    featured: false,
  },
  {
    id: 5,
    name: 'Lisa Wang',
    role: 'Mobile Development Lead',
    department: 'Engineering',
    bio: 'Cross-platform mobile expert who creates seamless experiences across iOS and Android. Lisa leads our mobile innovation.',
    image: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop&crop=face',
    location: 'Seattle, WA',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/lisawang',
    twitter: 'https://twitter.com/lisawang',
    github: 'https://github.com/lisawang',
    skills: ['React Native', 'Flutter', 'iOS', 'Android'],
    education: 'MS Mobile Computing, BS Computer Science UW',
    experience: '9+ years',
    languages: ['English', 'Mandarin', 'Korean'],
    interests: ['Mobile UX', 'AR/VR', 'Travel'],
    icon: DevicePhoneMobileIcon,
    featured: false,
  },
  {
    id: 6,
    name: 'Alex Thompson',
    role: 'Security Engineer',
    department: 'Security',
    bio: 'Cybersecurity expert who keeps our systems and client data safe. Alex implements best practices for secure development.',
    image: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&h=400&fit=crop&crop=face',
    location: 'Boston, MA',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/alexthompson',
    twitter: 'https://twitter.com/alexthompson',
    github: 'https://github.com/alexthompson',
    skills: ['Cybersecurity', 'Penetration Testing', 'DevSecOps', 'Compliance'],
    education: 'MS Cybersecurity MIT, BS Computer Science Harvard',
    experience: '11+ years',
    languages: ['English', 'Russian'],
    interests: ['Ethical Hacking', 'Privacy Rights', 'Chess'],
    icon: ShieldCheckIcon,
    featured: false,
  },
  {
    id: 7,
    name: 'Maria Garcia',
    role: 'Data Scientist',
    department: 'Analytics',
    bio: 'AI and machine learning specialist who turns data into insights. Maria helps our clients make data-driven decisions.',
    image: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=400&fit=crop&crop=face',
    location: 'Los Angeles, CA',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/mariagarcia',
    twitter: 'https://twitter.com/mariagarcia',
    github: 'https://github.com/mariagarcia',
    skills: ['Machine Learning', 'Python', 'TensorFlow', 'Data Visualization'],
    education: 'PhD Data Science UCLA, MS Statistics Caltech',
    experience: '7+ years',
    languages: ['English', 'Spanish', 'Italian'],
    interests: ['AI Ethics', 'Climate Data', 'Salsa Dancing'],
    icon: ChartBarIcon,
    featured: false,
  },
  {
    id: 8,
    name: 'James Wilson',
    role: 'DevOps Engineer',
    department: 'Infrastructure',
    bio: 'Cloud infrastructure expert who ensures our applications scale seamlessly. James automates everything for maximum efficiency.',
    image: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=400&h=400&fit=crop&crop=face',
    location: 'Denver, CO',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/jameswilson',
    twitter: 'https://twitter.com/jameswilson',
    github: 'https://github.com/jameswilson',
    skills: ['AWS', 'Kubernetes', 'Docker', 'CI/CD'],
    education: 'BS Computer Engineering Colorado State',
    experience: '6+ years',
    languages: ['English'],
    interests: ['Automation', 'Mountain Biking', 'Craft Beer'],
    icon: CloudIcon,
    featured: false,
  },
];

const departments = ['All', 'Leadership', 'Engineering', 'Security', 'Analytics', 'Infrastructure'];

export default function TeamPage() {
  const featuredMembers = teamMembers.filter(member => member.featured);
  const allMembers = teamMembers;

  return (
    <div className="min-h-screen bg-white">
      <Header />
      {/* Header */}
      <header className="bg-gray-50 py-20 pt-32">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
              Meet Our <span className="gradient-text">Team</span>
            </h1>
            <p className="mt-6 text-xl text-gray-600 max-w-3xl mx-auto">
              We're a diverse group of passionate developers, designers, and innovators 
              committed to building exceptional software solutions that make a difference.
            </p>
          </motion.div>
        </div>
      </header>

      <main className="container py-16">
        {/* Leadership Section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-20"
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Leadership</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
            {featuredMembers.map((member, index) => (
              <motion.div
                key={member.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                className="bg-white rounded-2xl shadow-lg p-8 border border-gray-200"
              >
                <div className="flex flex-col items-center text-center">
                  <div className="relative w-32 h-32 mb-6">
                    <Image
                      src={member.image}
                      alt={member.name}
                      fill
                      className="object-cover rounded-full"
                    />
                  </div>
                  
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {member.name}
                  </h3>
                  
                  <p className="text-blue-600 font-medium mb-4">
                    {member.role}
                  </p>
                  
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {member.bio}
                  </p>
                  
                  <div className="grid grid-cols-2 gap-4 w-full mb-6 text-sm">
                    <div className="text-left">
                      <p className="text-gray-500 mb-1">Experience</p>
                      <p className="font-medium">{member.experience}</p>
                    </div>
                    <div className="text-left">
                      <p className="text-gray-500 mb-1">Location</p>
                      <p className="font-medium">{member.location}</p>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-2 mb-6">
                    {member.skills.slice(0, 4).map((skill) => (
                      <span
                        key={skill}
                        className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                  
                  <div className="flex space-x-4">
                    <Link
                      href={`mailto:${member.email}`}
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                    >
                      <EnvelopeIcon className="w-5 h-5" />
                    </Link>
                    <Link
                      href={member.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </Link>
                    <Link
                      href={member.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd"/>
                      </svg>
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Department Filter */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mb-12"
        >
          <div className="flex flex-wrap gap-2 justify-center">
            {departments.map((department) => (
              <button
                key={department}
                className="px-4 py-2 rounded-full text-sm font-medium border border-gray-300 hover:border-blue-500 hover:text-blue-600 transition-colors"
              >
                {department}
              </button>
            ))}
          </div>
        </motion.section>

        {/* All Team Members */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Our Team</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {allMembers.map((member, index) => (
              <motion.div
                key={member.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.05 * index }}
                className="bg-white rounded-xl shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow group"
              >
                <div className="text-center">
                  <div className="relative w-20 h-20 mx-auto mb-4">
                    <Image
                      src={member.image}
                      alt={member.name}
                      fill
                      className="object-cover rounded-full"
                    />
                  </div>
                  
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {member.name}
                  </h3>
                  
                  <p className="text-blue-600 text-sm font-medium mb-2">
                    {member.role}
                  </p>
                  
                  <div className="flex items-center justify-center text-gray-500 text-sm mb-3">
                    <MapPinIcon className="w-4 h-4 mr-1" />
                    {member.location}
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {member.bio}
                  </p>
                  
                  <div className="flex flex-wrap gap-1 mb-4">
                    {member.skills.slice(0, 2).map((skill) => (
                      <span
                        key={skill}
                        className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                      >
                        {skill}
                      </span>
                    ))}
                    {member.skills.length > 2 && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                        +{member.skills.length - 2}
                      </span>
                    )}
                  </div>
                  
                  <div className="flex justify-center space-x-3 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Link
                      href={`mailto:${member.email}`}
                      className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                    >
                      <EnvelopeIcon className="w-4 h-4" />
                    </Link>
                    <Link
                      href={member.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                    >
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </Link>
                    <Link
                      href={member.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                    >
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd"/>
                      </svg>
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Company Culture */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="mt-20 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-12 text-center"
        >
          <HeartIcon className="w-12 h-12 text-blue-600 mx-auto mb-6" />
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Join Our Team
          </h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            We're always looking for talented individuals who share our passion for innovation 
            and excellence. If you're ready to make an impact, we'd love to hear from you.
          </p>
          <Link href="/careers" className="btn-primary">
            View Open Positions
          </Link>
        </motion.section>
      </main>
      <Footer />
    </div>
  );
}
