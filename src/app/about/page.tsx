import { <PERSON>ada<PERSON> } from 'next';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { AboutPageClient } from './about-page-client';

// Static data - moved to constants for better maintainability
const VALUES = [
  {
    name: 'Innovation',
    description: 'We stay ahead of technology trends and embrace cutting-edge solutions to deliver exceptional results.',
    icon: 'LightBulbIcon',
  },
  {
    name: 'Quality',
    description: 'We deliver excellence in every project, ensuring robust, scalable, and maintainable solutions.',
    icon: 'ShieldCheckIcon',
  },
  {
    name: 'Partnership',
    description: 'We work as an extension of your team, fostering collaboration and transparent communication.',
    icon: 'HeartIcon',
  },
  {
    name: 'Growth',
    description: 'We are committed to continuous learning and helping our clients achieve sustainable growth.',
    icon: 'RocketLaunchIcon',
  },
] as const;

const STATS = [
  { name: 'Years of Experience', value: '10+' },
  { name: 'Projects Completed', value: '500+' },
  { name: 'Happy Clients', value: '200+' },
  { name: 'Team Members', value: '50+' },
  { name: 'Countries Served', value: '25+' },
  { name: 'Technologies Mastered', value: '100+' },
] as const;

const MILESTONES = [
  {
    year: '2014',
    title: 'Company Founded',
    description: 'Started as a small team with a big vision to democratize technology.',
  },
  {
    year: '2016',
    title: 'First Major Client',
    description: 'Secured our first enterprise client and delivered a game-changing solution.',
  },
  {
    year: '2018',
    title: 'Team Expansion',
    description: 'Grew to 25+ team members and opened our second office.',
  },
  {
    year: '2020',
    title: 'Global Reach',
    description: 'Expanded internationally and started serving clients across 5 continents.',
  },
  {
    year: '2022',
    title: 'Innovation Hub',
    description: 'Launched our R&D division focusing on AI and emerging technologies.',
  },
  {
    year: '2024',
    title: 'Industry Leader',
    description: 'Recognized as a leading software development company with 500+ successful projects.',
  },
] as const;

export const metadata: Metadata = {
  title: 'About Us - Technoloway',
  description: 'Learn about Technoloway\'s journey from a small startup to an industry leader. Discover our mission, values, and commitment to delivering cutting-edge software solutions.',
  keywords: [
    'about technoloway',
    'software development company',
    'technology innovation',
    'digital transformation',
    'custom software solutions',
    'web development company',
    'mobile app development',
    'enterprise solutions'
  ],
  openGraph: {
    title: 'About Us - Technoloway',
    description: 'Learn about Technoloway\'s journey from a small startup to an industry leader. Discover our mission, values, and commitment to delivering cutting-edge software solutions.',
    url: '/about',
    siteName: 'Technoloway',
    images: [
      {
        url: '/images/og-about.svg',
        width: 1200,
        height: 630,
        alt: 'About Technoloway - Software Development Company',
      }
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About Us - Technoloway',
    description: 'Learn about Technoloway\'s journey from a small startup to an industry leader. Discover our mission, values, and commitment to delivering cutting-edge software solutions.',
    images: ['/images/og-about.svg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: '/about',
  },
};

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <AboutPageClient 
        values={VALUES}
        stats={STATS}
        milestones={MILESTONES}
      />
      <Footer />
    </div>
  );
}
