'use client';

import { motion } from 'framer-motion';
import { memo } from 'react';
import {
  LightBulbIcon,
  ShieldCheckIcon,
  HeartIcon,
  RocketLaunchIcon,
  GlobeAltIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';

// Types for better type safety
interface Value {
  readonly name: string;
  readonly description: string;
  readonly icon: string;
}

interface Stat {
  readonly name: string;
  readonly value: string;
}

interface Milestone {
  readonly year: string;
  readonly title: string;
  readonly description: string;
}

interface AboutPageClientProps {
  values: readonly Value[];
  stats: readonly Stat[];
  milestones: readonly Milestone[];
}

// Icon mapping
const iconMap: Record<string, React.ComponentType<any>> = {
  LightBulbIcon,
  ShieldCheckIcon,
  HeartIcon,
  RocketLaunchIcon,
  GlobeAltIcon,
  SparklesIcon,
};

// Optimized components with memoization
const StatCard = memo(({ stat, index }: { stat: Stat; index: number }) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.8 }}
    whileInView={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.5, delay: index * 0.1 }}
    viewport={{ once: true }}
    className="text-center"
  >
    <div className="text-3xl font-bold text-blue-600 sm:text-4xl" aria-label={`${stat.value} ${stat.name}`}>
      {stat.value}
    </div>
    <div className="mt-2 text-sm text-gray-600">
      {stat.name}
    </div>
  </motion.div>
));

StatCard.displayName = 'StatCard';

const ValueCard = memo(({ value, index }: { value: Value; index: number }) => {
  const Icon = iconMap[value.icon];
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="text-center group"
    >
      <div className="flex items-center justify-center w-16 h-16 bg-blue-100 rounded-2xl mx-auto mb-6 group-hover:bg-blue-200 transition-colors">
        {Icon && <Icon className="w-8 h-8 text-blue-600" aria-hidden="true" />}
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-3">
        {value.name}
      </h3>
      <p className="text-gray-600 leading-relaxed">
        {value.description}
      </p>
    </motion.div>
  );
});

ValueCard.displayName = 'ValueCard';

const TimelineItem = memo(({ milestone, index }: { milestone: Milestone; index: number }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.6, delay: index * 0.1 }}
    viewport={{ once: true }}
    className={`relative flex items-center ${
      index % 2 === 0 ? 'justify-start' : 'justify-end'
    }`}
  >
    {/* Timeline dot */}
    <div 
      className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg"
      aria-hidden="true"
    />

    <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
      <div className="bg-white p-6 rounded-lg shadow-lg border border-gray-200">
        <div className="text-2xl font-bold text-blue-600 mb-2">
          {milestone.year}
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {milestone.title}
        </h3>
        <p className="text-gray-600">
          {milestone.description}
        </p>
      </div>
    </div>
  </motion.div>
));

TimelineItem.displayName = 'TimelineItem';

export function AboutPageClient({ values, stats, milestones }: AboutPageClientProps) {
  return (
    <main className="pt-20" role="main" aria-label="About Technoloway company information">
      {/* Hero Section */}
      <section className="relative py-24 bg-gradient-to-br from-gray-50 via-white to-gray-100" aria-label="About hero">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="mb-8"
            >
              <span className="inline-flex items-center rounded-full bg-blue-50 px-6 py-2 text-sm font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                <SparklesIcon className="w-4 h-4 mr-2" aria-hidden="true" />
                Our Story
              </span>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl"
            >
              Building the Future of{' '}
              <span className="gradient-text">Software</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl"
            >
              Founded in 2022, Technoloway has been at the forefront of software innovation, helping businesses transform their ideas into digital reality. We specialize in creating scalable, modern solutions that drive growth and efficiency.
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white" aria-label="Company statistics">
        <div className="container">
          <div className="grid grid-cols-2 gap-8 md:grid-cols-3 lg:grid-cols-6">
            {stats.map((stat, index) => (
              <StatCard key={`stat-${stat.name}-${index}`} stat={stat} index={index} />
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-24 bg-gray-50" aria-label="Our mission">
        <div className="container">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Our <span className="gradient-text">Mission</span>
              </h2>
              <p className="mt-6 text-lg text-gray-600 leading-8">
                To democratize cutting-edge technology and make it accessible to businesses of all sizes. We believe that every company deserves world-class software solutions that can compete with industry giants.
              </p>
              <p className="mt-4 text-lg text-gray-600 leading-8">
                Our commitment extends beyond just writing code. We partner with our clients to understand their unique challenges and craft solutions that not only meet their current needs but also scale with their future growth.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="aspect-square bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl p-8 flex items-center justify-center">
                <div className="text-center">
                  <GlobeAltIcon className="w-24 h-24 text-blue-600 mx-auto mb-4" aria-hidden="true" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Global Impact</h3>
                  <p className="text-gray-600">
                    Serving clients across 25+ countries with innovative solutions
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-24 bg-white" aria-label="Our values">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Our <span className="gradient-text">Values</span>
            </h2>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              The principles that guide everything we do
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <ValueCard key={`value-${value.name}-${index}`} value={value} index={index} />
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-24 bg-gray-50" aria-label="Company timeline">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Our <span className="gradient-text">Journey</span>
            </h2>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              From a small startup to an industry leader, here are the key milestones that shaped our company.
            </p>
          </motion.div>

          <div className="relative">
            {/* Timeline line */}
            <div 
              className="absolute left-1/2 transform -translate-x-px h-full w-0.5 bg-gray-300"
              aria-hidden="true"
            />

            <div className="space-y-12" role="list" aria-label="Company milestones">
              {milestones.map((milestone, index) => (
                <TimelineItem key={`milestone-${milestone.year}-${index}`} milestone={milestone} index={index} />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-r from-blue-600 to-purple-600" aria-label="Call to action">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Ready to Start Your Journey?
            </h2>
            <p className="mt-4 text-lg text-blue-100 max-w-3xl mx-auto">
              Join hundreds of satisfied clients who have transformed their businesses with our solutions.
              Let's build something amazing together.
            </p>
            <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/contact"
                className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
                aria-label="Get started with Technoloway"
              >
                Get Started Today
              </a>
              <a
                href="/portfolio"
                className="inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
                aria-label="View our portfolio of work"
              >
                View Our Work
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </main>
  );
} 