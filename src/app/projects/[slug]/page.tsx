'use client'

import { useParams } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Header } from '@/components/header'
import { Footer } from '@/components/footer'
import { useProject } from '@/hooks/use-projects'
import LoadingSpinner from '@/components/ui/loading-spinner'
import {
  ArrowLeftIcon,
  ArrowTopRightOnSquareIcon,
  CodeBracketIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  StarIcon,
  ClockIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'

export default function ProjectDetailPage() {
  const params = useParams()
  const slug = params.slug as string
  const { project, loading, error } = useProject(slug)

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <main className="pt-20">
          <div className="container py-16">
            <div className="flex justify-center">
              <LoadingSpinner size="lg" />
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  if (error || !project) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <main className="pt-20">
          <div className="container py-16">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">
                {error || 'Project not found'}
              </h1>
              <p className="text-gray-600 mb-8">
                The project you're looking for doesn't exist or has been removed.
              </p>
              <Link
                href="/projects"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Projects
              </Link>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i}>
        {i < Math.floor(rating) ? (
          <StarIconSolid className="h-5 w-5 text-yellow-400" />
        ) : (
          <StarIcon className="h-5 w-5 text-gray-300" />
        )}
      </span>
    ))
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="pt-20">

        {/* Breadcrumb */}
        <section className="py-8 bg-gray-50">
          <div className="container">
            <nav className="flex items-center space-x-2 text-sm">
              <Link href="/" className="text-gray-500 hover:text-gray-700">
                Home
              </Link>
              <span className="text-gray-400">/</span>
              <Link href="/projects" className="text-gray-500 hover:text-gray-700">
                Projects
              </Link>
              <span className="text-gray-400">/</span>
              <span className="text-gray-900">{project.name}</span>
            </nav>
          </div>
        </section>

        {/* Hero Section */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Project Info */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
              >
                <div className="mb-6">
                  <Link
                    href="/projects"
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-4"
                  >
                    <ArrowLeftIcon className="h-4 w-4 mr-2" />
                    Back to Projects
                  </Link>

                  <h1 className="text-4xl font-bold text-gray-900 mb-4">
                    {project.name}
                  </h1>

                  {project.client && (
                    <p className="text-lg text-gray-600 mb-4">
                      for <span className="font-semibold">{project.client.companyName}</span>
                    </p>
                  )}

                  <p className="text-lg text-gray-700 leading-relaxed">
                    {project.description}
                  </p>
                </div>

                {/* Project Stats */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  {project.startDate && (
                    <div className="flex items-center text-sm text-gray-600">
                      <CalendarIcon className="h-4 w-4 mr-2" />
                      <span>Started {formatDate(project.startDate)}</span>
                    </div>
                  )}

                  {project.completionDate && (
                    <div className="flex items-center text-sm text-gray-600">
                      <CheckCircleIcon className="h-4 w-4 mr-2" />
                      <span>Completed {formatDate(project.completionDate)}</span>
                    </div>
                  )}

                  {project.estimatedCost && (
                    <div className="flex items-center text-sm text-gray-600">
                      <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                      <span>{formatCurrency(project.estimatedCost)}</span>
                    </div>
                  )}

                  {project.duration && (
                    <div className="flex items-center text-sm text-gray-600">
                      <ClockIcon className="h-4 w-4 mr-2" />
                      <span>{project.duration} days</span>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-4">
                  {project.projectUrl && (
                    <a
                      href={project.projectUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-2" />
                      Visit Project
                    </a>
                  )}


                </div>
              </motion.div>

              {/* Project Image */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="relative"
              >
                {project.imageUrl ? (
                  <div className="relative h-96 rounded-xl overflow-hidden shadow-2xl">
                    <Image
                      src={project.imageUrl}
                      alt={project.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                ) : (
                  <div className="h-96 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center">
                    <CodeBracketIcon className="h-24 w-24 text-gray-400" />
                  </div>
                )}
              </motion.div>
            </div>
          </div>
        </section>

        {/* Project Details */}
        <section className="py-16 bg-gray-50">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              {/* Services */}
              {project.services.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                  className="bg-white p-6 rounded-xl shadow-lg"
                >
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    Services Provided
                  </h3>
                  <div className="space-y-3">
                    {project.services.map((service) => (
                      <div key={service.id} className="flex items-start">
                        <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                        <div>
                          <h4 className="font-medium text-gray-900">{service.name}</h4>
                          <p className="text-sm text-gray-600">{service.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Technologies */}
              {project.technologies.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white p-6 rounded-xl shadow-lg"
                >
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    Technologies Used
                  </h3>
                  <div className="grid grid-cols-2 gap-3">
                    {project.technologies.map((tech) => (
                      <div key={tech.id} className="flex items-center space-x-3">
                        {tech.iconUrl ? (
                          <Image
                            src={tech.iconUrl}
                            alt={tech.name}
                            width={24}
                            height={24}
                            className="rounded"
                          />
                        ) : (
                          <div className="w-6 h-6 bg-gray-300 rounded"></div>
                        )}
                        <span className="text-sm font-medium text-gray-900">
                          {tech.name}
                        </span>
                      </div>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Tags */}
              {project.tagsArray && project.tagsArray.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  viewport={{ once: true }}
                  className="bg-white p-6 rounded-xl shadow-lg"
                >
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    Project Tags
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {project.tagsArray.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                </motion.div>
              )}
            </div>
          </div>
        </section>

        {/* Client Feedback */}
        {project.feedbacks.length > 0 && (
          <section className="py-16">
            <div className="container">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center mb-12"
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  Client Feedback
                </h2>
                {project.averageRating && (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="flex items-center space-x-1">
                      {renderStars(project.averageRating)}
                    </div>
                    <span className="text-lg font-medium text-gray-900">
                      {project.averageRating.toFixed(1)} out of 5
                    </span>
                    <span className="text-gray-600">
                      ({project._count.feedbacks} review{project._count.feedbacks !== 1 ? 's' : ''})
                    </span>
                  </div>
                )}
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {project.feedbacks.map((feedback, index) => (
                  <motion.div
                    key={feedback.id}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-white p-6 rounded-xl shadow-lg"
                  >
                    <div className="flex items-center mb-4">
                      <div className="flex items-center space-x-1 mr-4">
                        {renderStars(feedback.rating)}
                      </div>
                      <span className="text-sm text-gray-600">
                        {formatDate(feedback.createdAt)}
                      </span>
                    </div>

                    <blockquote className="text-gray-700 mb-4">
                      "{feedback.comment}"
                    </blockquote>

                    {feedback.client && (
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">{feedback.client.contactName}</span>
                        {feedback.client.companyName && (
                          <span> at {feedback.client.companyName}</span>
                        )}
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <h2 className="text-3xl font-bold text-white mb-4">
                Interested in a Similar Project?
              </h2>
              <p className="text-lg text-blue-100 mb-8 max-w-2xl mx-auto">
                Let's discuss how we can help bring your vision to life with the same
                level of quality and attention to detail.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/contact"
                  className="inline-flex items-center px-6 py-3 bg-white text-blue-600 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Start Your Project
                </Link>
                <Link
                  href="/projects"
                  className="inline-flex items-center px-6 py-3 border border-white text-white rounded-lg hover:bg-white hover:text-blue-600 transition-colors"
                >
                  View More Projects
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
}
