import { Metada<PERSON> } from 'next';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { ProjectsPageClient } from './projects-page-client';

// Static data for better maintainability
const STATS = [
  { name: 'Projects Completed', value: '150+', iconName: 'RocketLaunchIcon' },
  { name: 'Technologies Used', value: '25+', iconName: 'CpuChipIcon' },
  { name: 'Lines of Code', value: '1M+', iconName: 'CodeBracketIcon' },
  { name: 'Countries Served', value: '15+', iconName: 'GlobeAltIcon' },
] as const;

const FEATURES = [
  {
    title: 'Custom Development',
    description: 'Tailored solutions built from the ground up to meet your specific business requirements.',
    iconName: 'CodeBracketIcon',
  },
  {
    title: 'Modern Technologies',
    description: 'We use cutting-edge technologies and frameworks to ensure your project is future-proof.',
    iconName: 'CpuChipIcon',
  },
  {
    title: 'Global Reach',
    description: 'Our projects serve clients worldwide, from startups to enterprise-level organizations.',
    iconName: 'GlobeAltIcon',
  },
  {
    title: 'Proven Results',
    description: 'Track record of successful project deliveries with measurable business impact.',
    iconName: 'RocketLaunchIcon',
  },
] as const;

export const metadata: Metadata = {
  title: 'Our Projects - Technoloway',
  description: 'Explore our portfolio of successful projects that showcase our expertise in delivering innovative software solutions across various industries.',
  keywords: [
    'portfolio projects',
    'software development projects',
    'web development portfolio',
    'mobile app projects',
    'custom software solutions',
    'enterprise projects',
    'digital transformation projects',
    'successful case studies'
  ],
  openGraph: {
    title: 'Our Projects - Technoloway',
    description: 'Explore our portfolio of successful projects that showcase our expertise in delivering innovative software solutions across various industries.',
    url: '/projects',
    siteName: 'Technoloway',
    images: [
      {
        url: '/images/og-projects.svg',
        width: 1200,
        height: 630,
        alt: 'Technoloway Projects Portfolio - Software Development Solutions',
      }
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Our Projects - Technoloway',
    description: 'Explore our portfolio of successful projects that showcase our expertise in delivering innovative software solutions across various industries.',
    images: ['/images/og-projects.svg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: '/projects',
  },
};

export default function ProjectsPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <ProjectsPageClient stats={STATS} features={FEATURES} />
      <Footer />
    </div>
  );
}
