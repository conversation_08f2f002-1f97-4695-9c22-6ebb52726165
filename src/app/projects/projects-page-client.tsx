'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { memo } from 'react';
import ProjectGrid from '@/components/projects/project-grid';
import { useStaticContent } from '@/lib/hooks/use-static-content';
import {
  RocketLaunchIcon,
  CodeBracketIcon,
  CpuChipIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';

// Types for better type safety
interface Stat {
  readonly name: string;
  readonly value: string;
  readonly iconName: string;
}

interface Feature {
  readonly title: string;
  readonly description: string;
  readonly iconName: string;
}

interface ProjectsPageClientProps {
  stats: readonly Stat[];
  features: readonly Feature[];
}

// Icon mapping
const iconMap: Record<string, React.ComponentType<any>> = {
  RocketLaunchIcon,
  CodeBracketIcon,
  CpuChipIcon,
  GlobeAltIcon,
};

// Optimized components with memoization
const StatCard = memo(({ stat, index }: { stat: Stat; index: number }) => {
  const Icon = iconMap[stat.iconName];
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
      className="text-center"
    >
      <div className="flex justify-center mb-2">
        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
          {Icon && <Icon className="h-6 w-6 text-blue-600" aria-hidden="true" />}
        </div>
      </div>
      <div className="text-2xl font-bold text-gray-900 sm:text-3xl" aria-label={`${stat.value} ${stat.name}`}>
        {stat.value}
      </div>
      <div className="mt-1 text-sm text-gray-600">
        {stat.name}
      </div>
    </motion.div>
  );
});

StatCard.displayName = 'StatCard';

const FeatureCard = memo(({ feature, index }: { feature: Feature; index: number }) => {
  const Icon = iconMap[feature.iconName];
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="text-center"
    >
      <div className="flex justify-center mb-4">
        <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center">
          {Icon && <Icon className="h-8 w-8 text-blue-600" aria-hidden="true" />}
        </div>
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">
        {feature.title}
      </h3>
      <p className="text-gray-600">
        {feature.description}
      </p>
    </motion.div>
  );
});

FeatureCard.displayName = 'FeatureCard';

const CTASection = memo(() => {
  const { getContent } = useStaticContent();
  
  return (
    <section className="section-padding bg-gradient-to-r from-blue-600 to-purple-600" aria-label="Call to action">
      <div className="container">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h2 className="text-3xl font-bold text-white sm:text-4xl">
            {getContent('projects', 'cta', 'title', 'Ready to Start Your Project?')}
          </h2>
          <p className="mt-4 text-lg text-blue-100 max-w-3xl mx-auto">
            {getContent('projects', 'cta', 'subtitle', 'Let\'s discuss how we can help bring your vision to life with our expertise and proven track record.')}
          </p>
          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href={getContent('projects', 'cta', 'button_url', '/contact')}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
              aria-label="Start your project with Technoloway"
            >
              {getContent('projects', 'cta', 'button_text', 'Get Started Today')}
            </Link>
            <Link
              href="/contact"
              className="inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
              aria-label="Contact Technoloway"
            >
              Get in Touch
            </Link>
          </div>
        </motion.div>
      </div>
    </section>
  );
});

CTASection.displayName = 'CTASection';

export function ProjectsPageClient({ stats, features }: ProjectsPageClientProps) {
  const { getContent } = useStaticContent();

  return (
    <main role="main" aria-label="Projects page content">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 pt-20 pb-16" aria-label="Projects hero">
        <div className="container">
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
                {getContent('projects', 'hero', 'title', 'Our')} <span className="gradient-text">{getContent('projects', 'hero', 'title_highlight', 'Projects')}</span>
              </h1>
              <p className="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
                {getContent('projects', 'hero', 'subtitle', 'Explore our portfolio of successful projects that showcase our expertise in delivering innovative software solutions across various industries.')}
              </p>
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mt-12"
            >
              <div className="grid grid-cols-2 gap-8 md:grid-cols-4" role="list" aria-label="Project statistics">
                {stats.map((stat, index) => (
                  <StatCard key={`stat-${stat.name}-${index}`} stat={stat} index={index} />
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="section-padding bg-gray-50" aria-label="Why choose our solutions">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              {getContent('projects', 'features', 'title', 'Why Choose')} <span className="gradient-text">{getContent('projects', 'features', 'title_highlight', 'Our Solutions')}</span>
            </h2>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              {getContent('projects', 'features', 'subtitle', 'We deliver exceptional results through proven methodologies')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8" role="list" aria-label="Solution features">
            {features.map((feature, index) => (
              <FeatureCard key={`feature-${feature.title}-${index}`} feature={feature} index={index} />
            ))}
          </div>
        </div>
      </section>

      {/* Projects Grid Section */}
      <section className="section-padding" aria-label="Project portfolio">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Explore Our <span className="gradient-text">Portfolio</span>
            </h2>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              Browse through our collection of successful projects. Use filters to find projects
              by technology, service type, or search for specific solutions.
            </p>
          </motion.div>

          <ProjectGrid
            showFilters={true}
            showSearch={true}
            variant="default"
            columns={3}
            initialProjects={[]} // Will be fetched by the component
          />
        </div>
      </section>

      {/* CTA Section */}
      <CTASection />
    </main>
  );
} 