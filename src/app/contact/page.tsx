import { <PERSON>ada<PERSON> } from 'next';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { ContactPageClient } from './contact-page-client';

// Static data for better maintainability
const PROJECT_TYPES = [
  'Web Development',
  'Mobile App Development',
  'E-commerce Platform',
  'Custom Software',
  'API Development',
  'Cloud Solutions',
  'UI/UX Design',
  'Consulting',
  'Other'
] as const;

const BUDGET_RANGES = [
  'Under $10,000',
  '$10,000 - $25,000',
  '$25,000 - $50,000',
  '$50,000 - $100,000',
  'Over $100,000',
  'Not sure yet'
] as const;

const TIMELINES = [
  'ASAP',
  '1-2 months',
  '3-6 months',
  '6-12 months',
  'More than 1 year',
  'Flexible'
] as const;

const FAQ_ITEMS = [
  {
    question: 'How long does a typical project take?',
    answer: 'Project timelines vary based on complexity and scope. Simple websites typically take 4-6 weeks, while complex applications can take 3-6 months. We provide detailed timelines during our initial consultation.'
  },
  {
    question: 'What is your development process?',
    answer: 'We follow an agile development methodology with regular check-ins and updates. Our process includes discovery, planning, design, development, testing, and deployment phases with continuous client collaboration.'
  },
  {
    question: 'Do you provide ongoing support and maintenance?',
    answer: 'Yes, we offer comprehensive support and maintenance packages to ensure your application stays secure, updated, and performing optimally. We provide different tiers of support based on your needs.'
  },
  {
    question: 'Can you work with our existing team?',
    answer: 'Absolutely! We can integrate with your existing development team, provide consulting services, or work as an extension of your team. We adapt our approach to fit your organizational needs.'
  }
] as const;

export const metadata: Metadata = {
  title: 'Contact Us - Technoloway',
  description: 'Ready to transform your ideas into reality? Get in touch with our team and let\'s discuss how we can help bring your vision to life.',
  keywords: [
    'contact technoloway',
    'software development contact',
    'web development consultation',
    'project inquiry',
    'custom software quote',
    'mobile app development contact',
    'enterprise solutions contact',
    'digital transformation consultation'
  ],
  openGraph: {
    title: 'Contact Us - Technoloway',
    description: 'Ready to transform your ideas into reality? Get in touch with our team and let\'s discuss how we can help bring your vision to life.',
    url: '/contact',
    siteName: 'Technoloway',
    images: [
      {
        url: '/images/og-contact.svg',
        width: 1200,
        height: 630,
        alt: 'Contact Technoloway - Software Development Consultation',
      }
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact Us - Technoloway',
    description: 'Ready to transform your ideas into reality? Get in touch with our team and let\'s discuss how we can help bring your vision to life.',
    images: ['/images/og-contact.svg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: '/contact',
  },
};

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <ContactPageClient 
        projectTypes={PROJECT_TYPES}
        budgetRanges={BUDGET_RANGES}
        timelines={TIMELINES}
        faqItems={FAQ_ITEMS}
      />
      <Footer />
    </div>
  );
}
