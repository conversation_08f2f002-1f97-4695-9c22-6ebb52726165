'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useServices } from '@/hooks/useServices'
import {
  BuildingOfficeIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  UserIcon,
  CreditCardIcon,
  DocumentTextIcon,
  BanknotesIcon,
  ChatBubbleLeftIcon,
  CogIcon,
  EyeIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ListBulletIcon,
  Squares2X2Icon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  ArrowTopRightOnSquareIcon,
  XMarkIcon,
  TableCellsIcon,
  ChevronUpDownIcon,
} from '@heroicons/react/24/outline'

// Import modal components
import PaymentModal from '@/components/client/payment-modal'
import QuotationModal from '@/components/client/quotation-modal'
import PasswordModal from '@/components/client/password-modal'
import ProfileModal from '@/components/client/profile-modal'
import StripeWrapper from '@/components/client/stripe-wrapper'

// Import view modal components
import ProjectViewModal from '@/components/client/project-view-modal'
import InvoiceViewModal from '@/components/client/invoice-view-modal'
import PaymentViewModal from '@/components/client/payment-view-modal'
import QuotationViewModal from '@/components/client/quotation-view-modal'

// Types
interface Client {
  id: number
  name: string
  email: string
  phone?: string
  address?: string
  city?: string
  state?: string
  zipcode?: string
  country?: string
  // Additional fields needed by ProfileModal
  companyname?: string
  contactname?: string
  contactemail?: string
  contactphone?: string
  companywebsite?: string
  notes?: string
}

interface Project {
  id: number
  name: string
  description?: string
  status: string
  projstartdate?: string
  projcompletiondate?: string
  estimatecost?: number
  estimatetime?: string
  updatedat?: string
}

interface Invoice {
  id: number
  totalamount: number
  duedate: string
  issuedate?: string
  status: string
  description?: string
}

interface Payment {
  id: number
  amount: number
  paymentdate: string
  paymentmethod: string
  invoiceid: number
  status: string
}

interface Message {
  id: number
  content: string
  createdat: string
  isread: boolean
}

interface QuoteRequest {
  id: number
  servicename: string
  description?: string
  status: string
  requestdate?: string
  budgetrange?: string
}

export default function ClientDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { services } = useServices()
  const [activeSection, setActiveSection] = useState('overview')
  const [loading, setLoading] = useState(true)
  const [client, setClient] = useState<Client | null>(null)
  const [projects, setProjects] = useState<Project[]>([])
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [payments, setPayments] = useState<Payment[]>([])
  const [messages, setMessages] = useState<Message[]>([])
  const [quotations, setQuotations] = useState<QuoteRequest[]>([])

  // Modal states
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [showQuotationModal, setShowQuotationModal] = useState(false)
  const [showPasswordModal, setShowPasswordModal] = useState(false)
  const [showProfileModal, setShowProfileModal] = useState(false)

  // View modal states
  const [showProjectViewModal, setShowProjectViewModal] = useState(false)
  const [showInvoiceViewModal, setShowInvoiceViewModal] = useState(false)
  const [showPaymentViewModal, setShowPaymentViewModal] = useState(false)
  const [showQuotationViewModal, setShowQuotationViewModal] = useState(false)

  // Selected items for view modals
  const [selectedProjectForView, setSelectedProjectForView] = useState<Project | null>(null)
  const [selectedInvoiceForView, setSelectedInvoiceForView] = useState<Invoice | null>(null)
  const [selectedPaymentForView, setSelectedPaymentForView] = useState<Payment | null>(null)
  const [selectedQuotationForView, setSelectedQuotationForView] = useState<QuoteRequest | null>(null)

  // Enhanced Projects Section State
  const [projectsSearchQuery, setProjectsSearchQuery] = useState('')
  const [projectsShowFilters, setProjectsShowFilters] = useState(false)
  const [projectsViewMode, setProjectsViewMode] = useState('list')
  const [projectsDisplayDensity, setProjectsDisplayDensity] = useState('comfortable')
  const [projectsShowColumnControls, setProjectsShowColumnControls] = useState(false)
  const [projectsVisibleColumns, setProjectsVisibleColumns] = useState(['name', 'status', 'projstartdate', 'estimatecost'])
  const [projectsSortField, setProjectsSortField] = useState('')
  const [projectsSortOrder, setProjectsSortOrder] = useState<'asc' | 'desc'>('asc')

  // Enhanced Invoices Section State
  const [invoicesSearchQuery, setInvoicesSearchQuery] = useState('')
  const [invoicesShowFilters, setInvoicesShowFilters] = useState(false)
  const [invoicesViewMode, setInvoicesViewMode] = useState('list')
  const [invoicesDisplayDensity, setInvoicesDisplayDensity] = useState('comfortable')
  const [invoicesShowColumnControls, setInvoicesShowColumnControls] = useState(false)
  const [invoicesVisibleColumns, setInvoicesVisibleColumns] = useState(['invoicenumber', 'amount', 'duedate', 'issuedate', 'status', 'description'])
  const [invoicesSortField, setInvoicesSortField] = useState('')
  const [invoicesSortOrder, setInvoicesSortOrder] = useState<'asc' | 'desc'>('asc')

  // Enhanced Payments Section State
  const [paymentsSearchQuery, setPaymentsSearchQuery] = useState('')
  const [paymentsShowFilters, setPaymentsShowFilters] = useState(false)
  const [paymentsViewMode, setPaymentsViewMode] = useState('list')
  const [paymentsDisplayDensity, setPaymentsDisplayDensity] = useState('comfortable')
  const [paymentsShowColumnControls, setPaymentsShowColumnControls] = useState(false)
  const [paymentsVisibleColumns, setPaymentsVisibleColumns] = useState(['date', 'amount', 'method', 'invoice', 'status'])
  const [paymentsSortField, setPaymentsSortField] = useState('')
  const [paymentsSortOrder, setPaymentsSortOrder] = useState<'asc' | 'desc'>('asc')

  // Enhanced Quotations Section State
  const [quotationsSearchQuery, setQuotationsSearchQuery] = useState('')
  const [quotationsShowFilters, setQuotationsShowFilters] = useState(false)
  const [quotationsViewMode, setQuotationsViewMode] = useState('list')
  const [quotationsDisplayDensity, setQuotationsDisplayDensity] = useState('comfortable')
  const [quotationsShowColumnControls, setQuotationsShowColumnControls] = useState(false)
  const [quotationsVisibleColumns, setQuotationsVisibleColumns] = useState(['service', 'description', 'status', 'date', 'budget'])
  const [quotationsSortField, setQuotationsSortField] = useState('')
  const [quotationsSortOrder, setQuotationsSortOrder] = useState<'asc' | 'desc'>('asc')

  // Utility functions
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'paid':
      case 'completed':
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'pending':
      case 'in progress':
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800'
      case 'overdue':
      case 'failed':
      case 'rejected':
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'draft':
      case 'on hold':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-blue-100 text-blue-800'
    }
  }

  // Authentication check
  useEffect(() => {
    if (status === 'loading') return

    if (status === 'unauthenticated') {
      router.push('/client-auth/signin')
      return
    }

    if (session?.user?.email) {
      fetchClientData()
    }
  }, [session, status, router])



  const fetchClientData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/client/dashboard')

      if (!response.ok) {
        throw new Error('Failed to fetch client data')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch client data')
      }

      const data = result.data

      // Set client data with proper field mapping
      setClient({
        id: data.id,
        name: data.companyname || data.contactname,
        email: data.contactemail,
        phone: data.contactphone,
        address: data.address,
        city: data.city,
        state: data.state,
        zipcode: data.zipcode,
        country: data.country,
        // Preserve original fields for ProfileModal
        companyname: data.companyname,
        contactname: data.contactname,
        contactemail: data.contactemail,
        contactphone: data.contactphone,
        companywebsite: data.companywebsite,
        notes: data.notes
      })

      setProjects(data.projects || [])
      setInvoices(data.invoices || [])

      // Extract payments from invoices
      const allPayments = data.invoices?.flatMap(invoice =>
        invoice.payments?.map(payment => ({
          ...payment,
          invoiceid: invoice.id
        })) || []
      ) || []
      setPayments(allPayments)

      // Extract messages from projects
      const allMessages = data.projects?.flatMap(project =>
        project.messages?.map(message => ({
          ...message,
          projectid: project.id,
          projectname: project.name
        })) || []
      ) || []
      setMessages(allMessages)

      // Set quotations from quotationrequests
      setQuotations(data.quotationrequests || [])
    } catch (error) {
      console.error('Error fetching client data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!client) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-4">You don't have access to this dashboard.</p>
          <button
            onClick={() => router.push('/client-auth/signin')}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Sign In
          </button>
        </div>
      </div>
    )
  }

  // Navigation items
  const navigationItems = [
    { id: 'overview', name: 'Overview', icon: BuildingOfficeIcon },
    { id: 'projects', name: 'Projects', icon: DocumentTextIcon },
    { id: 'invoices', name: 'Invoices', icon: DocumentTextIcon },
    { id: 'payments', name: 'Payments', icon: CreditCardIcon },
    { id: 'quotations', name: 'Quotations', icon: BanknotesIcon },
    { id: 'messages', name: 'Messages', icon: ChatBubbleLeftIcon },
    { id: 'settings', name: 'Settings', icon: CogIcon },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">Client Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">Welcome, {client.name}</span>
              <button
                onClick={() => router.push('/api/auth/signout')}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">
          {/* Sidebar */}
          <div className="w-64 flex-shrink-0">
            <nav className="space-y-1">
              {navigationItems.map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveSection(item.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeSection === item.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {activeSection === 'overview' && renderOverview()}
            {activeSection === 'projects' && renderProjects()}
            {activeSection === 'invoices' && renderInvoices()}
            {activeSection === 'payments' && renderPayments()}
            {activeSection === 'quotations' && renderQuotations()}
            {activeSection === 'messages' && renderMessages()}
            {activeSection === 'settings' && renderSettings()}
          </div>
        </div>
      </div>

      {/* Modals */}
      {showPaymentModal && (
        <StripeWrapper>
          <PaymentModal
            isOpen={showPaymentModal}
            onClose={() => setShowPaymentModal(false)}
            invoices={invoices}
            onSubmit={async (paymentData) => {
              try {
                console.log('Payment data being sent:', paymentData)

                // Handle payment submission
                const response = await fetch('/api/payments', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify(paymentData)
                })

                console.log('Payment response status:', response.status)

                if (response.ok) {
                  const result = await response.json()
                  console.log('Payment success:', result)
                  await fetchClientData()
                  setShowPaymentModal(false)
                } else {
                  const errorData = await response.json()
                  console.error('Payment API error:', errorData)
                  throw new Error(errorData.error || 'Payment failed')
                }
              } catch (error) {
                console.error('Payment error:', error)
                throw error
              }
            }}
          />
        </StripeWrapper>
      )}

      {showQuotationModal && (
        <QuotationModal
          isOpen={showQuotationModal}
          onClose={() => setShowQuotationModal(false)}
          services={services}
          onSubmit={async (quotationData) => {
            try {
              if (!client?.id) {
                throw new Error('Client information not available')
              }

              // Add client ID to quotation data
              const submissionData = {
                ...quotationData,
                clientId: client.id.toString()
              }

              console.log('Submitting quotation data:', submissionData)

              // Handle quotation submission
              const response = await fetch('/api/quotations', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(submissionData)
              })

              const result = await response.json()
              console.log('Quotation response:', result)

              if (response.ok) {
                await fetchClientData()
                setShowQuotationModal(false)
                alert('Quotation request submitted successfully!')
              } else {
                throw new Error(result.message || 'Quotation submission failed')
              }
            } catch (error) {
              console.error('Quotation error:', error)
              throw error
            }
          }}
        />
      )}

      {showPasswordModal && (
        <PasswordModal
          isOpen={showPasswordModal}
          onClose={() => setShowPasswordModal(false)}
          onSubmit={async (passwordData) => {
            // Handle password change
            const response = await fetch('/api/user/change-password', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(passwordData)
            })

            if (response.ok) {
              setShowPasswordModal(false)
              alert('Password changed successfully!')
            } else {
              const error = await response.json()
              throw new Error(error.message || 'Password change failed')
            }
          }}
        />
      )}

      {showProfileModal && (
        <ProfileModal
          isOpen={showProfileModal}
          onClose={() => setShowProfileModal(false)}
          client={client}
          onSubmit={async (profileData) => {
            try {
              // Handle profile update
              const response = await fetch('/api/client/profile', {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(profileData)
              })

              const result = await response.json()

              if (response.ok && result.success) {
                await fetchClientData()
                setShowProfileModal(false)
              } else {
                throw new Error(result.message || 'Profile update failed')
              }
            } catch (error) {
              console.error('Profile update error:', error)
              throw error
            }
          }}
        />
      )}

      {/* View Modals */}
      {showProjectViewModal && selectedProjectForView && (
        <ProjectViewModal
          isOpen={showProjectViewModal}
          onClose={() => {
            setShowProjectViewModal(false)
            setSelectedProjectForView(null)
          }}
          project={selectedProjectForView}
        />
      )}

      {showInvoiceViewModal && selectedInvoiceForView && (
        <InvoiceViewModal
          isOpen={showInvoiceViewModal}
          onClose={() => {
            setShowInvoiceViewModal(false)
            setSelectedInvoiceForView(null)
          }}
          invoice={selectedInvoiceForView}
        />
      )}

      {showPaymentViewModal && selectedPaymentForView && (
        <PaymentViewModal
          isOpen={showPaymentViewModal}
          onClose={() => {
            setShowPaymentViewModal(false)
            setSelectedPaymentForView(null)
          }}
          payment={selectedPaymentForView}
        />
      )}

      {showQuotationViewModal && selectedQuotationForView && (
        <QuotationViewModal
          isOpen={showQuotationViewModal}
          onClose={() => {
            setShowQuotationViewModal(false)
            setSelectedQuotationForView(null)
          }}
          quotation={selectedQuotationForView}
        />
      )}
    </div>
  )

  // Render functions
  function renderOverview() {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Client Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center">
              <UserIcon className="h-5 w-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900">{client.name}</p>
                <p className="text-sm text-gray-500">Client Name</p>
              </div>
            </div>
            <div className="flex items-center">
              <EnvelopeIcon className="h-5 w-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900">{client.email}</p>
                <p className="text-sm text-gray-500">Email Address</p>
              </div>
            </div>
            {client.phone && (
              <div className="flex items-center">
                <PhoneIcon className="h-5 w-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{client.phone}</p>
                  <p className="text-sm text-gray-500">Phone Number</p>
                </div>
              </div>
            )}
            {client.address && (
              <div className="flex items-center">
                <MapPinIcon className="h-5 w-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {client.address}
                    {client.city && `, ${client.city}`}
                    {client.state && `, ${client.state}`}
                    {client.zipcode && ` ${client.zipcode}`}
                  </p>
                  <p className="text-sm text-gray-500">Address</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <DocumentTextIcon className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{projects.length}</p>
                <p className="text-sm text-gray-500">Projects</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <DocumentTextIcon className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{invoices.length}</p>
                <p className="text-sm text-gray-500">Invoices</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <CreditCardIcon className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{payments.length}</p>
                <p className="text-sm text-gray-500">Payments</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <ChatBubbleLeftIcon className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{messages.filter(m => !m.isread).length}</p>
                <p className="text-sm text-gray-500">Unread Messages</p>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {projects.slice(0, 3).map((project) => (
                <div key={project.id} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{project.name}</p>
                    <p className="text-sm text-gray-500">Project • {project.status}</p>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                    {project.status}
                  </span>
                </div>
              ))}
              {invoices.slice(0, 2).map((invoice) => (
                <div key={invoice.id} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">Invoice #{invoice.id}</p>
                    <p className="text-sm text-gray-500">{formatCurrency(invoice.totalamount)} • Due {formatDate(invoice.duedate)}</p>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                    {invoice.status}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  function renderProjects() {
    const handleSort = (field: string) => {
      const newOrder = projectsSortField === field && projectsSortOrder === 'asc' ? 'desc' : 'asc'
      setProjectsSortField(field)
      setProjectsSortOrder(newOrder)
    }

    const filteredProjects = projects.filter(project =>
      project.name.toLowerCase().includes(projectsSearchQuery.toLowerCase()) ||
      project.description?.toLowerCase().includes(projectsSearchQuery.toLowerCase())
    )

    const availableColumns = [
      { key: 'name', label: 'Project Name' },
      { key: 'status', label: 'Status' },
      { key: 'projstartdate', label: 'Start Date' },
      { key: 'projcompletiondate', label: 'Completion Date' },
      { key: 'estimatecost', label: 'Estimated Cost' },
      { key: 'estimatetime', label: 'Estimated Time' },
      { key: 'updatedat', label: 'Last Updated' },
    ]

    return (
      <div className="space-y-6">
        {/* Enhanced Section Header */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div>
                <h2 className="text-lg font-medium text-gray-900">Projects</h2>
                <p className="mt-1 text-sm text-gray-500">Manage and track your project progress</p>
              </div>
            </div>

            {/* Controls Bar */}
            <div className="mt-4 flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Search */}
              <div className="flex-1 max-w-lg">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={projectsSearchQuery}
                    onChange={(e) => setProjectsSearchQuery(e.target.value)}
                    placeholder="Search projects..."
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center space-x-3">
                {/* Filters */}
                <div className="relative">
                  <button
                    onClick={() => setProjectsShowFilters(!projectsShowFilters)}
                    className={`inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium transition-colors ${
                      projectsShowFilters ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <FunnelIcon className="h-4 w-4 mr-2" />
                    Filters
                  </button>
                </div>

                {/* View Mode */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setProjectsViewMode('list')}
                    className={`p-2 text-sm font-medium transition-colors ${
                      projectsViewMode === 'list' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="List view"
                  >
                    <ListBulletIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setProjectsViewMode('grid')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      projectsViewMode === 'grid' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Grid view"
                  >
                    <Squares2X2Icon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setProjectsViewMode('card')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      projectsViewMode === 'card' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Card view"
                  >
                    <RectangleStackIcon className="h-4 w-4" />
                  </button>
                </div>

                {/* Display Density */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setProjectsDisplayDensity('compact')}
                    className={`px-3 py-2 text-xs font-medium transition-colors ${
                      projectsDisplayDensity === 'compact' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Compact
                  </button>
                  <button
                    onClick={() => setProjectsDisplayDensity('comfortable')}
                    className={`px-3 py-2 text-xs font-medium transition-colors border-l border-gray-300 ${
                      projectsDisplayDensity === 'comfortable' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Comfortable
                  </button>
                </div>

                {/* Column Controls */}
                <div className="relative">
                  <button
                    onClick={() => setProjectsShowColumnControls(!projectsShowColumnControls)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    <TableCellsIcon className="h-4 w-4 mr-2" />
                    Columns
                  </button>
                  {projectsShowColumnControls && (
                    <div className="absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                      <div className="p-3">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Show columns</h4>
                        <div className="space-y-2">
                          {availableColumns.map((column) => (
                            <label key={column.key} className="flex items-center">
                              <input
                                type="checkbox"
                                checked={projectsVisibleColumns.includes(column.key)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setProjectsVisibleColumns(prev => [...prev, column.key])
                                  } else {
                                    setProjectsVisibleColumns(prev => prev.filter(col => col !== column.key))
                                  }
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="ml-2 text-sm text-gray-700">{column.label}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Summary Line */}
          <div className="px-6 py-3 bg-gray-50 border-b border-gray-200">
            <p className="text-sm text-gray-600">
              Showing {filteredProjects.length} of {projects.length} projects
            </p>
          </div>

          {/* Content */}
          <div className="p-6">
            {filteredProjects.length === 0 ? (
              <div className="text-center py-12">
                <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No projects found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {projectsSearchQuery ? 'Try adjusting your search' : 'You don\'t have any projects yet.'}
                </p>
              </div>
            ) : (
              <>
                {/* List View */}
                {projectsViewMode === 'list' && (
                  <div className="overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {availableColumns
                            .filter(col => projectsVisibleColumns.includes(col.key))
                            .map((column) => (
                              <th
                                key={column.key}
                                onClick={() => handleSort(column.key)}
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              >
                                <div className="flex items-center space-x-1">
                                  <span>{column.label}</span>
                                  <div className="flex flex-col">
                                    <ChevronUpIcon
                                      className={`h-3 w-3 ${projectsSortField === column.key && projectsSortOrder === 'asc' ? 'text-blue-600' : 'text-gray-400'}`}
                                    />
                                    <ChevronDownIcon
                                      className={`h-3 w-3 -mt-1 ${projectsSortField === column.key && projectsSortOrder === 'desc' ? 'text-blue-600' : 'text-gray-400'}`}
                                    />
                                  </div>
                                </div>
                              </th>
                            ))}
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredProjects.map((project) => (
                          <tr key={project.id} className={projectsDisplayDensity === 'compact' ? 'h-12' : 'h-16'}>
                            {projectsVisibleColumns.includes('name') && (
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">{project.name}</div>
                                    {project.description && <div className="text-sm text-gray-500">{project.description}</div>}
                                  </div>
                                </div>
                              </td>
                            )}
                            {projectsVisibleColumns.includes('status') && (
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                                  {project.status}
                                </span>
                              </td>
                            )}
                            {projectsVisibleColumns.includes('projstartdate') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {project.projstartdate ? formatDate(project.projstartdate) : '-'}
                              </td>
                            )}
                            {projectsVisibleColumns.includes('projcompletiondate') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {project.projcompletiondate ? formatDate(project.projcompletiondate) : '-'}
                              </td>
                            )}
                            {projectsVisibleColumns.includes('estimatecost') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {project.estimatecost ? formatCurrency(project.estimatecost) : '-'}
                              </td>
                            )}
                            {projectsVisibleColumns.includes('estimatetime') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {project.estimatetime || '-'}
                              </td>
                            )}
                            {projectsVisibleColumns.includes('updatedat') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {project.updatedat ? formatDate(project.updatedat) : '-'}
                              </td>
                            )}
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={() => {
                                  setSelectedProjectForView(project)
                                  setShowProjectViewModal(true)
                                }}
                                className="text-blue-600 hover:text-blue-900 inline-flex items-center"
                              >
                                <EyeIcon className="h-4 w-4 mr-1" />
                                View
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    )
  }

  function renderInvoices() {
    const handleSort = (field: string) => {
      const newOrder = invoicesSortField === field && invoicesSortOrder === 'asc' ? 'desc' : 'asc'
      setInvoicesSortField(field)
      setInvoicesSortOrder(newOrder)
    }

    const filteredInvoices = invoices.filter(invoice =>
      String(invoice.id).includes(invoicesSearchQuery) ||
      invoice.description?.toLowerCase().includes(invoicesSearchQuery.toLowerCase())
    )

    const availableColumns = [
      { key: 'invoicenumber', label: 'Invoice #' },
      { key: 'amount', label: 'Amount' },
      { key: 'duedate', label: 'Due Date' },
      { key: 'issuedate', label: 'Issue Date' },
      { key: 'status', label: 'Status' },
      { key: 'description', label: 'Description' },
    ]

    return (
      <div className="space-y-6">
        {/* Enhanced Section Header */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div>
                <h2 className="text-lg font-medium text-gray-900">Invoices</h2>
                <p className="mt-1 text-sm text-gray-500">View and manage your invoices</p>
              </div>
            </div>

            {/* Controls Bar */}
            <div className="mt-4 flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Search */}
              <div className="flex-1 max-w-lg">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={invoicesSearchQuery}
                    onChange={(e) => setInvoicesSearchQuery(e.target.value)}
                    placeholder="Search invoices..."
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center space-x-3">
                {/* Filters */}
                <div className="relative">
                  <button
                    onClick={() => setInvoicesShowFilters(!invoicesShowFilters)}
                    className={`inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium transition-colors ${
                      invoicesShowFilters ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <FunnelIcon className="h-4 w-4 mr-2" />
                    Filters
                  </button>
                </div>

                {/* View Mode */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setInvoicesViewMode('list')}
                    className={`p-2 text-sm font-medium transition-colors ${
                      invoicesViewMode === 'list' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="List view"
                  >
                    <ListBulletIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setInvoicesViewMode('grid')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      invoicesViewMode === 'grid' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Grid view"
                  >
                    <Squares2X2Icon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setInvoicesViewMode('card')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      invoicesViewMode === 'card' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Card view"
                  >
                    <RectangleStackIcon className="h-4 w-4" />
                  </button>
                </div>

                {/* Display Density */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setInvoicesDisplayDensity('compact')}
                    className={`px-3 py-2 text-xs font-medium transition-colors ${
                      invoicesDisplayDensity === 'compact' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Compact
                  </button>
                  <button
                    onClick={() => setInvoicesDisplayDensity('comfortable')}
                    className={`px-3 py-2 text-xs font-medium transition-colors border-l border-gray-300 ${
                      invoicesDisplayDensity === 'comfortable' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Comfortable
                  </button>
                </div>

                {/* Column Controls */}
                <div className="relative">
                  <button
                    onClick={() => setInvoicesShowColumnControls(!invoicesShowColumnControls)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    <TableCellsIcon className="h-4 w-4 mr-2" />
                    Columns
                  </button>
                  {invoicesShowColumnControls && (
                    <div className="absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                      <div className="p-3">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Show columns</h4>
                        <div className="space-y-2">
                          {availableColumns.map((column) => (
                            <label key={column.key} className="flex items-center">
                              <input
                                type="checkbox"
                                checked={invoicesVisibleColumns.includes(column.key)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setInvoicesVisibleColumns(prev => [...prev, column.key])
                                  } else {
                                    setInvoicesVisibleColumns(prev => prev.filter(col => col !== column.key))
                                  }
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="ml-2 text-sm text-gray-700">{column.label}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Summary Line */}
          <div className="px-6 py-3 bg-gray-50 border-b border-gray-200">
            <p className="text-sm text-gray-600">
              Showing {filteredInvoices.length} of {invoices.length} invoices
            </p>
          </div>

          {/* Content */}
          <div className="p-6">
            {filteredInvoices.length === 0 ? (
              <div className="text-center py-12">
                <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No invoices found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {invoicesSearchQuery ? 'Try adjusting your search' : 'You don\'t have any invoices yet.'}
                </p>
              </div>
            ) : (
              <>
                {/* List View */}
                {invoicesViewMode === 'list' && (
                  <div className="overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {availableColumns
                            .filter(col => invoicesVisibleColumns.includes(col.key))
                            .map((column) => (
                              <th
                                key={column.key}
                                onClick={() => handleSort(column.key)}
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              >
                                <div className="flex items-center space-x-1">
                                  <span>{column.label}</span>
                                  <div className="flex flex-col">
                                    <ChevronUpIcon
                                      className={`h-3 w-3 ${invoicesSortField === column.key && invoicesSortOrder === 'asc' ? 'text-blue-600' : 'text-gray-400'}`}
                                    />
                                    <ChevronDownIcon
                                      className={`h-3 w-3 -mt-1 ${invoicesSortField === column.key && invoicesSortOrder === 'desc' ? 'text-blue-600' : 'text-gray-400'}`}
                                    />
                                  </div>
                                </div>
                              </th>
                            ))}
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredInvoices.map((invoice) => (
                          <tr key={invoice.id} className={invoicesDisplayDensity === 'compact' ? 'h-12' : 'h-16'}>
                            {invoicesVisibleColumns.includes('invoicenumber') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                #{invoice.id}
                              </td>
                            )}
                            {invoicesVisibleColumns.includes('amount') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {formatCurrency(invoice.totalamount)}
                              </td>
                            )}
                            {invoicesVisibleColumns.includes('duedate') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatDate(invoice.duedate)}
                              </td>
                            )}
                            {invoicesVisibleColumns.includes('issuedate') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {invoice.issuedate ? formatDate(invoice.issuedate) : '-'}
                              </td>
                            )}
                            {invoicesVisibleColumns.includes('status') && (
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                                  {invoice.status}
                                </span>
                              </td>
                            )}
                            {invoicesVisibleColumns.includes('description') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {invoice.description || '-'}
                              </td>
                            )}
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                              <button
                                onClick={() => {
                                  setSelectedInvoiceForView(invoice)
                                  setShowInvoiceViewModal(true)
                                }}
                                className="text-blue-600 hover:text-blue-900 inline-flex items-center"
                              >
                                <EyeIcon className="h-4 w-4 mr-1" />
                                View
                              </button>
                              {invoice.status.toLowerCase() !== 'paid' && (
                                <button
                                  onClick={() => setShowPaymentModal(true)}
                                  className="text-green-600 hover:text-green-900 inline-flex items-center ml-2"
                                >
                                  <CreditCardIcon className="h-4 w-4 mr-1" />
                                  Pay Now
                                </button>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    )
  }

  function renderPayments() {
    const handleSort = (field: string) => {
      const newOrder = paymentsSortField === field && paymentsSortOrder === 'asc' ? 'desc' : 'asc'
      setPaymentsSortField(field)
      setPaymentsSortOrder(newOrder)
    }

    const filteredPayments = payments.filter(payment =>
      String(payment.id).includes(paymentsSearchQuery) ||
      payment.paymentmethod?.toLowerCase().includes(paymentsSearchQuery.toLowerCase())
    )

    const availableColumns = [
      { key: 'date', label: 'Date' },
      { key: 'amount', label: 'Amount' },
      { key: 'method', label: 'Method' },
      { key: 'invoice', label: 'Invoice' },
      { key: 'status', label: 'Status' },
    ]

    return (
      <div className="space-y-6">
        {/* Enhanced Section Header */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div>
                <h2 className="text-lg font-medium text-gray-900">Payments</h2>
                <p className="mt-1 text-sm text-gray-500">Track your payment history and make new payments</p>
              </div>
              <button
                onClick={() => setShowPaymentModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Payment
              </button>
            </div>

            {/* Controls Bar */}
            <div className="mt-4 flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Search */}
              <div className="flex-1 max-w-lg">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={paymentsSearchQuery}
                    onChange={(e) => setPaymentsSearchQuery(e.target.value)}
                    placeholder="Search payments..."
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center space-x-3">
                {/* Filters */}
                <div className="relative">
                  <button
                    onClick={() => setPaymentsShowFilters(!paymentsShowFilters)}
                    className={`inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium transition-colors ${
                      paymentsShowFilters ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <FunnelIcon className="h-4 w-4 mr-2" />
                    Filters
                  </button>
                </div>

                {/* View Mode */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setPaymentsViewMode('list')}
                    className={`p-2 text-sm font-medium transition-colors ${
                      paymentsViewMode === 'list' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="List view"
                  >
                    <ListBulletIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setPaymentsViewMode('grid')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      paymentsViewMode === 'grid' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Grid view"
                  >
                    <Squares2X2Icon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setPaymentsViewMode('card')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      paymentsViewMode === 'card' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Card view"
                  >
                    <RectangleStackIcon className="h-4 w-4" />
                  </button>
                </div>

                {/* Display Density */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setPaymentsDisplayDensity('compact')}
                    className={`px-3 py-2 text-xs font-medium transition-colors ${
                      paymentsDisplayDensity === 'compact' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Compact
                  </button>
                  <button
                    onClick={() => setPaymentsDisplayDensity('comfortable')}
                    className={`px-3 py-2 text-xs font-medium transition-colors border-l border-gray-300 ${
                      paymentsDisplayDensity === 'comfortable' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Comfortable
                  </button>
                </div>

                {/* Column Controls */}
                <div className="relative">
                  <button
                    onClick={() => setPaymentsShowColumnControls(!paymentsShowColumnControls)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    <TableCellsIcon className="h-4 w-4 mr-2" />
                    Columns
                  </button>
                  {paymentsShowColumnControls && (
                    <div className="absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                      <div className="p-3">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Show columns</h4>
                        <div className="space-y-2">
                          {availableColumns.map((column) => (
                            <label key={column.key} className="flex items-center">
                              <input
                                type="checkbox"
                                checked={paymentsVisibleColumns.includes(column.key)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setPaymentsVisibleColumns(prev => [...prev, column.key])
                                  } else {
                                    setPaymentsVisibleColumns(prev => prev.filter(col => col !== column.key))
                                  }
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="ml-2 text-sm text-gray-700">{column.label}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Summary Line */}
          <div className="px-6 py-3 bg-gray-50 border-b border-gray-200">
            <p className="text-sm text-gray-600">
              Showing {filteredPayments.length} of {payments.length} payments
            </p>
          </div>

          {/* Content */}
          <div className="p-6">
            {filteredPayments.length === 0 ? (
              <div className="text-center py-12">
                <CreditCardIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No payments found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {paymentsSearchQuery ? 'Try adjusting your search' : 'You haven\'t made any payments yet.'}
                </p>
              </div>
            ) : (
              <>
                {/* List View */}
                {paymentsViewMode === 'list' && (
                  <div className="overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {availableColumns
                            .filter(col => paymentsVisibleColumns.includes(col.key))
                            .map((column) => (
                              <th
                                key={column.key}
                                onClick={() => handleSort(column.key)}
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              >
                                <div className="flex items-center space-x-1">
                                  <span>{column.label}</span>
                                  <div className="flex flex-col">
                                    <ChevronUpIcon
                                      className={`h-3 w-3 ${paymentsSortField === column.key && paymentsSortOrder === 'asc' ? 'text-blue-600' : 'text-gray-400'}`}
                                    />
                                    <ChevronDownIcon
                                      className={`h-3 w-3 -mt-1 ${paymentsSortField === column.key && paymentsSortOrder === 'desc' ? 'text-blue-600' : 'text-gray-400'}`}
                                    />
                                  </div>
                                </div>
                              </th>
                            ))}
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredPayments.map((payment) => (
                          <tr key={payment.id} className={paymentsDisplayDensity === 'compact' ? 'h-12' : 'h-16'}>
                            {paymentsVisibleColumns.includes('date') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatDate(payment.paymentdate)}
                              </td>
                            )}
                            {paymentsVisibleColumns.includes('amount') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {formatCurrency(payment.amount)}
                              </td>
                            )}
                            {paymentsVisibleColumns.includes('method') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {payment.paymentmethod}
                              </td>
                            )}
                            {paymentsVisibleColumns.includes('invoice') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                #{payment.invoiceid}
                              </td>
                            )}
                            {paymentsVisibleColumns.includes('status') && (
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                                  {payment.status}
                                </span>
                              </td>
                            )}
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={() => {
                                  setSelectedPaymentForView(payment)
                                  setShowPaymentViewModal(true)
                                }}
                                className="text-blue-600 hover:text-blue-900 inline-flex items-center"
                              >
                                <EyeIcon className="h-4 w-4 mr-1" />
                                View
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    )
  }

  function renderQuotations() {
    const handleSort = (field: string) => {
      const newOrder = quotationsSortField === field && quotationsSortOrder === 'asc' ? 'desc' : 'asc'
      setQuotationsSortField(field)
      setQuotationsSortOrder(newOrder)
    }

    const filteredQuotations = quotations.filter(quotation =>
      quotation.servicename?.toLowerCase().includes(quotationsSearchQuery.toLowerCase()) ||
      quotation.description?.toLowerCase().includes(quotationsSearchQuery.toLowerCase())
    )

    const availableColumns = [
      { key: 'service', label: 'Service' },
      { key: 'description', label: 'Description' },
      { key: 'status', label: 'Status' },
      { key: 'date', label: 'Request Date' },
      { key: 'budget', label: 'Budget Range' },
    ]

    return (
      <div className="space-y-6">
        {/* Enhanced Section Header */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div>
                <h2 className="text-lg font-medium text-gray-900">Quotations</h2>
                <p className="mt-1 text-sm text-gray-500">Request quotes and track your quotation requests</p>
              </div>
              <button
                onClick={() => setShowQuotationModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Request Quote
              </button>
            </div>

            {/* Controls Bar */}
            <div className="mt-4 flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Search */}
              <div className="flex-1 max-w-lg">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={quotationsSearchQuery}
                    onChange={(e) => setQuotationsSearchQuery(e.target.value)}
                    placeholder="Search quotations..."
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center space-x-3">
                {/* Filters */}
                <div className="relative">
                  <button
                    onClick={() => setQuotationsShowFilters(!quotationsShowFilters)}
                    className={`inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium transition-colors ${
                      quotationsShowFilters ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <FunnelIcon className="h-4 w-4 mr-2" />
                    Filters
                  </button>
                </div>

                {/* View Mode */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setQuotationsViewMode('list')}
                    className={`p-2 text-sm font-medium transition-colors ${
                      quotationsViewMode === 'list' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="List view"
                  >
                    <ListBulletIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setQuotationsViewMode('grid')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      quotationsViewMode === 'grid' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Grid view"
                  >
                    <Squares2X2Icon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setQuotationsViewMode('card')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      quotationsViewMode === 'card' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Card view"
                  >
                    <RectangleStackIcon className="h-4 w-4" />
                  </button>
                </div>

                {/* Display Density */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setQuotationsDisplayDensity('compact')}
                    className={`px-3 py-2 text-xs font-medium transition-colors ${
                      quotationsDisplayDensity === 'compact' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Compact
                  </button>
                  <button
                    onClick={() => setQuotationsDisplayDensity('comfortable')}
                    className={`px-3 py-2 text-xs font-medium transition-colors border-l border-gray-300 ${
                      quotationsDisplayDensity === 'comfortable' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Comfortable
                  </button>
                </div>

                {/* Column Controls */}
                <div className="relative">
                  <button
                    onClick={() => setQuotationsShowColumnControls(!quotationsShowColumnControls)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    <TableCellsIcon className="h-4 w-4 mr-2" />
                    Columns
                  </button>
                  {quotationsShowColumnControls && (
                    <div className="absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                      <div className="p-3">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Show columns</h4>
                        <div className="space-y-2">
                          {availableColumns.map((column) => (
                            <label key={column.key} className="flex items-center">
                              <input
                                type="checkbox"
                                checked={quotationsVisibleColumns.includes(column.key)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setQuotationsVisibleColumns(prev => [...prev, column.key])
                                  } else {
                                    setQuotationsVisibleColumns(prev => prev.filter(col => col !== column.key))
                                  }
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="ml-2 text-sm text-gray-700">{column.label}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Summary Line */}
          <div className="px-6 py-3 bg-gray-50 border-b border-gray-200">
            <p className="text-sm text-gray-600">
              Showing {filteredQuotations.length} of {quotations.length} quotations
            </p>
          </div>

          {/* Content */}
          <div className="p-6">
            {filteredQuotations.length === 0 ? (
              <div className="text-center py-12">
                <BanknotesIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No quotations found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {quotationsSearchQuery ? 'Try adjusting your search' : 'You haven\'t requested any quotations yet.'}
                </p>
                <div className="mt-6">
                  <button
                    onClick={() => setShowQuotationModal(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Request Your First Quote
                  </button>
                </div>
              </div>
            ) : (
              <>
                {/* List View */}
                {quotationsViewMode === 'list' && (
                  <div className="overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {availableColumns
                            .filter(col => quotationsVisibleColumns.includes(col.key))
                            .map((column) => (
                              <th
                                key={column.key}
                                onClick={() => handleSort(column.key)}
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              >
                                <div className="flex items-center space-x-1">
                                  <span>{column.label}</span>
                                  <div className="flex flex-col">
                                    <ChevronUpIcon
                                      className={`h-3 w-3 ${quotationsSortField === column.key && quotationsSortOrder === 'asc' ? 'text-blue-600' : 'text-gray-400'}`}
                                    />
                                    <ChevronDownIcon
                                      className={`h-3 w-3 -mt-1 ${quotationsSortField === column.key && quotationsSortOrder === 'desc' ? 'text-blue-600' : 'text-gray-400'}`}
                                    />
                                  </div>
                                </div>
                              </th>
                            ))}
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredQuotations.map((quotation) => (
                          <tr key={quotation.id} className={quotationsDisplayDensity === 'compact' ? 'h-12' : 'h-16'}>
                            {quotationsVisibleColumns.includes('service') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {quotation.servicename}
                              </td>
                            )}
                            {quotationsVisibleColumns.includes('description') && (
                              <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                                {quotation.description || '-'}
                              </td>
                            )}
                            {quotationsVisibleColumns.includes('status') && (
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(quotation.status)}`}>
                                  {quotation.status}
                                </span>
                              </td>
                            )}
                            {quotationsVisibleColumns.includes('date') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {quotation.requestdate ? formatDate(quotation.requestdate) : '-'}
                              </td>
                            )}
                            {quotationsVisibleColumns.includes('budget') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {quotation.budgetrange || '-'}
                              </td>
                            )}
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={() => {
                                  setSelectedQuotationForView(quotation)
                                  setShowQuotationViewModal(true)
                                }}
                                className="text-blue-600 hover:text-blue-900 inline-flex items-center"
                              >
                                <EyeIcon className="h-4 w-4 mr-1" />
                                View
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    )
  }

  function renderMessages() {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Messages</h2>
          <p className="mt-1 text-sm text-gray-500">Communication with your team</p>
        </div>
        <div className="p-6">
          {messages.length === 0 ? (
            <div className="text-center py-12">
              <ChatBubbleLeftIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No messages</h3>
              <p className="mt-1 text-sm text-gray-500">You don't have any messages yet.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((message) => (
                <div key={message.id} className={`p-4 rounded-lg border ${message.isread ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'}`}>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <p className="text-sm text-gray-900">{message.content}</p>
                      <p className="text-xs text-gray-500 mt-1">{formatDate(message.createdat)}</p>
                    </div>
                    {!message.isread && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        New
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    )
  }

  function renderSettings() {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Account Settings</h2>
            <p className="mt-1 text-sm text-gray-500">Manage your account preferences</p>
          </div>
          <div className="p-6">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Profile Information</h3>
                <button
                  onClick={() => setShowProfileModal(true)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <UserIcon className="h-4 w-4 mr-2" />
                  Edit Profile
                </button>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Security</h3>
                <button
                  onClick={() => setShowPasswordModal(true)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <CogIcon className="h-4 w-4 mr-2" />
                  Change Password
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }
}
