-- AlterTable
ALTER TABLE "contactforms" ADD COLUMN     "attachments" TEXT,
ADD COLUMN     "contenttype" TEXT NOT NULL DEFAULT 'text',
ADD COLUMN     "deliveredat" TIMESTAMPTZ(6),
ADD COLUMN     "edithistory" TEXT,
ADD COLUMN     "isdelivered" BOOLEAN DEFAULT false,
ADD COLUMN     "messagetype" TEXT NOT NULL DEFAULT 'contact',
ADD COLUMN     "metadata" TEXT,
ADD COLUMN     "parentid" BIGINT,
ADD COLUMN     "receiverid" BIGINT,
ADD COLUMN     "senderid" BIGINT,
ADD COLUMN     "threadid" BIGINT;

-- CreateTable
CREATE TABLE "quotationrequests" (
    "id" BIGSERIAL NOT NULL,
    "clientid" BIGINT NOT NULL,
    "serviceid" TEXT NOT NULL,
    "servicename" TEXT NOT NULL,
    "selectedoptions" TEXT[],
    "selectedfeatures" TEXT[],
    "budget" DECIMAL(10,2) NOT NULL,
    "timeline" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "contactpreference" TEXT NOT NULL,
    "urgency" TEXT NOT NULL,
    "estimatedcost" DECIMAL(10,2),
    "servicedetails" JSONB,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "requestdate" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "responsedate" TIMESTAMPTZ(6),
    "quotedamount" DECIMAL(10,2),
    "notes" TEXT,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "quotationrequests_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "quotationrequests_clientid_idx" ON "quotationrequests"("clientid");

-- CreateIndex
CREATE INDEX "quotationrequests_status_idx" ON "quotationrequests"("status");

-- CreateIndex
CREATE INDEX "quotationrequests_requestdate_idx" ON "quotationrequests"("requestdate");

-- CreateIndex
CREATE INDEX "ix_contactforms_threadid" ON "contactforms"("threadid");

-- CreateIndex
CREATE INDEX "ix_contactforms_senderid" ON "contactforms"("senderid");

-- CreateIndex
CREATE INDEX "ix_contactforms_receiverid" ON "contactforms"("receiverid");

-- CreateIndex
CREATE INDEX "ix_contactforms_messagetype" ON "contactforms"("messagetype");

-- CreateIndex
CREATE INDEX "ix_contactforms_createdat" ON "contactforms"("createdat");

-- AddForeignKey
ALTER TABLE "contactforms" ADD CONSTRAINT "contactforms_parentid_fkey" FOREIGN KEY ("parentid") REFERENCES "contactforms"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "contactforms" ADD CONSTRAINT "contactforms_senderid_fkey" FOREIGN KEY ("senderid") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "contactforms" ADD CONSTRAINT "contactforms_receiverid_fkey" FOREIGN KEY ("receiverid") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "quotationrequests" ADD CONSTRAINT "quotationrequests_clientid_fkey" FOREIGN KEY ("clientid") REFERENCES "clients"("id") ON DELETE CASCADE ON UPDATE CASCADE;
