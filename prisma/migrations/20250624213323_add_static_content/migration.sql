-- CreateEnum
CREATE TYPE "userrole" AS ENUM ('USER', 'ADMIN', 'MODERATOR');

-- CreateTable
CREATE TABLE "aboutpages" (
    "id" BIGSERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "metadescription" TEXT,
    "metakeywords" TEXT,
    "herotitle" TEXT NOT NULL,
    "herosubtitle" TEXT NOT NULL,
    "heroimageurl" TEXT,
    "storytitle" TEXT NOT NULL,
    "storysubtitle" TEXT NOT NULL,
    "storycontent" TEXT NOT NULL,
    "storyimageurl" TEXT,
    "stat1number" TEXT,
    "stat1label" TEXT,
    "stat2number" TEXT,
    "stat2label" TEXT,
    "stat3number" TEXT,
    "stat3label" TEXT,
    "stat4number" TEXT,
    "stat4label" TEXT,
    "missiontitle" TEXT,
    "missioncontent" TEXT,
    "visiontitle" TEXT,
    "visioncontent" TEXT,
    "valuestitle" TEXT,
    "valuescontent" TEXT,
    "ctatitle" TEXT,
    "ctasubtitle" TEXT,
    "ctaprimarybuttontext" TEXT,
    "ctaprimarybuttonurl" TEXT,
    "ctasecondarybuttontext" TEXT,
    "ctasecondarybuttonurl" TEXT,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "modifiedby" TEXT,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "aboutpages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "aboutpagesections" (
    "id" BIGSERIAL NOT NULL,
    "aboutpageid" BIGINT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "iconclass" TEXT,
    "imageurl" TEXT,
    "displayorder" INTEGER NOT NULL DEFAULT 0,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "sectiontype" TEXT NOT NULL DEFAULT 'content',
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "aboutpagesections_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "blogposts" (
    "id" BIGSERIAL NOT NULL,
    "authorid" TEXT,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "featuredimageurl" TEXT,
    "excerpt" TEXT,
    "ispublished" BOOLEAN DEFAULT false,
    "publishedat" TIMESTAMPTZ(6),
    "categories" TEXT,
    "tags" TEXT,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "blogposts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "categories" (
    "id" BIGSERIAL NOT NULL,
    "categname" VARCHAR(50) NOT NULL,
    "categdesc" TEXT,
    "parentid" INTEGER NOT NULL DEFAULT 0,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "displayorder" INTEGER DEFAULT 0,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "chatbotintents" (
    "id" BIGSERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "displayname" TEXT NOT NULL,
    "description" TEXT,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "priority" INTEGER DEFAULT 1,
    "displayorder" INTEGER NOT NULL DEFAULT 0,
    "iconclass" TEXT,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "chatbotintents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "chatbotkeywords" (
    "id" BIGSERIAL NOT NULL,
    "keyword" TEXT NOT NULL,
    "synonyms" TEXT,
    "weight" INTEGER NOT NULL DEFAULT 1,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "matchtype" TEXT NOT NULL DEFAULT 'contains',
    "chatbotintentid" BIGINT NOT NULL,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "chatbotkeywords_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "chatbotquickactions" (
    "id" BIGSERIAL NOT NULL,
    "text" TEXT NOT NULL,
    "actiontype" TEXT NOT NULL DEFAULT 'message',
    "actionvalue" TEXT NOT NULL,
    "url" TEXT,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "displayorder" INTEGER NOT NULL DEFAULT 0,
    "cssclass" TEXT,
    "chatbotresponseid" BIGINT,
    "chatbotintentid" BIGINT,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "chatbotquickactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "chatbotresponses" (
    "id" BIGSERIAL NOT NULL,
    "responsetext" TEXT NOT NULL,
    "responsetype" TEXT NOT NULL DEFAULT 'text',
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "displayorder" INTEGER NOT NULL DEFAULT 0,
    "conditions" TEXT,
    "templatevariables" TEXT,
    "chatbotintentid" BIGINT NOT NULL,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "chatbotresponses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "clients" (
    "id" BIGSERIAL NOT NULL,
    "userid" BIGINT,
    "companyname" TEXT NOT NULL,
    "contactname" TEXT NOT NULL,
    "contactposition" TEXT,
    "contactemail" TEXT NOT NULL,
    "contactphone" TEXT,
    "contactfax" TEXT,
    "companywebsite" TEXT,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "zipcode" TEXT,
    "country" TEXT,
    "logourl" TEXT,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "notes" TEXT,

    CONSTRAINT "clients_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "contactforms" (
    "id" BIGSERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone" TEXT,
    "subject" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "isread" BOOLEAN DEFAULT false,
    "readat" TIMESTAMPTZ(6),
    "status" TEXT NOT NULL DEFAULT 'New',
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "contactforms_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "contracts" (
    "id" BIGSERIAL NOT NULL,
    "contname" TEXT NOT NULL,
    "projid" BIGINT NOT NULL,
    "clientid" BIGINT NOT NULL,
    "orderid" BIGINT NOT NULL,
    "contmanager" BIGINT,
    "contservtype" TEXT,
    "contlang" TEXT,
    "agreementdesc" TEXT,
    "contvalue" DOUBLE PRECISION,
    "contvaluecurr" TEXT,
    "billingtype" TEXT,
    "nextbilldate" TIMESTAMPTZ(6),
    "contsignmethod" TEXT,
    "contsigneddate" TIMESTAMPTZ(6),
    "contexecuteddate" TIMESTAMPTZ(6),
    "contexpirydate" TIMESTAMPTZ(6),
    "contstatus" TEXT DEFAULT 'DRAFT',
    "lastupdatedate" TIMESTAMPTZ(6),
    "lastupdateuser" BIGINT,
    "contfile" TEXT,
    "fileuploaddate" TIMESTAMPTZ(6),
    "comments" TEXT,
    "notes" TEXT,
    "createdat" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "contracts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "datauploadlogs" (
    "id" BIGSERIAL NOT NULL,
    "filename" TEXT,
    "entitytype" TEXT,
    "operation" TEXT,
    "recordcount" INTEGER,
    "status" TEXT,
    "errorlog" TEXT,
    "uploadedby" TEXT,
    "createdat" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "datauploadlogs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feedbacks" (
    "id" BIGSERIAL NOT NULL,
    "subject" TEXT NOT NULL,
    "comment" TEXT NOT NULL,
    "feedbacktype" TEXT NOT NULL,
    "rating" INTEGER,
    "clientname" TEXT,
    "clientemail" TEXT NOT NULL,
    "priority" TEXT NOT NULL DEFAULT 'Medium',
    "status" TEXT NOT NULL DEFAULT 'New',
    "isread" BOOLEAN DEFAULT false,
    "readat" TIMESTAMPTZ(6),
    "resolvedat" TIMESTAMPTZ(6),
    "adminresponse" TEXT,
    "adminname" TEXT,
    "responsedate" TIMESTAMPTZ(6),
    "clientid" BIGINT,
    "projectid" BIGINT,
    "ispublic" BOOLEAN DEFAULT false,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "feedbacks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "herosections" (
    "id" BIGSERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "metadescription" TEXT,
    "metakeywords" TEXT,
    "pagename" TEXT NOT NULL,
    "maintitle" TEXT NOT NULL,
    "mainsubtitle" TEXT NOT NULL,
    "maindescription" TEXT,
    "primarybuttontext" TEXT,
    "primarybuttonurl" TEXT,
    "secondarybuttontext" TEXT,
    "secondarybuttonurl" TEXT,
    "enableslideshow" INTEGER NOT NULL DEFAULT 1,
    "slideshowspeed" INTEGER NOT NULL DEFAULT 5000,
    "autoplay" INTEGER NOT NULL DEFAULT 1,
    "showdots" INTEGER NOT NULL DEFAULT 1,
    "showarrows" INTEGER NOT NULL DEFAULT 1,
    "enablefloatingelements" INTEGER NOT NULL DEFAULT 1,
    "floatingelementsconfig" TEXT,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "modifiedby" TEXT,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "herosections_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "heroslides" (
    "id" BIGSERIAL NOT NULL,
    "herosectionid" BIGINT NOT NULL,
    "content" TEXT NOT NULL,
    "mediatype" TEXT NOT NULL DEFAULT 'image',
    "imageurl" TEXT,
    "videourl" TEXT,
    "mediaalt" TEXT,
    "videoautoplay" INTEGER NOT NULL DEFAULT 1,
    "videomuted" INTEGER NOT NULL DEFAULT 1,
    "videoloop" INTEGER NOT NULL DEFAULT 1,
    "videocontrols" INTEGER NOT NULL DEFAULT 0,
    "buttontext" TEXT,
    "buttonurl" TEXT,
    "displayorder" INTEGER NOT NULL DEFAULT 0,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "animationtype" TEXT DEFAULT 'fade',
    "duration" INTEGER NOT NULL DEFAULT 5000,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "heroslides_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "invoiceitems" (
    "id" BIGSERIAL NOT NULL,
    "description" TEXT NOT NULL,
    "quantity" DECIMAL(18,2) NOT NULL,
    "unitprice" DECIMAL(18,2) NOT NULL,
    "totalprice" DECIMAL(18,2) NOT NULL,
    "invoiceid" BIGINT NOT NULL,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "invoiceitems_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "invoices" (
    "id" BIGSERIAL NOT NULL,
    "duedate" TIMESTAMPTZ(6) NOT NULL,
    "subtotal" DECIMAL,
    "taxrate" DECIMAL(5,2) NOT NULL DEFAULT 0,
    "taxamount" DECIMAL(18,2) NOT NULL DEFAULT 0,
    "totalamount" DECIMAL(18,2) NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'Pending',
    "description" TEXT,
    "clientid" BIGINT NOT NULL,
    "contid" BIGINT NOT NULL,
    "orderid" BIGINT NOT NULL,
    "projectid" BIGINT,
    "paidat" TIMESTAMPTZ(6),
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "invoices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "jobapplications" (
    "id" BIGSERIAL NOT NULL,
    "applicantname" TEXT NOT NULL,
    "applicantemail" TEXT NOT NULL,
    "applicantphone" TEXT,
    "resumeurl" TEXT,
    "coverletter" TEXT,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "notes" TEXT,
    "joblistingid" BIGINT NOT NULL,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "jobapplications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "joblistings" (
    "id" BIGSERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "requirements" TEXT NOT NULL,
    "location" TEXT NOT NULL,
    "employmenttype" TEXT NOT NULL,
    "salarymin" DECIMAL(18,2),
    "salarymax" DECIMAL(18,2),
    "salarycurrency" TEXT DEFAULT 'USD',
    "isremote" BOOLEAN DEFAULT false,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "expiresat" TIMESTAMPTZ(6),
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "joblistings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "legalpages" (
    "id" BIGSERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "metadescription" TEXT,
    "content" TEXT NOT NULL,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "displayorder" INTEGER DEFAULT 0,
    "lastmodified" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "modifiedby" TEXT,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "legalpages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "legalpagesections" (
    "id" BIGSERIAL NOT NULL,
    "legalpageid" BIGINT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "iconclass" TEXT,
    "displayorder" INTEGER NOT NULL DEFAULT 0,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "legalpagesections_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "messages" (
    "id" BIGSERIAL NOT NULL,
    "content" TEXT NOT NULL,
    "sendername" TEXT NOT NULL,
    "senderrole" TEXT NOT NULL,
    "senderid" TEXT NOT NULL,
    "isread" BOOLEAN DEFAULT false,
    "readat" TIMESTAMPTZ(6),
    "projectid" BIGINT NOT NULL,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "messages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "orderdetails" (
    "id" BIGSERIAL NOT NULL,
    "orderid" BIGINT NOT NULL,
    "servid" BIGINT NOT NULL,
    "optid" BIGINT,
    "featid" BIGINT,
    "costeach" DOUBLE PRECISION,
    "discountrate" INTEGER,
    "totaldiscount" DOUBLE PRECISION,
    "comments" TEXT,
    "notes" TEXT,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "createdat" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "orderdetails_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "orders" (
    "id" BIGSERIAL NOT NULL,
    "ordertitle" VARCHAR(40) NOT NULL,
    "clientid" BIGINT NOT NULL,
    "ordermanager" BIGINT,
    "orderdesc" TEXT,
    "orderdate" TIMESTAMPTZ(6),
    "ordertotalamount" DECIMAL,
    "orderdiscountrate" INTEGER,
    "ordertotaldiscount" DOUBLE PRECISION,
    "status" TEXT,
    "notes" TEXT,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "createdat" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "orders_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "payments" (
    "id" BIGSERIAL NOT NULL,
    "amount" DECIMAL(18,2) NOT NULL,
    "paymentdate" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "paymentmethod" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'Completed',
    "notes" TEXT,
    "reference" TEXT,
    "transactionid" TEXT,
    "processingfee" DECIMAL(18,2),
    "invoiceid" BIGINT NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "promocode" TEXT,
    "discount" DECIMAL(18,2) DEFAULT 0,
    "emailreceipt" BOOLEAN NOT NULL DEFAULT false,
    "receiptemail" TEXT,
    "termsaccepted" BOOLEAN NOT NULL DEFAULT true,
    "paymentdetails" JSONB,
    "stripepaymentintentid" TEXT,
    "stripeclientsecret" TEXT,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "payments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "payrollrecords" (
    "id" BIGSERIAL NOT NULL,
    "teammemberid" BIGINT NOT NULL,
    "paydate" TIMESTAMPTZ(6),
    "workhours" INTEGER,
    "payperiod" TEXT,
    "basesalary" DECIMAL,
    "payrate" DOUBLE PRECISION,
    "grosspay" DOUBLE PRECISION,
    "taxes" DOUBLE PRECISION,
    "overtime" DECIMAL DEFAULT 0,
    "bonuses" DECIMAL DEFAULT 0,
    "deduction" DOUBLE PRECISION,
    "netpay" DOUBLE PRECISION,
    "paymethod" VARCHAR(10),
    "status" VARCHAR(10),
    "notes" TEXT,
    "createdat" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "payrollrecords_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "projectdocuments" (
    "id" BIGSERIAL NOT NULL,
    "filename" TEXT NOT NULL,
    "fileurl" TEXT NOT NULL,
    "filetype" TEXT NOT NULL,
    "filesize" INTEGER NOT NULL,
    "uploadedbyid" TEXT NOT NULL,
    "uploadedbyname" TEXT NOT NULL,
    "projectid" BIGINT NOT NULL,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "projectdocuments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "projects" (
    "id" BIGSERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "projgoals" TEXT,
    "projmanager" BIGINT,
    "clientid" BIGINT,
    "orderid" BIGINT NOT NULL,
    "imageurl" TEXT,
    "projecturl" TEXT,
    "githuburl" TEXT,
    "tags" TEXT,
    "projstartdate" TIMESTAMPTZ(6),
    "projcompletiondate" TIMESTAMPTZ(6),
    "estimatecost" DOUBLE PRECISION,
    "estimatetime" TEXT,
    "estimateeffort" TEXT,
    "status" TEXT,
    "isfeatured" BOOLEAN DEFAULT false,
    "ispublic" BOOLEAN DEFAULT false,
    "displayorder" INTEGER NOT NULL DEFAULT 0,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "projects_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "projecttechnologies" (
    "id" BIGSERIAL NOT NULL,
    "projectsid" BIGINT NOT NULL,
    "technologiesid" BIGINT NOT NULL,

    CONSTRAINT "projecttechnologies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "serviceoptionfeatures" (
    "id" BIGSERIAL NOT NULL,
    "optid" BIGINT NOT NULL,
    "featname" VARCHAR(50) NOT NULL,
    "featcost" DOUBLE PRECISION,
    "featdiscountrate" INTEGER,
    "feattotaldiscount" DOUBLE PRECISION,
    "isincluded" BOOLEAN DEFAULT true,
    "createdat" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),
    "featdesc" TEXT,

    CONSTRAINT "serviceoptionfeatures_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "serviceoptions" (
    "id" BIGSERIAL NOT NULL,
    "servid" BIGINT NOT NULL,
    "optname" VARCHAR(50) NOT NULL,
    "optprice" DECIMAL,
    "optdiscountrate" INTEGER,
    "opttotaldiscount" DOUBLE PRECISION,
    "optdesc" TEXT,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "createdat" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "serviceoptions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "services" (
    "id" BIGSERIAL NOT NULL,
    "categid" BIGINT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "iconclass" TEXT,
    "price" DECIMAL(18,2) NOT NULL,
    "discountrate" INTEGER,
    "totaldiscount" DOUBLE PRECISION,
    "manager" TEXT,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "displayorder" INTEGER NOT NULL DEFAULT 0,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "services_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sitesettings" (
    "id" BIGSERIAL NOT NULL,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "category" TEXT NOT NULL DEFAULT 'GENERAL',
    "ispublic" BOOLEAN DEFAULT true,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "description" TEXT,
    "icon" TEXT,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "sitesettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tasks" (
    "id" BIGSERIAL NOT NULL,
    "projno" BIGINT NOT NULL,
    "teammemberid" BIGINT NOT NULL,
    "taskdesc" TEXT,
    "taskstartdate" TIMESTAMPTZ(6),
    "taskenddate" TIMESTAMPTZ(6),
    "workhours" INTEGER,
    "payrate" DOUBLE PRECISION,
    "status" VARCHAR(10),
    "notes" TEXT,
    "createdat" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "tasks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "teammembers" (
    "id" BIGSERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "position" TEXT NOT NULL,
    "birthdate" TIMESTAMPTZ(6),
    "gender" TEXT,
    "maritalstatus" TEXT,
    "socialsecurityno" TEXT,
    "hiredate" TIMESTAMPTZ(6),
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "zipcode" TEXT,
    "country" TEXT,
    "phone" TEXT NOT NULL,
    "salary" DOUBLE PRECISION,
    "payrollmethod" TEXT,
    "empresumeurl" TEXT,
    "notes" TEXT,
    "bio" TEXT,
    "photourl" TEXT,
    "email" TEXT,
    "linkedinurl" TEXT,
    "twitterurl" TEXT,
    "githuburl" TEXT,
    "displayorder" INTEGER NOT NULL DEFAULT 0,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "teammembers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "technologies" (
    "id" BIGSERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "iconurl" TEXT,
    "displayorder" INTEGER NOT NULL DEFAULT 0,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "technologies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "testimonials" (
    "id" BIGSERIAL NOT NULL,
    "clientid" BIGINT NOT NULL,
    "clientname" TEXT NOT NULL,
    "clienttitle" TEXT NOT NULL,
    "clientcompany" TEXT NOT NULL,
    "clientphotourl" TEXT,
    "content" TEXT NOT NULL,
    "rating" INTEGER NOT NULL DEFAULT 5,
    "isfeatured" BOOLEAN DEFAULT false,
    "displayorder" INTEGER NOT NULL DEFAULT 0,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "testimonials_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "id" BIGSERIAL NOT NULL,
    "email" TEXT NOT NULL,
    "emailverified" TIMESTAMPTZ(6),
    "password" TEXT,
    "firstname" TEXT,
    "lastname" TEXT,
    "imageurl" TEXT,
    "role" "userrole" NOT NULL DEFAULT 'USER',
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Account" (
    "id" TEXT NOT NULL,
    "userId" BIGINT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,

    CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Session" (
    "id" TEXT NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "userId" BIGINT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VerificationToken" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL
);

-- CreateTable
CREATE TABLE "auditlogs" (
    "id" BIGSERIAL NOT NULL,
    "userid" BIGINT,
    "action" TEXT NOT NULL,
    "resource" TEXT,
    "resourceid" TEXT,
    "details" TEXT,
    "ipaddress" TEXT,
    "useragent" TEXT,
    "success" BOOLEAN NOT NULL DEFAULT true,
    "errormessage" TEXT,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "auditlogs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "staticcontent" (
    "id" BIGSERIAL NOT NULL,
    "page" TEXT NOT NULL,
    "section" TEXT NOT NULL,
    "contentkey" TEXT NOT NULL,
    "contenttype" TEXT NOT NULL DEFAULT 'text',
    "content" TEXT NOT NULL,
    "displayorder" INTEGER NOT NULL DEFAULT 0,
    "isactive" BOOLEAN NOT NULL DEFAULT true,
    "createdat" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedat" TIMESTAMPTZ(6),

    CONSTRAINT "staticcontent_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ix_aboutpagesections_aboutpageid" ON "aboutpagesections"("aboutpageid");

-- CreateIndex
CREATE UNIQUE INDEX "blogposts_slug_key" ON "blogposts"("slug");

-- CreateIndex
CREATE INDEX "ix_blogposts_ispublished" ON "blogposts"("ispublished");

-- CreateIndex
CREATE INDEX "ix_blogposts_publishedat" ON "blogposts"("publishedat");

-- CreateIndex
CREATE INDEX "ix_chatbotkeywords_chatbotintentid" ON "chatbotkeywords"("chatbotintentid");

-- CreateIndex
CREATE INDEX "ix_chatbotquickactions_chatbotintentid" ON "chatbotquickactions"("chatbotintentid");

-- CreateIndex
CREATE INDEX "ix_chatbotquickactions_chatbotresponseid" ON "chatbotquickactions"("chatbotresponseid");

-- CreateIndex
CREATE INDEX "ix_chatbotresponses_chatbotintentid" ON "chatbotresponses"("chatbotintentid");

-- CreateIndex
CREATE INDEX "ix_contactforms_status" ON "contactforms"("status");

-- CreateIndex
CREATE INDEX "ix_feedbacks_clientid" ON "feedbacks"("clientid");

-- CreateIndex
CREATE INDEX "ix_feedbacks_projectid" ON "feedbacks"("projectid");

-- CreateIndex
CREATE INDEX "ix_feedbacks_status" ON "feedbacks"("status");

-- CreateIndex
CREATE INDEX "ix_heroslides_herosectionid" ON "heroslides"("herosectionid");

-- CreateIndex
CREATE INDEX "ix_invoiceitems_invoiceid" ON "invoiceitems"("invoiceid");

-- CreateIndex
CREATE INDEX "ix_invoices_clientid" ON "invoices"("clientid");

-- CreateIndex
CREATE INDEX "ix_invoices_duedate" ON "invoices"("duedate");

-- CreateIndex
CREATE INDEX "ix_invoices_orderid" ON "invoices"("orderid");

-- CreateIndex
CREATE INDEX "ix_invoices_status" ON "invoices"("status");

-- CreateIndex
CREATE INDEX "ix_jobapplications_joblistingid" ON "jobapplications"("joblistingid");

-- CreateIndex
CREATE INDEX "ix_joblistings_expiresat" ON "joblistings"("expiresat");

-- CreateIndex
CREATE INDEX "ix_joblistings_isactive" ON "joblistings"("isactive");

-- CreateIndex
CREATE UNIQUE INDEX "legalpages_slug_key" ON "legalpages"("slug");

-- CreateIndex
CREATE INDEX "ix_legalpagesections_legalpageid" ON "legalpagesections"("legalpageid");

-- CreateIndex
CREATE INDEX "ix_messages_projectid" ON "messages"("projectid");

-- CreateIndex
CREATE INDEX "ix_payments_invoiceid" ON "payments"("invoiceid");

-- CreateIndex
CREATE INDEX "ix_payments_paymentmethod" ON "payments"("paymentmethod");

-- CreateIndex
CREATE INDEX "ix_payments_status" ON "payments"("status");

-- CreateIndex
CREATE INDEX "ix_projectdocuments_projectid" ON "projectdocuments"("projectid");

-- CreateIndex
CREATE INDEX "ix_projects_clientid" ON "projects"("clientid");

-- CreateIndex
CREATE INDEX "ix_projects_isfeatured" ON "projects"("isfeatured");

-- CreateIndex
CREATE INDEX "ix_projects_orderid" ON "projects"("orderid");

-- CreateIndex
CREATE INDEX "ix_projects_projcompletiondate" ON "projects"("projcompletiondate");

-- CreateIndex
CREATE INDEX "ix_services_isactive" ON "services"("isactive");

-- CreateIndex
CREATE UNIQUE INDEX "sitesettings_key_key" ON "sitesettings"("key");

-- CreateIndex
CREATE INDEX "ix_teammembers_isactive" ON "teammembers"("isactive");

-- CreateIndex
CREATE INDEX "ix_testimonials_isfeatured" ON "testimonials"("isfeatured");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Account_provider_providerAccountId_key" ON "Account"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "Session_sessionToken_key" ON "Session"("sessionToken");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_token_key" ON "VerificationToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_identifier_token_key" ON "VerificationToken"("identifier", "token");

-- CreateIndex
CREATE INDEX "auditlogs_userid_idx" ON "auditlogs"("userid");

-- CreateIndex
CREATE INDEX "auditlogs_action_idx" ON "auditlogs"("action");

-- CreateIndex
CREATE INDEX "auditlogs_resource_idx" ON "auditlogs"("resource");

-- CreateIndex
CREATE INDEX "auditlogs_createdat_idx" ON "auditlogs"("createdat");

-- CreateIndex
CREATE INDEX "staticcontent_page_section_idx" ON "staticcontent"("page", "section");

-- CreateIndex
CREATE INDEX "staticcontent_isactive_idx" ON "staticcontent"("isactive");

-- CreateIndex
CREATE UNIQUE INDEX "staticcontent_page_section_contentkey_key" ON "staticcontent"("page", "section", "contentkey");

-- AddForeignKey
ALTER TABLE "aboutpagesections" ADD CONSTRAINT "aboutpagesections_aboutpageid_fkey" FOREIGN KEY ("aboutpageid") REFERENCES "aboutpages"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "chatbotkeywords" ADD CONSTRAINT "chatbotkeywords_chatbotintentid_fkey" FOREIGN KEY ("chatbotintentid") REFERENCES "chatbotintents"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "chatbotquickactions" ADD CONSTRAINT "chatbotquickactions_chatbotintentid_fkey" FOREIGN KEY ("chatbotintentid") REFERENCES "chatbotintents"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "chatbotquickactions" ADD CONSTRAINT "chatbotquickactions_chatbotresponseid_fkey" FOREIGN KEY ("chatbotresponseid") REFERENCES "chatbotresponses"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "chatbotresponses" ADD CONSTRAINT "chatbotresponses_chatbotintentid_fkey" FOREIGN KEY ("chatbotintentid") REFERENCES "chatbotintents"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "clients" ADD CONSTRAINT "clients_userid_fkey" FOREIGN KEY ("userid") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "contracts" ADD CONSTRAINT "contracts_clientid_fkey" FOREIGN KEY ("clientid") REFERENCES "clients"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "contracts" ADD CONSTRAINT "contracts_contmanager_fkey" FOREIGN KEY ("contmanager") REFERENCES "teammembers"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "contracts" ADD CONSTRAINT "contracts_lastupdateuser_fkey" FOREIGN KEY ("lastupdateuser") REFERENCES "teammembers"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "contracts" ADD CONSTRAINT "contracts_orderid_fkey" FOREIGN KEY ("orderid") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "contracts" ADD CONSTRAINT "contracts_projid_fkey" FOREIGN KEY ("projid") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "feedbacks" ADD CONSTRAINT "feedbacks_clientid_fkey" FOREIGN KEY ("clientid") REFERENCES "clients"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "feedbacks" ADD CONSTRAINT "feedbacks_projectid_fkey" FOREIGN KEY ("projectid") REFERENCES "projects"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "heroslides" ADD CONSTRAINT "heroslides_herosectionid_fkey" FOREIGN KEY ("herosectionid") REFERENCES "herosections"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "invoiceitems" ADD CONSTRAINT "invoiceitems_invoiceid_fkey" FOREIGN KEY ("invoiceid") REFERENCES "invoices"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_clientid_fkey" FOREIGN KEY ("clientid") REFERENCES "clients"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_contid_fkey" FOREIGN KEY ("contid") REFERENCES "contracts"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_orderid_fkey" FOREIGN KEY ("orderid") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_projectid_fkey" FOREIGN KEY ("projectid") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "jobapplications" ADD CONSTRAINT "jobapplications_joblistingid_fkey" FOREIGN KEY ("joblistingid") REFERENCES "joblistings"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "legalpagesections" ADD CONSTRAINT "legalpagesections_legalpageid_fkey" FOREIGN KEY ("legalpageid") REFERENCES "legalpages"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "messages" ADD CONSTRAINT "messages_projectid_fkey" FOREIGN KEY ("projectid") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "orderdetails" ADD CONSTRAINT "orderdetails_featid_fkey" FOREIGN KEY ("featid") REFERENCES "serviceoptionfeatures"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "orderdetails" ADD CONSTRAINT "orderdetails_optid_fkey" FOREIGN KEY ("optid") REFERENCES "serviceoptions"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "orderdetails" ADD CONSTRAINT "orderdetails_orderid_fkey" FOREIGN KEY ("orderid") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "orderdetails" ADD CONSTRAINT "orderdetails_servid_fkey" FOREIGN KEY ("servid") REFERENCES "services"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "orders" ADD CONSTRAINT "orders_clientid_fkey" FOREIGN KEY ("clientid") REFERENCES "clients"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "orders" ADD CONSTRAINT "orders_ordermanager_fkey" FOREIGN KEY ("ordermanager") REFERENCES "teammembers"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "payments" ADD CONSTRAINT "payments_invoiceid_fkey" FOREIGN KEY ("invoiceid") REFERENCES "invoices"("id") ON DELETE RESTRICT ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "payrollrecords" ADD CONSTRAINT "payrollrecords_teammemberid_fkey" FOREIGN KEY ("teammemberid") REFERENCES "teammembers"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "projectdocuments" ADD CONSTRAINT "projectdocuments_projectid_fkey" FOREIGN KEY ("projectid") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "projects" ADD CONSTRAINT "projects_clientid_fkey" FOREIGN KEY ("clientid") REFERENCES "clients"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "projects" ADD CONSTRAINT "projects_orderid_fkey" FOREIGN KEY ("orderid") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "projects" ADD CONSTRAINT "projects_projmanager_fkey" FOREIGN KEY ("projmanager") REFERENCES "teammembers"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "projecttechnologies" ADD CONSTRAINT "projecttechnologies_projectsid_fkey" FOREIGN KEY ("projectsid") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "projecttechnologies" ADD CONSTRAINT "projecttechnologies_technologiesid_fkey" FOREIGN KEY ("technologiesid") REFERENCES "technologies"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "serviceoptionfeatures" ADD CONSTRAINT "serviceoptionfeatures_optid_fkey" FOREIGN KEY ("optid") REFERENCES "serviceoptions"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "serviceoptions" ADD CONSTRAINT "serviceoptions_servid_fkey" FOREIGN KEY ("servid") REFERENCES "services"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "services" ADD CONSTRAINT "services_categid_fkey" FOREIGN KEY ("categid") REFERENCES "categories"("id") ON DELETE RESTRICT ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_projno_fkey" FOREIGN KEY ("projno") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_teammemberid_fkey" FOREIGN KEY ("teammemberid") REFERENCES "teammembers"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "testimonials" ADD CONSTRAINT "testimonials_clientid_fkey" FOREIGN KEY ("clientid") REFERENCES "clients"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "auditlogs" ADD CONSTRAINT "auditlogs_userid_fkey" FOREIGN KEY ("userid") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
