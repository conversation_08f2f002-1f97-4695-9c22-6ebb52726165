import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedStaticContent() {
  console.log('🌱 Seeding static content...')

  // Clear existing static content
  await prisma.staticcontent.deleteMany()

  // Home page static content
  const homeContent = [
    // Hero Section
    { page: 'home', section: 'hero', contentkey: 'badge_text', content: 'Crafting Digital Excellence Since 2014' },
    { page: 'home', section: 'hero', contentkey: 'title', content: 'Build the Future' },
    { page: 'home', section: 'hero', contentkey: 'title_highlight', content: 'Today' },
    { page: 'home', section: 'hero', contentkey: 'subtitle', content: 'We create cutting-edge software solutions that transform businesses and drive innovation in the digital age.' },
    { page: 'home', section: 'hero', contentkey: 'primary_button_text', content: 'Get a Quote' },
    { page: 'home', section: 'hero', contentkey: 'primary_button_url', content: '#contact', contenttype: 'url' },
    { page: 'home', section: 'hero', contentkey: 'secondary_button_text', content: 'Meet Our Team' },
    { page: 'home', section: 'hero', contentkey: 'secondary_button_url', content: '#team', contenttype: 'url' },

    // Services Section
    { page: 'home', section: 'services', contentkey: 'title', content: 'Our' },
    { page: 'home', section: 'services', contentkey: 'title_highlight', content: 'Services' },
    { page: 'home', section: 'services', contentkey: 'subtitle', content: 'From concept to deployment, we provide end-to-end software development services' },

    // Contact Section
    { page: 'home', section: 'contact', contentkey: 'title', content: 'Get in' },
    { page: 'home', section: 'contact', contentkey: 'title_highlight', content: 'Touch' },
    { page: 'home', section: 'contact', contentkey: 'subtitle', content: 'Ready to start your project? Contact us today for a free consultation.' },
    { page: 'home', section: 'contact', contentkey: 'info_title', content: 'Contact Information' },
    { page: 'home', section: 'contact', contentkey: 'email_label', content: 'Email' },
    { page: 'home', section: 'contact', contentkey: 'email', content: '<EMAIL>', contenttype: 'email' },
    { page: 'home', section: 'contact', contentkey: 'phone_label', content: 'Phone' },
    { page: 'home', section: 'contact', contentkey: 'phone', content: '+****************', contenttype: 'phone' },
    { page: 'home', section: 'contact', contentkey: 'location_label', content: 'Location' },
    { page: 'home', section: 'contact', contentkey: 'location', content: 'San Francisco, CA' },

    // CTA Section
    { page: 'home', section: 'cta', contentkey: 'title', content: 'Let\'s Build Something' },
    { page: 'home', section: 'cta', contentkey: 'title_highlight', content: 'Great Together' },
    { page: 'home', section: 'cta', contentkey: 'subtitle', content: 'Ready to transform your ideas into reality? Get in touch and let\'s discuss your next project.' },
    { page: 'home', section: 'cta', contentkey: 'primary_button_text', content: 'Start Your Project' },
    { page: 'home', section: 'cta', contentkey: 'primary_button_url', content: '#contact', contenttype: 'url' },
    { page: 'home', section: 'cta', contentkey: 'secondary_button_text', content: 'View Our Work' },
    { page: 'home', section: 'cta', contentkey: 'secondary_button_url', content: '/projects', contenttype: 'url' },

    // Newsletter Section
    { page: 'home', section: 'newsletter', contentkey: 'title', content: 'Stay in the' },
    { page: 'home', section: 'newsletter', contentkey: 'title_highlight', content: 'Loop' },
    { page: 'home', section: 'newsletter', contentkey: 'subtitle', content: 'Get the latest insights, tips, and updates delivered straight to your inbox. Join thousands of developers and business leaders.' },
    { page: 'home', section: 'newsletter', contentkey: 'input_placeholder', content: 'Enter your email' },
    { page: 'home', section: 'newsletter', contentkey: 'button_text', content: 'Subscribe' },
    { page: 'home', section: 'newsletter', contentkey: 'button_text_success', content: 'Subscribed!' },
    { page: 'home', section: 'newsletter', contentkey: 'privacy_text', content: 'No spam, unsubscribe at any time. We respect your privacy.' },
  ]

  // Services page static content
  const servicesContent = [
    { page: 'services', section: 'hero', contentkey: 'title', content: 'Our' },
    { page: 'services', section: 'hero', contentkey: 'title_highlight', content: 'Services' },
    { page: 'services', section: 'hero', contentkey: 'subtitle', content: 'Comprehensive software development services to help your business thrive in the digital world. From concept to deployment, we\'ve got you covered.' },
    
    { page: 'services', section: 'process', contentkey: 'title', content: 'Our' },
    { page: 'services', section: 'process', contentkey: 'title_highlight', content: 'Process' },
    { page: 'services', section: 'process', contentkey: 'subtitle', content: 'We follow a proven methodology to ensure your project\'s success' },
    
    { page: 'services', section: 'process', contentkey: 'step1_number', content: '01' },
    { page: 'services', section: 'process', contentkey: 'step1_title', content: 'Discovery' },
    { page: 'services', section: 'process', contentkey: 'step1_description', content: 'We understand your business goals, requirements, and technical needs through detailed consultation.' },
    
    { page: 'services', section: 'process', contentkey: 'step2_number', content: '02' },
    { page: 'services', section: 'process', contentkey: 'step2_title', content: 'Planning' },
    { page: 'services', section: 'process', contentkey: 'step2_description', content: 'We create a comprehensive project plan with timelines, milestones, and resource allocation.' },
    
    { page: 'services', section: 'process', contentkey: 'step3_number', content: '03' },
    { page: 'services', section: 'process', contentkey: 'step3_title', content: 'Development' },
    { page: 'services', section: 'process', contentkey: 'step3_description', content: 'Our expert team builds your solution using agile methodology with regular updates and feedback.' },
    
    { page: 'services', section: 'process', contentkey: 'step4_number', content: '04' },
    { page: 'services', section: 'process', contentkey: 'step4_title', content: 'Delivery' },
    { page: 'services', section: 'process', contentkey: 'step4_description', content: 'We deploy your solution and provide ongoing support to ensure optimal performance.' },
  ]

  // About page static content
  const aboutContent = [
    { page: 'about', section: 'hero', contentkey: 'title', content: 'About' },
    { page: 'about', section: 'hero', contentkey: 'title_highlight', content: 'Technoloway' },
    { page: 'about', section: 'hero', contentkey: 'subtitle', content: 'We are a team of passionate developers, designers, and strategists dedicated to creating exceptional digital experiences.' },
  ]

  // Contact page static content
  const contactContent = [
    { page: 'contact', section: 'hero', contentkey: 'title', content: 'Contact' },
    { page: 'contact', section: 'hero', contentkey: 'title_highlight', content: 'Us' },
    { page: 'contact', section: 'hero', contentkey: 'subtitle', content: 'Ready to start your project? Get in touch with our team today.' },
  ]

  // Combine all content
  const allContent = [
    ...homeContent,
    ...aboutContent,
    ...contactContent
  ]

  // Insert static content with display order
  for (let i = 0; i < allContent.length; i++) {
    await prisma.staticcontent.create({
      data: {
        ...allContent[i],
        displayorder: i + 1,
        contenttype: (allContent[i] as any).contenttype || 'text'
      }
    })
  }

  console.log(`✅ Created ${allContent.length} static content entries`)
}

async function main() {
  try {
    await seedStaticContent()
    console.log('🎉 Static content seeding completed successfully!')
  } catch (error) {
    console.error('❌ Error seeding static content:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
