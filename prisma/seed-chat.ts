import { PrismaClient } from '@prisma/client'
import { Faker, en } from '@faker-js/faker'

const prisma = new PrismaClient()
const faker = new Faker({ locale: [en] })

// Utility functions
const randomChoice = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)]
}

const randomChoices = <T>(array: T[], count: number): T[] => {
  const shuffled = [...array].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, Math.min(count, array.length))
}

// Chat-specific seed data
const CHAT_SUBJECTS = [
  'Project Inquiry',
  'Technical Support',
  'Service Question',
  'Billing Question',
  'General Discussion',
  'Feature Request',
  'Bug Report',
  'Meeting Request',
  'Document Review',
  'Project Update',
  'Urgent: Need Help',
  'Quick Question',
  'Support Request',
  'Follow-up Discussion'
]

const ADMIN_MESSAGES = [
  'Thanks for reaching out! I\'d be happy to help you with that.',
  'Great question! Let me provide you with some detailed information.',
  'I understand your concern. Here\'s what I can tell you about this.',
  'That sounds like an interesting project! Let\'s discuss the requirements.',
  'I\'ve reviewed your request and here are my recommendations.',
  'Perfect timing! We actually have some great solutions for this.',
  'I can definitely help you with that. Let me explain the process.',
  'Thanks for the feedback! That\'s a really valuable suggestion.',
  'I\'ve looked into this issue and here\'s what I found.',
  'Let me connect you with the right team member for this.',
  'I\'ll need to gather some more information to help you better.',
  'That\'s a great idea! Let me discuss this with the team.'
]

const CLIENT_MESSAGES = [
  'Hi! I have a question about your web development services. Could you help me understand the process?',
  'Hello, I need some technical support with my current project. Are you available to chat?',
  'Good morning! I\'m interested in your mobile app development services. Can we discuss the details?',
  'Hi there! I have some questions about the billing for my recent project. Could you clarify?',
  'Hello! I\'d like to discuss a potential new project with your team. When would be a good time?',
  'Hi! I have an idea for a new feature that might be useful. Would love to get your thoughts!',
  'That\'s exactly what I was looking for! Thank you for the explanation.',
  'This sounds perfect for our needs. What would be the next steps?',
  'Great! Could you also tell me about the timeline and pricing?',
  'I appreciate the quick response. This helps a lot!',
  'That makes sense. I have a few more questions if you don\'t mind.',
  'Excellent! I\'m excited to move forward with this project.',
  'Thank you for the detailed information. Very helpful!',
  'Perfect! I\'ll discuss this with my team and get back to you.'
]

async function seedChatSystem() {
  console.log('💬 Starting chat system seeding...')

  // Get existing users
  const users = await prisma.users.findMany()
  const adminUsers = users.filter(u => u.role === 'ADMIN')
  const clientUsers = users.filter(u => u.role === 'USER' || u.role === 'CLIENT')
  const allUsers = [...adminUsers, ...clientUsers]

  if (adminUsers.length === 0 || clientUsers.length === 0) {
    console.log('❌ No admin or client users found. Please run the main seed file first.')
    return
  }

  console.log(`Found ${adminUsers.length} admin users and ${clientUsers.length} client users`)

  // Clear existing chat data
  console.log('🗑️ Clearing existing chat data...')
  await prisma.contactforms.deleteMany({
    where: {
      messagetype: {
        in: ['chat', 'reply']
      }
    }
  })

  // Create chat conversations
  console.log('💭 Creating chat conversations...')
  
  const conversations = []
  const numConversations = Math.min(10, clientUsers.length * 2)
  
  for (let i = 0; i < numConversations; i++) {
    const client = randomChoice(clientUsers)
    const admin = randomChoice(adminUsers)
    const threadId = BigInt(Date.now() + i * 1000)
    
    // Create initial message from client
    const initialMessage = await prisma.contactforms.create({
      data: {
        name: `${client.firstname} ${client.lastname || ''}`,
        email: client.email,
        phone: faker.phone.number(),
        subject: randomChoice(CHAT_SUBJECTS),
        message: randomChoice(CLIENT_MESSAGES),
        senderid: client.id,
        receiverid: admin.id,
        threadid: threadId,
        messagetype: 'chat',
        contenttype: 'text',
        isread: faker.datatype.boolean(),
        readat: faker.datatype.boolean() ? faker.date.past() : null,
        status: randomChoice(['New', 'In Progress', 'Resolved']),
        isdelivered: true,
        deliveredat: faker.date.past(),
        createdat: faker.date.past(),
      },
    })
    
    conversations.push({
      thread: initialMessage,
      client,
      admin,
      threadId
    })
  }

  // Create replies for each conversation
  console.log('💬 Creating conversation replies...')
  
  let totalReplies = 0
  for (const conv of conversations) {
    const numReplies = faker.number.int({ min: 1, max: 6 })
    
    for (let i = 0; i < numReplies; i++) {
      const isAdminReply = i % 2 === 0 // Alternate between admin and client
      const sender = isAdminReply ? conv.admin : conv.client
      const receiver = isAdminReply ? conv.client : conv.admin
      const message = isAdminReply ? randomChoice(ADMIN_MESSAGES) : randomChoice(CLIENT_MESSAGES)
      
      await prisma.contactforms.create({
        data: {
          name: isAdminReply ? 'Admin Support' : conv.thread.name,
          email: isAdminReply ? '<EMAIL>' : conv.thread.email,
          phone: conv.thread.phone,
          subject: `Re: ${conv.thread.subject}`,
          message: message,
          parentid: conv.thread.id,
          threadid: conv.threadId,
          senderid: sender.id,
          receiverid: receiver.id,
          messagetype: 'reply',
          contenttype: 'text',
          isread: faker.datatype.boolean(),
          readat: faker.datatype.boolean() ? faker.date.past() : null,
          status: conv.thread.status,
          isdelivered: true,
          deliveredat: faker.date.past(),
          createdat: new Date(conv.thread.createdat.getTime() + (i + 1) * 60000 * 30), // 30 minutes apart
        },
      })
      
      totalReplies++
    }
  }

  // Create messages with attachments
  console.log('📎 Creating messages with attachments...')

  const attachmentMessages = []
  for (let i = 0; i < 3; i++) {
    const sender = randomChoice(allUsers)
    const receiver = randomChoice(allUsers.filter(u => u.id !== sender.id))
    const threadId = BigInt(Date.now() + Math.floor(Math.random() * 10000))

    const attachments = JSON.stringify([
      {
        id: faker.string.uuid(),
        name: `document_${faker.number.int({ min: 1, max: 999 })}.pdf`,
        type: randomChoice(['image/jpeg', 'application/pdf', 'text/plain', 'application/docx']),
        size: faker.number.int({ min: 1024, max: 5242880 }),
        url: faker.internet.url(),
        uploadedAt: new Date().toISOString()
      }
    ])

    const message = await prisma.contactforms.create({
      data: {
        name: `${sender.firstname} ${sender.lastname || ''}`,
        email: sender.email,
        phone: faker.phone.number(),
        subject: 'File Sharing',
        message: 'I\'ve attached the document you requested. Please review and let me know your thoughts.',
        senderid: sender.id,
        receiverid: receiver.id,
        threadid: threadId,
        messagetype: 'chat',
        contenttype: 'text',
        attachments: attachments,
        isread: faker.datatype.boolean(),
        readat: faker.datatype.boolean() ? faker.date.past() : null,
        status: randomChoice(['New', 'In Progress']),
        isdelivered: true,
        deliveredat: faker.date.past(),
        createdat: faker.date.past(),
      },
    })

    attachmentMessages.push(message)
  }

  // Create unread messages for testing notifications
  console.log('🔔 Creating unread messages...')

  const unreadMessages = []
  for (let i = 0; i < 5; i++) {
    const sender = randomChoice(clientUsers)
    const receiver = randomChoice(adminUsers)
    const threadId = BigInt(Date.now() + Math.floor(Math.random() * 10000))

    const message = await prisma.contactforms.create({
      data: {
        name: `${sender.firstname} ${sender.lastname || ''}`,
        email: sender.email,
        phone: faker.phone.number(),
        subject: randomChoice(['Urgent: Need Help', 'Quick Question', 'Support Request']),
        message: randomChoice([
          'Hi! I need some urgent help with my project. Could you please get back to me as soon as possible?',
          'Quick question about the billing. Could you clarify this for me?',
          'I\'m having some technical issues. Could someone help me troubleshoot?'
        ]),
        senderid: sender.id,
        receiverid: receiver.id,
        threadid: threadId,
        messagetype: 'chat',
        contenttype: 'text',
        isread: false,
        readat: null,
        status: 'New',
        isdelivered: true,
        deliveredat: faker.date.recent(),
        createdat: faker.date.recent(),
      },
    })

    unreadMessages.push(message)
  }

  // Create messages with metadata
  console.log('🎨 Creating messages with rich metadata...')

  const metadataMessages = []
  for (let i = 0; i < 3; i++) {
    const sender = randomChoice(allUsers)
    const receiver = randomChoice(allUsers.filter(u => u.id !== sender.id))
    const threadId = BigInt(Date.now() + Math.floor(Math.random() * 10000))

    const metadata = JSON.stringify({
      priority: randomChoice(['low', 'normal', 'high']),
      tags: randomChoices(['urgent', 'follow-up', 'question', 'feedback'], 2),
      readReceipt: faker.datatype.boolean(),
      deliveryStatus: 'delivered',
      messageId: faker.string.uuid(),
      editCount: faker.number.int({ min: 0, max: 3 }),
    })

    const message = await prisma.contactforms.create({
      data: {
        name: `${sender.firstname} ${sender.lastname || ''}`,
        email: sender.email,
        phone: faker.phone.number(),
        subject: randomChoice(['Project Update', 'Important Announcement', 'Meeting Schedule']),
        message: 'This message contains rich metadata for testing advanced chat features.',
        senderid: sender.id,
        receiverid: receiver.id,
        threadid: threadId,
        messagetype: 'chat',
        contenttype: 'text',
        metadata: metadata,
        isread: faker.datatype.boolean(),
        readat: faker.datatype.boolean() ? faker.date.past() : null,
        status: randomChoice(['New', 'In Progress']),
        isdelivered: true,
        deliveredat: faker.date.past(),
        createdat: faker.date.past(),
      },
    })

    metadataMessages.push(message)
  }

  console.log(`✅ Created ${conversations.length} chat conversations with ${totalReplies} replies`)
  console.log(`✅ Created ${attachmentMessages.length} messages with attachments`)
  console.log(`✅ Created ${unreadMessages.length} unread messages`)
  console.log(`✅ Created ${metadataMessages.length} messages with metadata`)
  console.log('🎉 Chat system seeding completed successfully!')
}

async function main() {
  try {
    await seedChatSystem()
  } catch (error) {
    console.error('❌ Error seeding chat system:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

main()
