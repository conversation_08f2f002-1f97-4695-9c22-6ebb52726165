# Database Seed Data Documentation

This document describes the comprehensive seed data available for the Technoloway application, including the chat system implementation.

## Overview

The seed data provides a complete set of realistic test data for all application features, including:
- User accounts with different roles (ADMIN, USER, CLIENT)
- Business entities (clients, projects, services, invoices)
- Chat system with conversations, replies, and attachments
- Content management data (blog posts, testimonials, etc.)

## Seed Files

### 1. `seed.ts` - Main Seed File
The primary seed file that creates all core application data including:

**Users Created:**
- 1 Main Admin: `<EMAIL>` (password: `password123`)
- 2 Additional Admin users
- 4 Regular USER accounts
- 6 CLIENT accounts for chat testing

**Business Data:**
- 15 Clients with realistic company information
- 21 Services across 7 categories
- 12 Projects with technologies and team assignments
- 15 Invoices with payment records
- 8 Contracts and legal documents

**Chat System Data:**
- 8 Traditional contact form submissions
- 6 Chat conversation threads
- Multiple replies per conversation
- 4 Messages with file attachments
- 3 Rich content messages with metadata
- 5 Unread messages for notification testing

### 2. `seed-chat.ts` - Chat System Focused Seed
A specialized seed file for testing chat functionality:

**Features:**
- Creates realistic chat conversations between users
- Alternating message patterns (client → admin → client)
- Various message types (chat, reply, contact)
- Attachment handling with file metadata
- Unread message scenarios
- Rich metadata for advanced features

## Running Seed Data

### Available Commands

```bash
# Run main seed data only
npm run db:seed

# Run chat system seed data only (requires existing users)
npm run db:seed:chat

# Run both main and chat seed data
npm run db:seed:all

# Reset database and run main seed
npm run db:reset

# Reset database and run all seed data
npm run db:reset:full
```

### Prerequisites

1. **Database Setup**: Ensure PostgreSQL is running and configured
2. **Environment Variables**: Set up `.env` with database connection
3. **Migrations**: Run `npm run db:migrate` to apply schema changes

### Recommended Workflow

```bash
# 1. Reset and seed everything (fresh start)
npm run db:reset:full

# 2. Or step by step
npm run db:reset        # Reset with main data
npm run db:seed:chat    # Add chat data
```

## Test User Accounts

All users have the password: `password123`

### Admin Login
- **URL**: `/auth/signin`
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: ADMIN

### Additional Admin Accounts
- 2 additional admin accounts with faker-generated emails
- All use the same password: `password123`

### Client Accounts
- **URL**: `/client-auth/signin`
- 6 CLIENT role accounts for testing chat functionality
- 4 USER role accounts for general testing
- All use the same password: `password123`

## Chat System Test Scenarios

### 1. Active Conversations
- Multiple ongoing conversations between clients and admins
- Realistic message flow with proper threading
- Read/unread status variations

### 2. Message Types
- **Contact Forms**: Traditional contact submissions
- **Chat Messages**: Real-time conversation starters
- **Replies**: Threaded responses to existing messages
- **Attachments**: Messages with file uploads

### 3. Notification Testing
- Unread messages for testing notification systems
- Recent messages for real-time update testing
- Various priority levels and metadata

### 4. Access Control Testing
- Client-to-admin conversations
- Admin-to-client responses
- User role-based message filtering

## Database Schema Integration

### Chat System Fields
The `contactforms` table includes extended fields for chat functionality:

```sql
-- Core chat fields
parentid     BIGINT     -- Threading support
threadid     BIGINT     -- Conversation grouping
senderid     BIGINT     -- Message sender (users.id)
receiverid   BIGINT     -- Message recipient (users.id)
messagetype  STRING     -- 'contact', 'reply', 'chat'
contenttype  STRING     -- 'text', 'html', 'file'

-- Advanced features
attachments  STRING     -- JSON array of file attachments
metadata     STRING     -- JSON for additional data
edithistory  STRING     -- JSON array of edit records
isdelivered  BOOLEAN    -- Delivery confirmation
deliveredat  TIMESTAMP  -- Delivery timestamp
```

### Relationships
- `sender` → `users` table (who sent the message)
- `receiver` → `users` table (who receives the message)
- `parent` → `contactforms` table (for threading)
- `replies` → `contactforms[]` table (child messages)

## Testing Recommendations

### 1. Authentication Testing
- Log in with different user roles
- Test role-based access to chat features
- Verify user permissions for messaging

### 2. Chat Functionality Testing
- Send new messages between users
- Reply to existing conversations
- Test file attachment uploads
- Verify real-time updates

### 3. Admin Interface Testing
- View all conversations in admin panel
- Test message management features
- Verify notification systems
- Test search and filtering

### 4. Client Dashboard Testing
- Access client-specific conversations
- Test message sending from client side
- Verify conversation history
- Test responsive design

## Data Relationships

The seed data maintains proper foreign key relationships:
- Users ↔ Clients (via userid)
- Users ↔ Messages (via senderid/receiverid)
- Projects ↔ Clients ↔ Invoices
- Messages ↔ Threads (via threadid)
- Messages ↔ Replies (via parentid)

## Customization

### Adding More Chat Data
To add additional chat scenarios, modify `seed-chat.ts`:

```typescript
// Add more conversation types
const CUSTOM_SUBJECTS = ['Your Custom Subject']

// Add more message templates
const CUSTOM_MESSAGES = ['Your custom message template']

// Adjust conversation counts
const numConversations = 20 // Increase as needed
```

### Modifying User Roles
Update the user creation section in `seed.ts` to adjust role distribution:

```typescript
// Adjust role distribution
role: randomChoice(['ADMIN', 'USER', 'CLIENT'])
```

## Troubleshooting

### Common Issues

1. **Foreign Key Errors**: Ensure main seed runs before chat seed
2. **Duplicate Data**: Use reset commands to clear existing data
3. **Permission Errors**: Check database user permissions
4. **Memory Issues**: Reduce seed data counts for large datasets

### Verification Queries

```sql
-- Check user distribution
SELECT role, COUNT(*) FROM users GROUP BY role;

-- Check message types
SELECT messagetype, COUNT(*) FROM contactforms GROUP BY messagetype;

-- Check conversation threads
SELECT threadid, COUNT(*) FROM contactforms WHERE threadid IS NOT NULL GROUP BY threadid;

-- Check unread messages
SELECT COUNT(*) FROM contactforms WHERE isread = false;
```

## Support

For issues with seed data:
1. Check database connection and permissions
2. Verify all migrations are applied
3. Review console output for specific errors
4. Use verification queries to check data integrity
