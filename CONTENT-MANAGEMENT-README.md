# Content Management System

## Overview

The Content Management System is a comprehensive admin interface that automatically detects and manages all static content across public pages. It provides an intuitive way to edit text content, hero slides, and manage sections for all public pages in the application.

## Features

### 🎯 **Automatic Page Detection**
- Automatically detects all public pages (Home, About, Services, Contact, Projects, Team, Blog)
- Identifies sections within each page (Hero, Content, Stats, Features, Process, CTA, Contact, Form, Grid, List)
- No manual configuration required - the system scans the application structure

### 📝 **Comprehensive Content Editing**
- **Inline Field Editing**: Edit text fields directly in the interface
- **Hero Slide Management**: Add, remove, reorder, and edit hero carousel slides
- **Section Management**: Toggle sections on/off and manage display order
- **Real-time Preview**: Preview hero carousels and content changes

### 🎨 **User-Friendly Interface**
- **Sidebar Navigation**: Easy page and section selection
- **Visual Indicators**: Icons for different page types and section types
- **Status Indicators**: Active/inactive status for sections and slides
- **Change Tracking**: Visual indicators for unsaved changes

### 🔄 **Advanced Functionality**
- **Auto-save Detection**: Tracks changes and enables save button
- **Bulk Operations**: Update multiple fields and slides at once
- **Error Handling**: Comprehensive error handling with retry functionality
- **Audit Logging**: All changes are logged for security and compliance

## Page Structure

### Detected Pages

| Page | Path | Description | Sections |
|------|------|-------------|----------|
| **Home** | `/` | Main landing page | Hero, Client Logos, Services, Projects, Testimonials, Technologies, Team, Blog, Pricing, Newsletter, CTA, Contact |
| **About** | `/about` | Company information | Hero, Stats, Mission, Values, Timeline |
| **Services** | `/services` | Service offerings | Hero, Services Grid, Process, CTA |
| **Contact** | `/contact` | Contact information | Hero, Contact Info, Contact Form |
| **Projects** | `/projects` | Portfolio showcase | Hero, Projects Grid |
| **Team** | `/team` | Team members | Hero, Team Grid |
| **Blog** | `/blog` | Blog articles | Hero, Blog List |

### Section Types

| Type | Icon | Description | Features |
|------|------|-------------|----------|
| **Hero** | 📷 | Hero carousel with slides | Slide management, image URLs, button configuration |
| **Content** | 📄 | Text content sections | Title, subtitle, description fields |
| **Stats** | ⚙️ | Statistics and metrics | Number displays, labels |
| **Features** | 💬 | Feature highlights | Feature lists, descriptions |
| **Process** | ⚙️ | Process steps | Step-by-step workflows |
| **CTA** | 🌐 | Call-to-action sections | Button text, URLs, descriptions |
| **Contact** | 📞 | Contact information | Address, phone, email, hours |
| **Form** | 📄 | Form sections | Form configurations |
| **Grid** | ⊞ | Grid layouts | Grid content management |
| **List** | 📋 | List layouts | List content management |

## Usage Guide

### Accessing the Content Editor

1. Navigate to `/admin/content-editor`
2. Ensure you're logged in as an admin user
3. The system will automatically load all detected pages and sections

### Editing Content

#### 1. **Select a Page**
- Click on any page in the left sidebar
- Pages are organized with intuitive icons
- The page will expand to show all available sections

#### 2. **Select a Section**
- Click on any section under the selected page
- Sections are categorized by type with appropriate icons
- The main content area will display the section's editable fields

#### 3. **Edit Fields**
- **Text Fields**: Click on any text field to edit inline
- **Hero Slides**: For hero sections, manage individual slides
  - Edit slide title, subtitle, button text, and URL
  - Add new slides with the "Add Slide" button
  - Remove slides with the trash icon
  - Reorder slides with up/down arrows

#### 4. **Preview Changes**
- Toggle "Show Preview" to see hero carousel previews
- Changes are reflected in real-time
- Preview mode shows how the content will appear on the frontend

#### 5. **Save Changes**
- Click "Save Changes" to persist all modifications
- The system will update the database and log the changes
- Success/error messages will be displayed

### Hero Slide Management

#### Adding a New Slide
1. Select a hero section
2. Click "Add Slide" button
3. Fill in the slide details:
   - **Title**: Main headline
   - **Subtitle**: Supporting text
   - **Button Text**: Call-to-action text
   - **Button URL**: Link destination
   - **Image URL**: Background image path

#### Editing Slides
- **Reorder**: Use up/down arrows to change slide order
- **Toggle Active**: Slides can be activated/deactivated
- **Delete**: Remove slides with the trash icon
- **Edit Content**: Modify any slide field inline

#### Slide Properties
- **ID**: Unique identifier (auto-generated)
- **Title**: Main slide headline
- **Subtitle**: Supporting text
- **Button Text**: Call-to-action text
- **Button URL**: Link destination
- **Image URL**: Background image path
- **Display Order**: Position in carousel
- **Is Active**: Whether slide is visible

## API Endpoints

### GET `/api/content`
Fetches all content with page detection integration.

**Response:**
```json
{
  "success": true,
  "data": {
    "pages": [
      {
        "id": "home",
        "name": "Home",
        "slug": "home",
        "path": "/",
        "title": "Home Page",
        "sections": [...]
      }
    ],
    "lastUpdated": "2024-01-01T00:00:00.000Z",
    "totalPages": 7,
    "totalSections": 35
  }
}
```

### PUT `/api/content`
Updates content with field and slide modifications.

**Request Body:**
```json
{
  "content": {
    "pages": [...]
  },
  "updates": [
    {
      "pageId": "home",
      "sectionId": "home-hero",
      "fieldKey": "sectionTitle",
      "value": "New Title"
    }
  ],
  "slideUpdates": [
    {
      "pageId": "home",
      "sectionId": "home-hero",
      "slideId": "slide-1",
      "slide": {
        "title": "Updated Title",
        "subtitle": "Updated Subtitle"
      }
    }
  ]
}
```

### POST `/api/content`
Creates new content entries.

### DELETE `/api/content`
Deletes specific content entries.

## Database Schema

### StaticContent Table
```sql
CREATE TABLE staticcontent (
  id INTEGER PRIMARY KEY,
  page VARCHAR NOT NULL,
  section VARCHAR NOT NULL,
  contentkey VARCHAR NOT NULL,
  contenttype VARCHAR NOT NULL,
  content TEXT NOT NULL,
  displayorder INTEGER DEFAULT 1,
  isactive BOOLEAN DEFAULT true,
  createdat TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedat TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Content Types
- **text**: Regular text content
- **hero_slide**: JSON-encoded hero slide data

## Security Features

### Authentication
- Admin-only access required
- Session-based authentication
- Role-based permissions

### Audit Logging
- All content changes are logged
- User tracking and IP logging
- Timestamp and action details

### Data Validation
- Input sanitization
- Content type validation
- SQL injection prevention

## Performance Optimizations

### Caching
- Static content caching
- Database query optimization
- Efficient data loading

### Lazy Loading
- Content loaded on demand
- Progressive enhancement
- Minimal initial payload

## Troubleshooting

### Common Issues

#### 1. **Content Not Loading**
- Check authentication status
- Verify admin permissions
- Check browser console for errors

#### 2. **Changes Not Saving**
- Ensure you're logged in as admin
- Check network connectivity
- Verify database permissions

#### 3. **Hero Slides Not Displaying**
- Check image URLs are valid
- Verify slide is marked as active
- Check display order settings

#### 4. **Page Detection Issues**
- Ensure page files exist in correct locations
- Check page detector configuration
- Verify import paths

### Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| "Unauthorized" | Not logged in or not admin | Log in with admin account |
| "Content not found" | Database entry missing | Refresh page or recreate content |
| "Invalid content structure" | Malformed data | Check input format |
| "Failed to save" | Database error | Check database connection |

## Development

### Adding New Pages

1. Create the page file in `src/app/[page-name]/page.tsx`
2. Add page configuration to `src/lib/page-detector.ts`
3. Define sections and their fields
4. Update the page detection system

### Adding New Section Types

1. Define the section type in `PageSection` interface
2. Add icon mapping in `getSectionIcon` function
3. Update the content editor to handle the new type
4. Add appropriate field editing components

### Customizing Field Types

1. Extend the field editing interface
2. Add validation rules
3. Implement custom input components
4. Update the save/load logic

## Future Enhancements

### Planned Features
- **Image Upload**: Direct image upload for hero slides
- **Rich Text Editor**: WYSIWYG editing for content
- **Version Control**: Content versioning and rollback
- **Bulk Import/Export**: CSV/JSON import/export
- **Advanced Preview**: Full page preview mode
- **Content Scheduling**: Publish content at specific times
- **Multi-language Support**: Internationalization
- **Content Analytics**: Usage tracking and insights

### Technical Improvements
- **Real-time Collaboration**: Multiple admin editing
- **Advanced Caching**: Redis-based caching
- **API Rate Limiting**: Request throttling
- **Webhook Integration**: External system notifications
- **Backup System**: Automated content backups

## Support

For technical support or feature requests:
- Check the troubleshooting section
- Review the API documentation
- Contact the development team
- Submit issues through the project repository

---

**Last Updated**: January 2024
**Version**: 2.0.0
**Compatibility**: Next.js 14+, React 18+, TypeScript 5+ 